import { defineConfig, loadEnv } from 'vite';
import postcsspxtoviewport from 'postcss-px-to-viewport-8-plugin';

const { resolve } = require('path');
import createVitePlugins from './vite/plugins';
// https://vitejs.dev/config/
export default ({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_BASE } = env;
  return defineConfig({
    base: VITE_APP_BASE,
    server: {
      port: 2888,
      proxy: {
        '/api': {
          // target: 'https://jd.coiot.net',
          //  target: "http://localhost:8009",
          target: 'https://8d739922521f.ngrok-free.app',
          //  target: 'http://dengxia.natapp1.cc',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, ''),
        },
      },
    },
    resolve: {
      alias: {
        '~': resolve(__dirname, './'),
        '@': resolve(__dirname, './src'),
        components: resolve(__dirname, './src/components'),
        styles: resolve(__dirname, './src/styles'),
        utils: resolve(__dirname, './src/utils'),
      },
    },
    plugins: createVitePlugins(env, command === 'build'),
    css: {
      postcss: {
        plugins: [
          postcsspxtoviewport({
            unitToConvert: 'npx', // 要转化的单位
            viewportWidth: 1920, // UI设计稿的宽度
            viewportHeight: 1080, // UI设计稿的宽度
            propList: ['*'], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
            viewportUnit: 'vw', // 指定需要转换成的视窗单位，默认vw
            fontViewportUnit: 'vw', // 指定字体需要转换成的视窗单位，默认vw
            selectorBlackList: ['ignore-'], // 指定不转换为视窗单位的类名，
            minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
            mediaQuery: true, // 是否在媒体查询的css代码中也进行转换，默认false
            replace: true, // 是否转换后直接更换属性值
            //exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
            // exclude: [],
            landscape: false, // 是否处理横屏情况
          }),
        ],
      },
    },
    define: {
      'process.env': {},
    },
  });
};
