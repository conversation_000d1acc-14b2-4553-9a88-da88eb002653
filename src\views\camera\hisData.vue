<template>
  <basic-container style="overflow: auto;">
    <el-form ref="submitForm" :inline="true" :model="queryForm" label-width="80px">
      <!--      <el-form-item label="设备名称" prop="devicesn"  style="width: 240px;">
              <el-select v-model="queryForm.devicesn"
                         :filterable="true"
                         @change="changeDevice"
                         placeholder="请选择设备名称">
                <el-option
                    v-for="item in devEquList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>-->
      <!--      <el-form-item label="开始时间" prop="startTime"  style="width: 260px;">
              <avue-date
                  type="datetime"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  v-model="queryForm.startTime"
                  placeholder="请输入开始时间"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="endTime" style="width: 260px;">
              <avue-date
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                v-model="queryForm.endTime"
                placeholder="请输入结束时间"
              />

            </el-form-item>-->
      <!--      <el-form-item label="同步时间">
              <el-input v-model="input.time" placeholder="同步时间 " :disabled="true"  style="width: 160px;"></el-input>
            </el-form-item>-->
      <!--      <el-form-item>
              <el-button type="primary" @click="handleQuery">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary"
              @click="openDialog" >导出数据</el-button>
            </el-form-item>-->
    </el-form>
    <el-dialog
        v-model="dialogVisible"
        title="提示"
        width="500"
    >
      <div style="font-size: 15px;margin-left: 6px;">
        确认导出当前搜索到的数据？
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleGet">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- <el-row> -->
    <el-col :span="2" class="hisData-left" v-show=false>
      <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
      >全选
      </el-checkbox>
      <el-checkbox-group
          class="hisData-group"
          style="height: 90%; overflow-y: auto; overflow-x: hidden"
          v-model="checkedDatas"
          @change="handleCheckedDatasChange"
      >
        <el-checkbox
            style="width: 100%"
            v-for="item in dataOptions"
            :key="item.dictKey"
            :label="item.dictKey"
        >{{ item.dictValue }}
        </el-checkbox>
      </el-checkbox-group>
    </el-col>
    <!-- <el-col :span="22"  > -->
    <div style="width:100%;height:768px;">

      <div
          class="wel-container-right-bottom animate__animated animate__bounceIn"
          style="background-color: transparent"
      >
        <div class="wel-container-right-total" style="width: 100%;" id="ibmsLineOptions"></div>
      </div>
    </div>
    <!-- </el-col> -->
    <!-- </el-row> -->
  </basic-container>
</template>

<script>
import * as echarts from "echarts";
import {getDictionary} from "@/api/system/dictbiz";
import {getDevData, getDeviceSynchronizeByNumber} from "@/api/device/card";
import {getBatterList} from "@/api/battery/equipment";
import {ibmsLineOptions} from "../battery/ibms_echats";
import {validatenull} from "utils/validate";
import {markRaw} from "vue";

export default {
  data() {
    return {
      input: {},
      loading: true,
      form: {},
      queryForm: {},
      ibmsLineOptions: null,
      page: {
        pageSize: 100,
        currentPage: 1,
        total: 0,
      },
      data: [],
      equipData: [],
      // 设备
      devEquList: [],
      // 点位
      dataOptions: [],
      checkAll: false,
      isIndeterminate: true,
      hisChart: null,
      checkedDatas: [],
      query: [],
      count: 0,
      dialogVisible: false,
      initTimer: null,
      intervalDays: null,
    };
  },
  props: {
    dataTime: {
      type: Object,
      default: {},
    },
  },
  watch: {
    isContainer: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.initDensityEchats(this.data);
          });
        }
      },
      immediate: true,
    },
    dataTime(data) {

      let params = {
        devicesn: data.devicesn,
        startTime: data.startTime,
        endTime: data.endTime
      }
      this.onLoad(this.page, params)
    }
  },
  async created() {
  },
  mounted() {
    this.init();
  },
  beforeUnmount() {
    // 销毁ECharts实例
    if (this.ibmsLineOptions) {
      this.ibmsLineOptions.dispose();
      this.ibmsLineOptions = null;
    }

    // 移除resize事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
      this.resizeHandler = null;
    }

    // 清理数据引用
    this.data = [];
    this.equipData = [];
    this.devEquList = [];
    this.dataOptions = [];
    this.checkedDatas = [];
    this.query = [];

    // 清理查询表单
    this.queryForm = {};
    this.input = {};

    // 移除全局resize事件监听器
    if (window.onresize) {
      window.onresize = null;
    }

    // 清理setTimeout引用
    if (this.initTimer) {
      clearTimeout(this.initTimer);
      this.initTimer = null;
    }
  },
  methods: {
    /**
     * 初始化
     **/
    init() {
      this.$nextTick(() => {
        // 获取设备
        /*   getBatterList().then((res) => {
             var data = res.data.data;
             data.forEach(item => {
               this.devEquList.push({label: item.equNo, value: item.equNo});
             });
           });*/
        // 获取字典列表
        getDictionary({code: "equ_point"}).then((res) => {

          const data = res.data.data;
          this.checkedDatas = data.map((p) => p.dictKey)
          this.dataOptions = data;
        });
        // 获取设备
        var deviceSn = localStorage.getItem('devicesn');


        if (deviceSn) {
          this.queryForm.devicesn = deviceSn;
          this.query.startTime = this.dataTime.startTime;
          this.queryForm.startTime = this.dataTime.startTime;
          this.query.endTime = this.dataTime.endTime;
          this.queryForm.endTime = this.dataTime.endTime;

          // 保存setTimeout引用以便后续清理
          this.initTimer = setTimeout(() => {
            this.handleQuery();
          }, 600)

        }
      });

    },
    /**
     * 选择时间
     **/
    disabledDate(time) {
      if (!validatenull(this.queryForm.startTime)) {
        var startTime = new Date(this.queryForm.startTime).getTime();
        const day = 7 * 60 * 60 * 24 * 1000;
        return (
            time.getTime() > startTime + day ||
            time.getTime() < startTime
        );
      } else {
        return time.getTime() > Date.now()
      }
    },
    /**
     * 查询
     **/
    handleQuery() {
      this.refreshChange();
      /*    let devicesn = this.queryForm.devicesn
          getDeviceSynchronizeByNumber(devicesn).then(res => {
            if ( this.input.time==="") {
              this.input.time = res.data.data.onlineTime
            }
          })*/
    },
    handleCheckAllChange(val) {
      this.checkedDatas = val ? this.dataOptions.map((p) => p.dictKey) : [];
      this.isIndeterminate = false;
      this.refreshChange();
    },
    handleCheckedDatasChange(val) {
      const checkedCount = val.length;
      this.checkAll = checkedCount === this.dataOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.dataOptions.length;
      this.refreshChange();
    },
    refreshChange() {
      this.onLoad(this.page, this.queryForm);
    },
    openDialog() {
      this.dialogVisible = true;
    },
    async handleGet() {
      //关闭弹窗
      this.dialogVisible = false;
      if (!this.equipData || this.equipData.length === 0) {
        return this.$message.warning('没有数据可导出');
      }

      let blobUrl = null;
      try {
        const XLSX = await import('xlsx');
        const {saveAs} = await import('file-saver');

        // 定义列配置
        const columns = [
          {label: "电池状态", prop: "eleStatusName"},
          {label: "电池编号", prop: "devicesn"},
          {label: "电流", prop: "eleFlowStr"},
          {label: "剩余电量(%)", prop: "resiEle"},
          {label: "第1节电芯电压(V)", prop: "batterVol1Str"},
          {label: "第2节电芯电压(V)", prop: "batterVol2Str"},
          {label: "第3节电芯电压(V)", prop: "batterVol3Str"},
          {label: "第4节电芯电压(V)", prop: "batterVol4Str"},
          {label: "第5节电芯电压(V)", prop: "batterVol5Str"},
          {label: "第6节电芯电压(V)", prop: "batterVol6Str"},
          {label: "第7节电芯电压(V)", prop: "batterVol7Str"},
          {label: "第8节电芯电压(V)", prop: "batterVol8Str"},
          {label: "电池总电压(V)", prop: "batterVolAllStr"},
          {label: "电芯1温度(℃)", prop: "temperature1Str"},
          {label: "电芯2温度(℃)", prop: "temperature2Str"},
          {label: "电芯3温度(℃)", prop: "temperature3Str"},
          {label: "电芯4温度(℃)", prop: "temperature4Str"},
          {label: "充放电MOS温度(℃)", prop: "temperature5Str"},
          {label: "加热片温度(℃)", prop: "temperature6Str"},
          {label: "MCU温度(℃)", prop: "temperature7Str"},
          {label: "时间", prop: "time"},
          {label: "循环放电次数", prop: "sumCycEle"}
        ];

        // 准备数据
        const headers = columns.map(item => item.label);
        const data = this.equipData.map(row =>
            columns.map(col => row[col.prop])
        );

        // 创建工作簿和工作表
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);

        // 添加工作表到工作簿并导出
        XLSX.utils.book_append_sheet(wb, ws, '电池数据');
        const excelBuffer = XLSX.write(wb, {
          bookType: 'xlsx',
          type: 'array'
        });

        // 创建Blob并保存文件
        const blob = new Blob([excelBuffer], {type: 'application/octet-stream'});
        blobUrl = URL.createObjectURL(blob);

        saveAs(blob, `电池数据_${new Date().toISOString().slice(0, 10)}_${this.equipData[0].devicesn}.xlsx`);

        this.$message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败，请重试');
      } finally {
        // 清理Blob URL
        if (blobUrl) {
          URL.revokeObjectURL(blobUrl);
        }
      }
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]));
    },
    calculateInterval(startTime, endTime) {
      // 验证输入
      if (!startTime || !endTime) {
        alert('请选择开始时间和结束时间');
        return;
      }

      // 将datetime-local字符串转换为Date对象
      const start = new Date(startTime);
      const end = new Date(endTime);

      // 验证时间有效性
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        alert('时间格式错误，请重新选择');
        return;
      }

      // 确保开始时间小于结束时间
      if (start > end) {
        alert('开始时间不能晚于结束时间');
        return;
      }

      // 计算时间差（毫秒）
      const timeDiff = end.getTime() - start.getTime();

      // 转换为天数（1天 = 86400000毫秒）
      this.intervalDays = Math.ceil(timeDiff / 86400000);
    },
    onLoad(page, params = {}) {
      if (validatenull(params.devicesn)) {
        return this.$message.warning('请选择设备');
      }
      if (validatenull(params.startTime)) {
        return this.$message.warning('请选择开始时间');
      }
      if (validatenull(params.endTime)) {
        return this.$message.warning('请选择结束时间');
      }

      let loading = null;
      try {
        loading = this.$loading({
          lock: true,
          text: "努力加载中...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.5)",
          target: document.querySelector(".wel-container-right-total")  // 替换成你的类名
        });
      } catch (error) {
        console.error('创建loading失败:', error);
      }

      let doms = document.getElementById("ibmsLineOptions");
      if (doms) {
        if (!this.ibmsLineOptions) {
          //echarts自适应
          let myChart = echarts.init(doms);

          // 使用addEventListener而不是覆盖window.onresize
          const resizeHandler = () => {
            myChart.resize();
          };
          window.addEventListener('resize', resizeHandler);

          // 保存引用以便后续清理
          this.resizeHandler = resizeHandler;
          this.ibmsLineOptions = markRaw(myChart);
        }
      }
      this.calculateInterval(params.startTime, params.endTime);
      if (this.intervalDays > 60) {
        this.$notify({
          title: '警告',
          message: '数据量较大，加载较慢，如遇到数据无法显示，建议缩短搜索时间再次进行搜索！',
          type: 'warning'
        });
      }
      getDevData({
        devicesn: params.devicesn,
        startTime: params.startTime,
        endTime: params.endTime,
      })
          .then((res) => {
            const data = res.data.data;
            if (Array.isArray(data) && data.length > 0) {
              this.input.time = data[data.length - 1].time
            } else {
              getDeviceSynchronizeByNumber(this.queryForm.devicesn).then(res => {
                this.input.time = res.data.data.onlineTime
              })
            }
            this.equipData = data;
            // 检查是否有上报数据
            if (validatenull(data)) {
              if (loading) loading.close();
              this.$message.warning('未找到上报数据');
              return;
            }
            let xAxis = data.map((item) => item.time);

            let dataOptionsObj = {};
            this.dataOptions.map((item) => {
              dataOptionsObj[item.dictKey] = item.dictValue;
            });
            let legend = this.checkedDatas.map((item) => dataOptionsObj[item]);
            let series = [];
            this.checkedDatas.map((objs) => {
              const result = data.map((item) => item[objs]);

              series.push({
                name: dataOptionsObj[objs],
                type: "line",
                data: result,
              });

            });
            this.ibmsLineOptions.setOption(ibmsLineOptions(xAxis, legend, series), true);
            if (loading) loading.close();
          })
          .catch((err) => {
            console.error('获取数据失败:', err);
            if (loading) loading.close();
          });
    },
    /**
     * 变动
     **/
    changeDevice(val) {
      localStorage.setItem('devicesn', val);
    },
  },
};
</script>
<style lang="scss" scoped>
#ibmsLineOptions {
  width: 100%;
  flex: 1;
}

#densityEchats {
  width: 100%;
  flex: 1;
}

:global(#avue-view) {
  margin-bottom: 0px;
}

.el-date-table td.disabled .el-date-table-cell {
  background-color: grey;
}

@mixin normalStyle() {
  width: 100%;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.08);
  border-radius: 6 npx 6 npx 6 npx 6 npx;
  background-color: #ffffff;
  padding: 0px 0 npx;
}

.wel-container {
  position: relative;

  &-tabs-postion {
    position: absolute;
    top: 40 npx;
    left: 15 npx;
    z-index: 1;
  }

  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #2a357a;
    margin: 15px 0px;
    position: relative;
    padding-left: 12 npx;
  }

  &-title::after {
    content: " ";
    width: 5 npx;
    height: 21 npx;
    display: block;
    background: #07b667;
    border-radius: 3px 3px 3px 3px;
    position: absolute;
    top: 2 npx;
    left: 0px;
  }

  .welMap {
    width: 100%;
    height: 100%;
  }

  &-left,
  &-right {
    position: absolute;
    top: 10 npx;
    height: 100%;
  }

  &-left {
    left: 10 npx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 96%;
    top: 2%;

    &-top {
      height: 24%;
      @include normalStyle();
      display: flex;
      flex-wrap: wrap;

      &-items:nth-child(1),
      &-items:nth-child(2) {
        border-bottom: 1px solid #e5e6eb;
      }

      &-items {
        display: flex;
        width: 50%;
        height: 50%;
        align-items: center;
        justify-content: left;

        img {
          width: 40 npx;
          height: 40 npx;
          margin-right: 20 npx;
        }

        &-block {
          div:nth-child(1) {
            font-size: 14 npx;
            color: #333333;
            margin-bottom: 2 npx;
          }

          div:nth-child(2) {
            font-size: 24 npx;
            color: #333333;
          }
        }
      }
    }

    &-center {
      height: 36%;
      @include normalStyle();
      display: flex;
      flex-direction: column;
    }

    &-bottom {
      height: 36%;
      @include normalStyle();
      display: flex;
      flex-direction: column;
      position: relative;

      :deep(.wel-tabs) {
        margin-bottom: 0px;
      }
    }
  }

  &-right {
    right: 10 npx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 96%;
    top: 2%;

    &-top {
      height: 49%;
      @include normalStyle();
      display: flex;
      flex-direction: column;
      position: relative;

      &-img {
      }
    }

    &-bottom {
      height: 96%;
      display: flex;
      flex-direction: column;
      @include normalStyle();
    }
  }
}

:deep(.el-form-item__label) {
  color: #ffffff;
}

:deep(.el-input__inner) {
  color: #ffffff;
}

.hisData-left {
  // height: 98%;
  // overflow: hidden;
}

.hisData-group {
  // height: 90%;
  // overflow-y: auto;
  // overflow-x: hidden;

  .el-checkbox {
    color: #ffffff;
  }
}
</style>
