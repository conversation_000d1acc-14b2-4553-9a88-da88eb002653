<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta http-equiv="X-UA-Compatible" content="chrome=1" />
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="format-detection" content="telephone=no">
  <link rel="icon" href="/img/logo3.png" />
  <link rel="stylesheet" href="/iconfont/index.css">
  <link rel="stylesheet" href="/iconfont/avue/iconfont.css">
  <link rel="stylesheet" href="/iconfont/saber/iconfont.css">
  <link rel="stylesheet" href="/css/loading.css">
</head>
<script>
  window._AMapSecurityConfig = {
    securityJsCode: 'd9d4395939bf07d719a719ad4f7ce8a5',
  }
</script>
<!-- 导入需要的包 （一定要放到index.html中的head标签里）-->
<!--
<script src="https://cdn.staticfile.org/FileSaver.js/2014-11-29/FileSaver.min.js"></script>
<script src="https://cdn.staticfile.org/xlsx/0.18.2/xlsx.full.min.js"></script>
-->

<body>
  <div id="app">
    <div class="loading">
      <div class="loading-wrap">
        <div class="loading-dots">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
        <div class="loading-title">
          系统加载中
        </div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>
