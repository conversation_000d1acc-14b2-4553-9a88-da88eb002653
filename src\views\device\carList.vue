<template>
  <basic-container>
    <avue-crud
      style="width: 100%"
      :option="option"
      v-model:search="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="warning"
          size="small"
          plain
          icon="el-icon-download"
          @click="handleExport"
          >导 出
        </el-button>
      </template>
      <template #menu="{ size, row, index }">
        <el-button
          @click.stop="handleView(row)"
          icon="el-icon-view"
          text
          type="primary"
          :size="size"
          >查看</el-button
        >
        <el-button
          @click.stop="handleTrajectory(row)"
          icon="el-icon-position"
          text
          type="primary"
          :size="size"
          >轨迹</el-button
        >
      </template>
      <template #eleStatus="scope">
        {{ status[scope.row.eleStatus] }}
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getlastDevPage as getList } from "@/api/device/card";
import { mapGetters } from "vuex";

export default {
  name: "wel",
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        menu: true,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 2,
        border: false,
        index: true,
        searchBtn: true,
        viewBtn: false,
        emptyBtn: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        selection: false,
        dialogClickModal: false,
        labelWidth: 140,
        indexLabel: "序号",
        indexWidth: 60,
        size: "mini",
        searchSize: "mini",
        columnBtn: true,
        align: "center",
        column: [
          {
            label: "设备编号",
            prop: "devicesn",
            search: true,
            searchSpan: 4,
            minWidth: 110,
          },
          // {
          //   label: "电池编号",
          //   prop: "batterNo",
          //   minWidth: 110,
          // },
          {
            label: "同步时间",
            prop: "time",
            minWidth: 180,
          },
          {
            label: "电流",
            prop: "eleFlow",
            minWidth: 110,
          },
          {
            label: "电池状态",
            prop: "eleStatus",
            minWidth: 110,
            slot: true,
            formslot: true,
          },
          // {
          //   label: "预计使用时间",
          //   prop: "estTime",
          //   minWidth: 110,
          // },
          {
            label: "剩余电量",
            prop: "resiEle",
            minWidth: 110,
          },
          {
            label: "软件版本",
            prop: "softVer",
            minWidth: 110,
          },
          {
            label: "累计循环放电次数",
            prop: "sumCycEle",
            minWidth: 140,
          },
          {
            label: "温度",
            prop: "temperature1",
            minWidth: 110,
          },
          //   {
          //     label: "温度2",
          //     prop: "temperature2",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "温度3",
          //     prop: "temperature3",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "温度4",
          //     prop: "temperature4",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "温度5",
          //     prop: "temperature5",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "温度6",
          //     prop: "temperature6",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "温度7",
          //     prop: "temperature7",
          //     minWidth: 110,
          //   },
          {
            label: "电池电压",
            prop: "batterVol1",
            minWidth: 110,
          },
          //   {
          //     label: "电池电压2",
          //     prop: "batterVol2",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "电池电压3",
          //     prop: "batterVol3",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "电池电压4",
          //     prop: "batterVol4",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "电池电压5",
          //     prop: "batterVol5",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "电池电压6",
          //     prop: "batterVol6",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "电池电压7",
          //     prop: "batterVol7",
          //     minWidth: 110,
          //   },
          //   {
          //     label: "电池电压8",
          //     prop: "batterVol8",
          //     minWidth: 110,
          //   },
        ],
      },
      data: [],
      status: {
        0: "待机",
        1: "预充",
        2: "放电",
        3: "充电",
        4: "待机",
        5: "短路保护",
        6: "AFE异常",
        7: "预充次数超限",
        8: "关机",
        9: "NTC异常",
        10: "电池断线",
        11: "放电过流1",
        12: "电池低电",
        13: "充电过流",
        14: "充电完成",
      },
    };
  },
  computed: {},
  methods: {
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    handleTrajectory(row) {
      this.$router.push({
        path: `/device/trajectory`,
        query: { devicesn: row.devicesn },
      });
    },
    handleView(row) {
      this.$router.push({ path: `/device/details`, query: { devicesn: row.devicesn } });
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(params.devicesn).then((res) => {
        const data = res.data.data;
        this.data = data;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
