<template>
  <div class="battery-monitor-container dark-theme">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <div class="control-tabs">
        <el-radio-group v-model="checkIBms" @change="changeIBms" size="small">
          <el-radio-button label="实时数据"></el-radio-button>
          <el-radio-button label="历史数据"></el-radio-button>
          <el-radio-button label="定位"></el-radio-button>
          <!-- 修改：移除绝对定位，使用 Flexbox 实现右对齐 -->
        </el-radio-group>
      </div>

      <div class="control-actions">
        <avue-select
            v-model="queryForm.devicesn"
            placeholder="选择设备"
            type="tree"
            size="small"
            style="width: 120px"
            :clearable="false"
            :filterable="true"
            :dic="devEquList"
            @change="changeDevice">
        </avue-select>

        <!-- 添加开始时间选择 -->
        <el-date-picker
            :disabled="!hisDataVislbe && !locationVislbe"
            v-model="queryFormLoc1.startTime"
            type="datetime"
            style="width: 160px;"
            size="small"
            :clearable="false"
            :editable="false"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请输入开始时间">
        </el-date-picker>

        <!-- 添加结束时间选择 -->
        <el-date-picker
            :disabled="!hisDataVislbe && !locationVislbe"
            type="datetime"
            :clearable="false"
            :editable="false"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 160px;"
            size="small"
            v-model="queryFormLoc1.endTime"
            placeholder="请输入结束时间">
        </el-date-picker>

        <el-button @click="getlastDevPage" type="primary" size="small" style="margin-left: 5px">
          <i class="el-icon-search"></i> 搜索
        </el-button>
        <div style="margin-left: 5px">
          <!--
                    <el-button @click="getlastUpData" type="info" size="small"
                               v-if="!equVislbe && !hisDataVislbe && !locationVislbe && status===2">
                      末次上报
                    </el-button>
          -->
          <el-button type="warning" size="small" @click="$refs.hisData.openDialog()"
                     v-if="!hisDataVislbe && !locationVislbe" :disabled="!hisDataVislbe && !locationVislbe">
            导出数据
          </el-button>
          <el-button type="warning" size="small" @click="$refs.hisData.openDialog()" v-if="hisDataVislbe">
            导出数据
          </el-button>
          <el-button type="warning" size="small" @click="exporLoc" v-if="locationVislbe">
            导出数据
          </el-button>
        </div>
        <el-button type="success" size="small" v-if="userInfo.dept_id === '1123598813738675201'"
                   @click="sendOperation('getBluetoothData')">蓝牙</el-button>
        <el-checkbox v-model="ckRefresh" size="small" style="margin-left: 1px" @change="setupAutoRefreshChange">
          自动刷新15/s
        </el-checkbox>

        <div class="status-indicator" style="margin-left: 5px">
          <span :class="['status-dot', status === 2 ? 'offline' : 'online']"></span>
          <span>{{ status === 2 ? '离线' : '在线' }}</span>
        </div>

        <div class="time-info" style="margin-left: 5px">
          <span class="time-label">同步时间</span>
          <span class="time-value">{{ input.time || '--' }}</span>
        </div>
      </div>

      <div class="settings-container">
        <el-radio-group v-model="checkIBms" @change="changeIBms" size="small">
          <el-radio-button label="参数设置" v-if="controlBtn" class="settings-btn"></el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="monitor-content" v-if="!equVislbe && !hisDataVislbe && !locationVislbe">
      <!-- 左侧数据监测区 -->
      <div class="data-panel">
        <!-- 电压监测 -->
        <div class="data-card voltage-card">

          <h3 class="card-title "><span class="icon-battery card-icon"></span>电池电芯监测(V)</h3>
          <div class="voltage-grid">
            <div class="voltage-item" v-for="(voltage, index) in devicesnInfo.voltageList" :key="index">
              <span class="voltage-label">电芯{{ index + 1 }}(V)</span>
              <span class="voltage-value">{{ voltage == 0 ? "--" : voltage.toFixed(3) }}</span>
            </div>
          </div>
        </div>

        <!-- 核心状态 -->
        <div class="data-card status-card">
          <h3 class="card-title"><span class="icon-battery card-icon"></span>电池核心状态</h3>
          <div class="status-content">
            <div class="battery-level">
              <div class="level-circle">
                <div class="progress-circle" :style="circleStyle">
                  <span>{{ devicesnInfo.resiEle }}%</span>
                </div>
              </div>
              <div class="level-label">剩余电量</div>
            </div>
            <div class="status-details">
              <div class="status-item">
                <span>电池电压(V)</span>
                <span>{{ devicesnInfo.batterVolAll || "--" }}</span>
              </div>
              <div class="status-item">
                <span>电池电流(A)</span>
                <span>{{
                    devicesnInfo.eleFlow !== null && devicesnInfo.eleFlow !== "" ? devicesnInfo.eleFlow : "--"
                  }}</span>
              </div>
              <div class="status-item">
                <span>循环次数</span>
                <span>{{
                    devicesnInfo.sumCycEle !== null && devicesnInfo.sumCycEle !== "" ? devicesnInfo.sumCycEle : "--"
                  }}</span>
              </div>
              <div class="status-item" v-if="perHide">
                <span>软件版本</span>
                <span>{{ devicesnInfo.softVer || '--' }}</span>
              </div>
              <div class="status-item">
                <span>电池状态</span>
                <span>{{ statusObj[devicesnInfo.eleStatus] || '--' }}</span>
              </div>
              <div class="status-item location-item" style="text-align: right">
                <span class="location-label">当前位置</span>
                <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="position"
                    placement="top-start">
                  <span :class="bluetoothStatus==1?'location-bluetooth':'location-value'"
                        @click.stop="handleView">{{ status === 2 ? "--" : position }}</span>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>

        <!-- 控制系统状态 -->
        <div class="data-card control-card">
          <h3 class="card-title"><span class="icon-chip card-icon"></span>电池设备状态</h3>
          <div class="control-grid">
            <div class="control-item">
              <span>充电MOS状态</span>
              <span :class="getStatusClass(4)">{{ getStatusText(4) }}</span>
            </div>
            <div class="control-item">
              <span>放电MOS状态</span>
              <span :class="getStatusClass(3)">{{ getStatusText(3) }}</span>
            </div>
            <div class="control-item">
              <span>强启状态</span>
              <span :class="getStatusClass(2)">{{ getStatusText(2) }}</span>
            </div>
            <div class="control-item" v-if="perHide">
              <span>加热片MOS状态</span>
              <span :class="getStatusClass(1)">{{ getStatusText(1) }}</span>
            </div>
            <div class="control-item">
              <span>智能加热功能</span>
              <span :class="getStatusClass(0)">{{ getStatusText(0) }}</span>
            </div>
            <div class="control-item">
              <span>设备CCID</span>
              <span :class="getStatusClass(5)" @click="getQueryCCID" style="cursor:pointer;">查看</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧温度监测区 -->
      <div class="temp-panel">
        <div class="data-card temp-card">
          <h3 class="card-title"><span class="icon-thermometer card-icon"></span>温度监测(℃)</h3>
          <div class="temp-grid">
            <div class="temp-item">
              <span>NTC1温度</span>
              <span>{{ devicesnInfo.temperature1 || '--' }}</span>
            </div>
            <div class="temp-item">
              <span>NTC2温度</span>
              <span>{{ devicesnInfo.temperature2 || '--' }}</span>
            </div>
            <div class="temp-item">
              <span>NTC3温度</span>
              <span>{{ devicesnInfo.temperature3 || '--' }}</span>
            </div>
            <div class="temp-item">
              <span>NTC4温度</span>
              <span>{{ devicesnInfo.temperature4 || '--' }}</span>
            </div>
            <div class="temp-item">
              <span>MOS温度</span>
              <span>{{ devicesnInfo.temperature5 || '--' }}</span>
            </div>
            <div class="temp-item">
              <span>加热片温度</span>
              <span>{{ devicesnInfo.temperature6 || '--' }}</span>
            </div>
            <div class="temp-item" v-if="perHide">
              <span>MCU温度</span>
              <span>{{ devicesnInfo.temperature7 || '--' }}</span>
            </div>
          </div>
        </div>

        <!-- 设备关联的用户 -->
        <div class="data-card reserved-area">

          <h3 class="card-title" style="width: 100%; display: flex; align-items: center;">
            <span style="display: flex; align-items: center;">
              <span class="icon-placeholder card-icon">👤</span>设备关联用户
              <!-- 新增：管理此设备链接 -->
              <el-link
                  :underline="false"
                  type="primary"
                  style=" font-size: 14px"
                  @click="navigateToDeviceManage">
                （管理此设备）
              </el-link>
            </span>
            <!-- 右侧文本容器 -->
            <el-tooltip effect="dark" :content="customerTree" placement="top">
              <div style="max-width: 65%;
                  color: #c7c7c7;
                  line-height: 1.6;
                  padding-left: 16px;
                  font-size: 14px;
                  border-left: 1px solid #333;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;">
                层级： {{ customerTree }}
              </div>
            </el-tooltip>
          </h3>

          <!-- 剩余内容保持不变 -->
          <div class="reserved-content">
            <div class="directory-selector">
              <div class="add-value">
                <el-table
                    :data="bindUser"
                    style="width: 100%"
                    border
                    height="150px"
                    empty-text="暂无数据">
                  <el-table-column type="index" width="70" label="序列" align="center"></el-table-column>
                  <el-table-column prop="name" label="用户名" align="center"></el-table-column>
                  <el-table-column prop="carNo" label="车牌号" align="center"></el-table-column>
                  <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
                  <el-table-column label="操作" width="100" align="center">
                    <template #default="{ row }">
                      <el-button size="small" type="text" @click="UnbindDevice(row)">解绑</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>

        <!-- 日志区域 -->
        <div class="data-card log-card">
          <div class="log-header">
            <h3 class="card-title"
                style="width: 100%; display: flex; justify-content: space-between; align-items: center;">
      <span style="display: flex; align-items: center;">
        <span class="icon-logs card-icon"></span>
        设备日志
      </span>
              <el-button
                  type="primary"
                  size="small"
                  style="margin-left: 10px;"
                  @click="dialogLogVisible=true">
                新增日志
              </el-button>
            </h3>
          </div>
          <div class="log-content">
            <avue-crud
                :data="logData"
                :option="option"
                v-model:page="page"
                @row-save="rowSave"
                @row-update="rowUpdate"
                @row-del="rowDel"
                @size-change="sizeChange"
                @current-change="currentChange">
            </avue-crud>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗集 -->
    <el-dialog
        v-model="dialogVisible"
        title="CCID使用记录"
        width="700px"
        class="ccid-dialog">
      <!-- 当前CCID卡片 -->
      <div class="current-card" v-if="ccidData.current">
        <div class="card-header">
          <el-icon>
            <Connection/>
          </el-icon>
          <span class="ccid">{{ ccidData.current.ccid || '暂无' }}</span>
          <el-tag type="success" effect="dark" size="small">当前使用</el-tag>
        </div>
        <div class="card-body">
          <div class="info-row">
            <span class="label">设备编号：</span>
            <el-tag type="warning">{{ ccidData.current.deviceNo || '暂无' }}</el-tag>
          </div>
          <div class="info-row">
            <span class="label">记录时间：</span>
            <span class="value">{{ ccidData.current.updateTime || '暂无' }}</span>
          </div>
        </div>
      </div>

      <!-- 历史记录时间轴 -->
      <div class="history-section">
        <h3>
          <el-icon>
            <Clock/>
          </el-icon>
          历史记录
        </h3>
        <el-timeline>
          <el-timeline-item
              v-for="(item, index) in ccidData.history"
              :key="index"
              :timestamp="item.updateTime"
              placement="top">
            <el-card :class="['history-card', { 'current': item.isCurrent }]">
              <div class="ccid-info">
                <el-icon>
                  <Connection/>
                </el-icon> &nbsp;&nbsp;
                <span class="ccid">  {{ item.ccid }}</span>
              </div>
              <div class="devices-info">
                <span class="label" style="width: 70px">关联设备：</span>
                <div class="devices-list">
                  <el-tag
                      style="color: #000c17;font-weight: bold;"
                      v-for="device in item.allDevices"
                      :key="device"
                      :type="device === ccidData.current?.deviceNo ? 'primary' : ''">
                    {{ device }}
                  </el-tag>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        <div class="empty-tip" v-if="ccidData.history.length === 0">
          <el-empty description="暂无历史记录"/>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <ibms-equ ref="ibmsEqu" :devicesn="queryForm.devicesn" :softVer="softVer" :status="status"
              v-if="equVislbe"></ibms-equ>

    <his-data ref="hisData" v-if="hisDataVislbe" :dataTime="queryFormLoc"></his-data>

    <location ref="location" v-if="locationVislbe" :dataTime="queryFormLoc" :softVer="softVer"
              :status="status"></location>
  </div>

  <!--添加日志弹窗-->
  <el-dialog
      class="Nubmer-viewForm"
      :fullscreen="false"
      :show-close="true"
      align-center
      append-to-body
      v-model="dialogLogVisible"
      width="400px"
      title="新增日志">
    <el-form-item label="日志内容：">
      <el-input type="textarea" v-model="logDesc"></el-input>
    </el-form-item>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogLogVisible = false">取 消</el-button>
        <el-button type="primary" @click="rowSave">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {mapGetters} from 'vuex'
import {
  getLastDevInfoOne,
  getLastDevInfo,
  getBatterList,
  getAssociationUserInfo,
  liftDeviceBind
} from '@/api/battery/equipment'
import {devicelevelTree, recordAll, saveRecord} from '@/api/device/card'
import {getDeviceVersion} from '@/api/base/batterControl'
import {getQueryCCID} from "@/api/battery/ccid.js";
import ibmsEqu from "./ibmsEqu.vue"
import hisData from "@/views/camera/hisData.vue";
import location from "@/views/battery/location.vue";

export default {
  components: {ibmsEqu, hisData, location},

  data() {
    return {
      customerTree: null,
      bindUser: [],
      logDesc: null,
      dialogLogVisible: false,
      queryFormLoc: {
        devicesn: this.$route.query.devicesn,
      },
      queryFormLoc1: {},
      position: null,
      // 日志相关
      logData: [],
      query: {},
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      dialogVisible: false,
      ccidData: {
        current: null,
        history: []
      },
      option: {
        columnBtn: false,
        refreshBtn: false,
        dialogWidth: '30%',
        height: 240,
        cellBtn: true,
        menuWidth: 200,
        index: true,
        indexLabel: '序列',
        indexWidth: 60,
        align: "center",
        addBtn: false,
        column: [
          {
            label: '时间',
            overHidden: true,
            prop: 'createTime',
            addDisplay: false,
            editDisplay: true
          },
          {
            label: '内容',
            prop: 'content',
            cell: true,
            type: 'textarea',
          }
        ]
      },

      // 控制相关
      controlBtn: false,
      checkIBms: '实时数据',
      equVislbe: false,
      hisDataVislbe: false,
      ckRefresh: true,
      locationVislbe: false,
      timer: null,

      // 设备相关
      queryForm: {
        devicesn: this.$route.query.devicesn,
      },
      devEquList: [],
      equStatus: 0,
      batterId: null,

      // 数据相关
      input: {
        time: null,//同步时间
        enableTime: null
      },
      devicesnInfo: {
        voltageList: [0, 0, 0, 0, 0, 0, 0, 0],
        devStatus: [-1, -1, -1, -1, -1],
        alarm: "00000000",
        batterVolAll: null,
        eleFlow: null,
        resiEle: null,
        sumCycEle: null,
        softVer: null,
        temperature1: null,
        temperature2: null,
        temperature3: null,
        temperature4: null,
        temperature5: null,
        temperature6: null,
        temperature7: null,
        eleStatus: null
      },

      // 状态映射
      statusObj: {
        0: "待机", 1: "预充", 2: "放电", 3: "充电", 4: "待机",
        5: "短路保护", 6: "AFE异常", 7: "预充次数超限", 8: "关机",
        9: "NTC异常", 10: "电池断线", 11: "放电过流", 12: "电池低电",
        13: "充电过流", 14: "充电完成", 15: "MOS过温", 1001: "--"
      },
      softVer: null,
      status: 2,
      // 组件销毁标志
      isDestroyed: false,
      //蓝牙
      ws: null,
      isConnected: false,
      connectionStatus: '未连接',
      messageLogs: [],
      heartbeatTimer: null,
      connecIsDestroyed: false,
      bluetoothStatus: null,
    }
  },

  computed: {
    ...mapGetters(['userInfo']),
    circleStyle() {
      const progress = this.devicesnInfo.resiEle || 0
      return {
        '--progress': `${progress}%`
      }
    }
  },

  methods: {
    // 初始化方法
    init() {
      this.perHide = this.userInfo.dept_id === '1123598813738675201'
      getBatterList().then(res => {
        this.devEquList = res.data.data.map(item => ({
          label: item.equNo,
          value: item.equNo
        }))
      })

      var deviceSn = localStorage.getItem('devicesn')
      if (deviceSn) {
        this.queryForm.devicesn = deviceSn;
        this.queryFormLoc1.startTime = new Date().toISOString().split("T")[0] + " 00:00:00";
        this.queryFormLoc1.endTime = new Date().toISOString().split("T")[0] + " 23:59:59";

        this.getlastDevPage()
      }
      this.controlBtn = this.userInfo.premSign.split(',').includes('controlBtn')
    },

    //蓝牙数据方法
    connectWebSocket() {
      // 连接WebSocket服务器
      this.ws = new WebSocket('wss://socket.sloctopus.com/socket');

      // 连接成功
      this.ws.onopen = () => {
        this.isConnected = true;
        this.connectionStatus = '已连接';
        // this.addLog('成功连接到服务器');
        console.log("成功连接到服务器")
        // 发送身份认证，表明是电脑客户端
        this.ws.send(JSON.stringify({
          type: 'auth',
          role: 'computer'
        }));
        // 启动心跳
        this.startHeartbeat();
      };

      // 接收消息
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          // this.addLog(data.message);
          console.log(data)
            this.$message.warning(data.command.content)
        } catch (error) {
          //this.addLog(`收到未知格式消息: ${event.data}`);
          console.log(`收到未知格式消息: ${event.data}`)
        }
      };

      // 连接关闭
      this.ws.onclose = () => {
        console.log('与服务器的连接已断开')
        if (this.isDestroyed) return; // 如果组件已销毁，不再尝试重连
        this.isConnected = false;
        this.connectionStatus = '已断开';
        // this.addLog('与服务器的连接已断开');

        // 尝试重连
        setTimeout(() => {
          if (!this.isDestroyed) { // 检查组件是否仍然挂载
            console.log('尝试重新连接...')
            this.connectWebSocket();
          }
        }, 3000);

      };

      // 连接错误
      this.ws.onerror = (error) => {
        console.log(`连接错误: ${error.message}`)
        //   this.addLog(`连接错误: ${error.message}`);
      };
    },
    // 发送操作指令
    sendOperation(command) {
      if (!this.isConnected || !this.ws) {
        console.log('未连接到服务器，无法发送指令')
        // this.addLog('未连接到服务器，无法发送指令');
        return;
      }
      this.ws.send(JSON.stringify({
        type: 'operation',
        deviceNo: localStorage.getItem('devicesn'),
        data: command
      }));
      this.$message.success("操作成功,等待小程序上报数据！")
    },

    // 新增：启动心跳
    startHeartbeat() {
      // 清除已有定时器
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
      }
      // 每30秒发送一次心跳
      this.heartbeatTimer = setInterval(() => {
        if (this.isConnected && this.ws) {
          this.ws.send(JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().getTime()
          }));
          //  console.log('发送心跳');
        }
      }, 30000);
    },

    // 新增：停止心跳
    stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },
    //跳转设备进行查看当前设备
    navigateToDeviceManage() {
      // console.log(this.queryForm.devicesn,"当前设备有无？");
      localStorage.removeItem("StorageData");
      this.$router.push({
        path: "/equipment", query: {
          batSn: this.queryForm.devicesn,
        }
      });
    },
    //解除设备绑定
    UnbindDevice(data) {
      let userId = data.id;
      liftDeviceBind({deviceNo: this.queryForm.devicesn, userId: userId}).then(res => {
        if (res.data.data === true) {
          this.$message.success('操作成功！');
          this.BindingUserOpen = false
          this.queyrDevicesnBind(this.queryForm.devicesn)
        } else {
          this.$message.error(res.data.msg);
        }
      })
    },

    exporLoc() {
      this.queryFormLoc = {
        devicesn: this.queryForm.devicesn,
        startTime: this.queryFormLoc1.startTime,
        endTime: this.queryFormLoc1.endTime
      }
      this.$refs.location.exportAlarmMessage();
    },
    //查询ccid
    getQueryCCID() {
      this.dialogVisible = true;
    },
    // 获取设备数据
    async getlastDevPage() {
      if (!this.queryForm.devicesn) return
      try {
        const [devInfoRes, versionRes] = await Promise.all([
          getLastDevInfo(this.queryForm.devicesn),
          getDeviceVersion(this.queryForm.devicesn)
        ])

        // 检查组件是否已销毁
        if (this.isDestroyed) return

        this.queryFormLoc = {
          devicesn: this.queryForm.devicesn,
          startTime: this.queryFormLoc1.startTime,
          endTime: this.queryFormLoc1.endTime
        }

        const data = devInfoRes.data.data
        const versionData = versionRes.data.data
        this.bluetoothStatus = versionData.ccid
        if (versionData.status) {
          this.input.time = null
        }
        // 再次检查组件是否已销毁
        if (this.isDestroyed) return

        this.processDeviceData(data, versionData)

        this.onLoad(this.page)
      } catch (error) {
        console.error('获取设备数据失败:', error)
      } finally {
        // 只有在组件未销毁时才设置自动刷新
        if (!this.isDestroyed) {
          this.setupAutoRefresh()
        }
      }
    },
// 检查两个时间是否超过10天
    // 处理设备数据
    processDeviceData(data, versionData) {
      this.softVer = versionData.softVer
      this.status = versionData.status
      this.input.time = data[0]?.time ?? versionData.onlineTime;
      if (versionData.status === 2) {
        this.resetDeviceData()
        this.devicesnInfo.softVer = versionData.softVer
        this.devicesnInfo.sumCycEle = versionData.sumCycEle
        return;
      }
      this.batterId = versionData.id


      this.input.enableTime = versionData.timeEnable
      const onlineTime = new Date(versionData.onlineTime)
      const currentDate = new Date()
      this.position = onlineTime.toDateString() === currentDate.toDateString()
          ? versionData.position : ""

      this.equStatus = versionData.status === 1 ? 0 : 1

      if (data.length > 0) {
        this.devicesnInfo = {
          ...this.devicesnInfo,
          ...data[0],
          devStatus: data[0].devStatus.split("").map(Number),
          voltageList: Array(8).fill(0).map((_, i) =>
              parseFloat(data[0][`batterVol${i + 1}`]) || 0
          )
        }

        if ([4, 8].includes(this.devicesnInfo.eleStatus)) {
          this.devicesnInfo.eleFlow = 0
        }
      } else {
        this.resetDeviceData()
      }
    },
    async getlastUpData() {
      if (!this.queryForm.devicesn) return

      try {
        const [devInfoRes, versionRes] = await Promise.all([
          getLastDevInfoOne(this.queryForm.devicesn),
          getDeviceVersion(this.queryForm.devicesn)
        ])

        const data = devInfoRes.data.data

        const versionData = versionRes.data.data

        this.processDeviceData(data, versionData)

        this.onLoad(this.page)
      } catch (error) {
        console.error('获取设备数据失败:', error)
      } finally {
        this.setupAutoRefresh()
      }
    },

    // 重置设备数据
    resetDeviceData() {
      Object.keys(this.devicesnInfo).forEach(key => {
        if (!['voltageList', 'devStatus', 'alarm'].includes(key)) {
          this.devicesnInfo[key] = null
        }
      })

      this.devicesnInfo.voltageList = [0, 0, 0, 0, 0, 0, 0, 0]
      this.devicesnInfo.devStatus = [-1, -1, -1, -1, -1]
      this.devicesnInfo.eleStatus = '1001'
    },

    // 自动刷新设置
    setupAutoRefresh() {
      // 确保清理之前的定时器
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }

      if (this.ckRefresh) {
        this.timer = setTimeout(() => {
          this.getlastDevPage()
        }, 15000)
      }
    },
    setupAutoRefreshChange() {
      this.setupAutoRefresh();
    },
    // 状态获取方法
    getStatusText(index) {
      if (index === 0) {
        const status = this.devicesnInfo.devStatus?.[index] ?? -1
        return status === 1 ? "启用" : status === 0 ? "停用" : "--"
      }
      const status = this.devicesnInfo.devStatus?.[index] ?? -1
      return status === 1 ? "开" : status === 0 ? "关" : "--"
    },

    getStatusClass(index) {
      const status = this.devicesnInfo.devStatus?.[index] ?? -1
      return status === 1 ? 'active-status' : status === 0 ? 'inactive-status' : 'unknown-status'
    },

    // 页面切换
    changeIBms() {
      if (this.checkIBms === '定位' || this.checkIBms === '历史数据') {
        if (this.timer) clearTimeout(this.timer)
        this.ckRefresh = false
      } else {
        this.ckRefresh = true
      }
      this.equVislbe = this.checkIBms === '参数设置'
      this.hisDataVislbe = this.checkIBms === '历史数据'
      this.locationVislbe = this.checkIBms === '定位'
    },

    queyrDevicesnBind(devicesn) {
      getAssociationUserInfo(devicesn).then(res => {
        if (Object.keys(res.data.data).length !== 0) {
          this.bindUser = res.data.data;
        } else {
          this.bindUser = [];
        }
      }).catch(error => {
        console.error('获取关联用户信息失败:', error);
        this.bindUser = []; // 请求异常时清空数据
      });
      devicelevelTree(devicesn).then(res => {
        this.customerTree = res.data.data.join(" > ")
      })
    },
    // 设备切换
    changeDevice(val) {
      this.queyrDevicesnBind(val.value)
      this.onLoad(this.page)
      localStorage.setItem('devicesn', val.value);
      getQueryCCID({devicesn: val.value}).then((res) => {
        if (res.data.code === 200) {
          this.ccidData = res.data.data;
        } else {
          this.ccidData = []
        }
      })

      this.queryForm.devicesn = val.value
      this.getlastDevPage()

    },

    // 日志CRUD方法
    rowSave(form) {
      if (!this.batterId) return
      form.batterId = this.batterId
      form.content = this.logDesc
      saveRecord(form).then(() => {
        this.onLoad(this.page)
        this.dialogLogVisible = false
      })
    },

    rowUpdate(form, index, done) {
      saveRecord(form).then(() => {
        done()
        this.onLoad(this.page)
      })
    },

    rowDel(form) {
      form.isDeleted = 1
      saveRecord(form).then(() => {
        this.$message.success("操作成功")
        this.onLoad(this.page)
      })
    },

    sizeChange(val) {
      this.page.currentPage = 1
      this.page.pageSize = val
      this.onLoad(this.page)
    },

    currentChange(val) {
      this.page.currentPage = val
      this.onLoad(this.page)
    },
    isSameDay(dateStr1, dateStr2) {
      if (!dateStr1 || !dateStr2) return false
      return dateStr1.split(' ')[0] === dateStr2.split(' ')[0]
    },
    onLoad(page) {
      recordAll({
        pageNum: page.currentPage,
        pageSize: page.pageSize,
        batterId: this.batterId
      }).then(res => {
        if (res.data.data.data.length > 0) {
          this.logData = res.data.data.data
          this.page.total = res.data.data.total
        } else {
          this.logData = []
        }
      })
    },

    // 位置查看
    handleView() {
      this.checkIBms = '定位'
      this.locationVislbe = true
      this.ckRefresh = false
    },
    destroyWebSocket() {
      this.isDestroyed = true;
      this.stopHeartbeat();
      if (this.ws) {
        this.ws.close();
      }
    }
  },

  mounted() {
    this.init()
    this.connectWebSocket();
  },

  beforeDestroy() {
    if (this.timer) clearTimeout(this.timer)
  },
  beforeUnmount() {
    this.destroyWebSocket()
    // 清理定时器
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }

    // 清理子组件引用
    if (this.$refs.ibmsEqu) {
      this.$refs.ibmsEqu = null
    }
    if (this.$refs.hisData) {
      this.$refs.hisData = null
    }
    if (this.$refs.location) {
      this.$refs.location = null
    }

    // 清理数据引用
    this.logData = []
    this.devEquList = []
    this.devicesnInfo = {
      voltageList: [0, 0, 0, 0, 0, 0, 0, 0],
      devStatus: [-1, -1, -1, -1, -1],
      alarm: "00000000",
      batterVolAll: null,
      eleFlow: null,
      resiEle: null,
      sumCycEle: null,
      softVer: null,
      temperature1: null,
      temperature2: null,
      temperature3: null,
      temperature4: null,
      temperature5: null,
      temperature6: null,
      temperature7: null,
      eleStatus: null
    }

    // 清理查询表单
    this.queryForm = {
      devicesn: this.$route.query.devicesn,
    }
    this.queryFormLoc = {
      devicesn: this.$route.query.devicesn,
    }
    this.queryFormLoc1 = {}

    // 清理CCID数据
    this.ccidData = {
      current: null,
      history: []
    }

    // 清理页面对象
    this.page = {
      total: 0,
      currentPage: 1,
      pageSize: 10
    }

    // 清理输入数据
    this.input = {
      time: null,
      enableTime: null
    }

    // 清理状态变量
    this.controlBtn = false
    this.checkIBms = '实时数据'
    this.equVislbe = false
    this.hisDataVislbe = false
    this.ckRefresh = true
    this.locationVislbe = false
    this.equStatus = 0
    this.batterId = null
    this.softVer = null
    this.status = 2
    this.position = null
    this.dialogVisible = false
    this.isDestroyed = true
  }
}
</script>
<style lang="scss" scoped>
/* 基础布局设置 */
.battery-monitor-container {
  width: 100%;
  // height: 897px;
  // min-height: 85vh;
  margin: 0 auto;
  padding: 10px 5px;
  background-color: #121212;
  color: #e0e0e0;
  font-family: "Arial", sans-serif;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.7);
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;

  /* 控制栏区域 */
  .control-bar {
    display: flex;
    align-items: center;
    padding: 10px 5px;
    background-color: #1e1e1e;
    border-radius: 10px;
    margin-bottom: 10px;
    border: 1px solid #333;

    .control-tabs {
      flex: 0 0 auto;

      .el-radio-group {
        display: flex;

        .el-radio-button {
          //  margin-right: 8px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .control-actions {
      margin-left: 5px;
      display: flex;
      align-items: center;
      gap: 5px;

      .el-select {
        width: 180px;
      }

      .el-button {
        padding: 8px 16px;
      }

      .time-info {
        display: flex;
        align-items: center;
        font-size: 14px;

        .time-label {
          color: #9e9e9e;
          margin-right: 6px;
        }

        .time-value {
          font-weight: 500;
        }
      }

      :deep(.el-input__inner) {
        color: #ffffff;
      }
    }
  }

  /* 主内容区 */
  .monitor-content {
    display: grid;
    grid-template-columns: 7fr 5fr;
    gap: 10px;
    min-height: calc(80vh + 0px);
    grid-template-rows: 783px;

    /* 左侧数据面板 */
    .data-panel {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    /* 右侧面板 */
    .temp-panel {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  /* 卡片通用样式 */
  .data-card {
    background-color: #1e1e1e;
    border-radius: 8px;
    padding: 10px 10px 16px 18px;
    border: 1px solid #333;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #4caf50;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #333;
      display: flex;
      align-items: center;
    }
  }

  /* 电压监测区 */
  .voltage-card {
    .voltage-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .voltage-item {

        background-color: #252525;
        border-radius: 6px;
        padding: 12px;
        border: 1px solid #3a3a3a;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .voltage-label {
          color: #a0a0a0;
          font-size: 14px;

        }

        .voltage-value {
          font-weight: 600;
          color: #ffffff;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }

  /* 电池核心状态 */
  .status-card {
    .status-content {
      display: flex;
      gap: 10px;

      .battery-level {
        flex: 0 0 140px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .level-circle {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          background: conic-gradient(#4caf50 var(--progress), #252525 0);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 10px;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: #1e1e1e;
          }

          span {
            font-size: 24px;
            font-weight: 600;
            z-index: 1;
            color: #ffffff;
          }
        }

        .level-label {
          color: #9e9e9e;
          font-size: 14px;
        }
      }

      .status-details {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .status-item {
          background-color: #252525;
          border-radius: 6px;
          padding: 10px 12px;
          border: 1px solid #3a3a3a;
          display: flex;
          justify-content: space-between;
          align-items: center;

          span:first-child {
            color: #b0b0b0;
            font-size: 14px;
          }

          span:last-child {
            font-weight: 600;
            color: #ffffff;
            font-family: 'Courier New', monospace;
          }
        }
      }
    }
  }

  /* 控制系统状态 */
  .control-card {
    .control-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .control-item {
        background-color: #252525;
        border-radius: 6px;
        padding: 12px;
        border: 1px solid #3a3a3a;
        display: flex;
        justify-content: space-between;
        align-items: center;

        span:first-child {
          color: #b0b0b0;
          font-size: 14px;
        }

        span:last-child {
          font-weight: 600;
          font-size: 14px;

          &.active-status {
            color: #4caf50;
          }

          &.inactive-status {
            color: #f44336;
          }

          &.unknown-status {
            color: #9e9e9e;
          }
        }
      }
    }
  }

  /* 温度监测区 */
  .temp-card {
    .temp-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .temp-item {
        background-color: #252525;
        border-radius: 6px;
        padding: 12px;
        border: 1px solid #3a3a3a;
        display: flex;
        justify-content: space-between;
        align-items: center;

        span:first-child {
          color: #b0b0b0;
          font-size: 14px;
        }

        span:last-child {
          font-weight: 600;
          color: #ff7043;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }

  /* 日志区域 */
  .log-card {
    flex: 1;
    display: flex;
    flex-direction: column;

    .log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-button {
        padding: 6px 12px;
      }
    }

    .log-content {
      width: 97.5%;
      margin-left: 1.25%;
      overflow: hidden;

      :deep(.avue-crud) {
        height: 100%;

        .avue-crud__menu {
          background-color: #252525;
          border-color: #333;
        }

        .avue-crud__pagination {
          padding: 1px;
          background-color: #252525;
          border-color: #333;
        }
      }
    }
  }

  /* 响应式设计 */
  @media (max-width: 1538px) {
    .monitor-content {
      grid-template-columns: 50% 50%;

      .data-panel, .temp-panel {
        width: 100%;
      }
    }

  }
  @media (max-width: 1280px) {
    width: 95%;
    padding: 15px;
    .monitor-content {
      grid-template-columns: 50% 50%;

      .data-panel, .temp-panel {
        width: 100%;
      }
    }
  }

  @media (max-width: 1024px) {
    .monitor-content {
      grid-template-columns: 1fr;
      grid-template-rows: 955px;

      .data-panel, .temp-panel {
        width: 100%;
      }
    }

    .status-content {
      flex-direction: column;

      .battery-level {
        margin-bottom: 20px;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 10px;

    .control-bar {
      flex-direction: column;
      align-items: stretch;
      gap: 10px;

      .control-actions {
        flex-wrap: wrap;
        justify-content: flex-start;
      }
    }

    .voltage-grid,
    .temp-grid,
    .control-grid,
    .status-details {
      grid-template-columns: 1fr !important;
    }
  }

  @media (max-width: 480px) {
    .el-select {
      width: 100% !important;
    }

    .control-actions {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .progress-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: conic-gradient(#4e73df var(--progress, 0%), #eee 0);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.5rem;
    font-weight: 700;
    color: #3498db;
  }

  /* 图标系统 */
  .icon-battery::before {
    content: "🔋";
  }

  .icon-thermometer::before {
    content: "🌡️";
  }

  .icon-chip::before {
    content: "💻";
  }

  .icon-logs::before {
    content: "📝";
  }

  .card-icon {
    font-size: 2rem;
    margin-right: 15px;
    background: #f8f9fa;
    padding: 3px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .location-item {
    display: flex;
    align-items: center;
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden;
    text-overflow: ellipsis;

    .location-label {
      flex-shrink: 0; /* 标签不压缩 */
      color: #b0b0b0;
      margin-right: 8px;
    }

    .location-value {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis; /* 超出部分显示省略号 */
      font-family: 'Courier New', monospace;
      font-weight: 500;
      // 📡
      &::before {
        content: "📍";
        margin-right: 6px;
        opacity: 0.8;
      }
    }

    .location-bluetooth {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis; /* 超出部分显示省略号 */
      font-family: 'Courier New', monospace;
      font-weight: 500;
      color: blue;
      // 📡
      &::before {
        color: blue;
        content: "🌐";
        margin-right: 6px;
        opacity: 0.8;
      }
    }


  }

}

.ccidDataBox {
  width: 100%;
  height: 320px;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid white;
  border-radius: 10px;
  background-color: #f2f1f1;

  &:hover {
    border: 1px solid #45454570;
  }
}

.ccidData {
  width: 100%;
  height: 150px;

  .ccidDataTitle {
    font-size: 16px;
    font-weight: 600;
    margin: 10px 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center; /* 如果需要垂直居中对齐 */
  }

  .ccidDataContent {
    width: 95%;
    height: 78%;
    overflow: auto;
    margin: 10px auto;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .ccidDataContent-child {
    border: 1px solid #333;
    background-color: #252525;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 12px;
    line-height: 28px;
    text-align: center;
    color: #e0e0e0;
    height: 28px;
    min-width: 80px;
    transition: all 0.3s;

    &:hover {
      background-color: #333;
      color: #fff;
    }
  }

}

.status-indicator {
  display: flex;
  align-items: center;
  font-size: 14px;

  .status-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;

    &.online {
      background-color: #67c23a;
    }

    &.offline {
      background-color: #f56c6c;
    }
  }
}

/* 关键修改：让 control-bar 使用 Flexbox 布局 */
.control-bar {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 左右两侧分开 */
  gap: 0px; /* 按钮间距 */
  width: 100%;
}

/* 左侧区域（实时数据/历史数据/定位） */
.control-tabs {
  flex: 0 0 auto; /* 不伸缩 */
}

/* 中间区域（设备选择/搜索等） */
.control-actions {
  flex: 1; /* 占据剩余空间 */
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap; /* 允许换行 */
}

/* 右侧区域（参数设置） */
.settings-container {
  display: flex;
  flex: 0 0 auto; /* 不伸缩 */
  margin-left: auto; /* 关键：右对齐 */
}

.ccid-dialog {
  border-radius: 8px;
}

/* 当前卡片样式 */
.current-card {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 10px;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
}

.card-header .ccid {
  font-weight: 600;
  margin: 0 10px 0 8px;
  color: #333;
}

.card-body {
  display: flex;
  gap: 20px;
}

.info-row {
  display: flex;
  align-items: center;
}

.label {
  color: #1a1919;
  margin-right: 8px;
  font-size: 14px;
}

.value {
  color: #333;
  font-size: 14px;
}

/* 历史记录区域 */
.history-section {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 8px;
}

.history-section h4 {
  display: flex;
  align-items: center;
  color: #333;
  margin-bottom: 10px;
  font-size: 15px;
}

.history-section h4 i {
  margin-right: 8px;
}

.history-card {
  margin-bottom: 12px;
  border-radius: 6px;
}

.history-card.current {
  border-left: 3px solid #67c23a;
}

.ccid-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.ccid-info .ccid {
  font-weight: 500;
  margin-right: 10px;
  font-size: 14px;
}

.devices-info {
  display: flex;
}

.devices-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.empty-tip {
  margin: 40px 0;
}

/* 时间线样式 */
:deep(.el-timeline) {
  padding-left: 8px;
  margin-top: 10px;
}

:deep(.el-timeline-item__timestamp) {
  color: #666;
  font-size: 13px;
}

:deep(.el-card__body) {
  padding: 5px;
}

.el-timeline-item {
  padding-bottom: 1px;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  color: #ffffff;
  background-color: #1c1c1c;
}

/*设备关联用户表格 */
.reserved-area {
  background-color: #1e1e1e;
  border: 1px solid #333;
  height: 350px;
  display: flex;
  flex-direction: column;

  .card-title {
    color: #4caf50;
  }

  .reserved-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .directory-selector {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .add-value {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }

  /* 使用更具体的选择器覆盖Element默认样式 */
  :deep(.el-table) {
    --el-table-header-bg-color: #252525; /* 表头背景色 */
    --el-table-header-text-color: #fff; /* 表头文字色 */
    --el-table-border-color: #333; /* 边框色 */
    --el-table-bg-color: #1e1e1e; /* 表格背景色 */
    --el-table-tr-bg-color: #1e1e1e; /* 行背景色 */
    --el-table-text-color: #e0e0e0; /* 文字颜色 */
    --el-table-row-hover-bg-color: #2e2e2e; /* 悬停背景色 */

    /* 表头单元格样式 */
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: var(--el-table-header-bg-color) !important;
          color: var(--el-table-header-text-color) !important;
          border-bottom: 1px solid var(--el-table-border-color) !important;
        }
      }
    }

    /* 表格单元格样式 */
    .el-table__body-wrapper {
      .el-table__body {
        td {
          background-color: var(--el-table-tr-bg-color) !important;
          color: var(--el-table-text-color) !important;
          border-bottom: 1px solid var(--el-table-border-color) !important;
        }

        tr:hover > td {
          background-color: var(--el-table-row-hover-bg-color) !important;
        }
      }
    }

    /* 按钮样式 */
    .el-button--text {
      color: #4caf50;
    }

    /* 空数据提示样式 */
    .el-table__empty-block {
      background-color: var(--el-table-bg-color);

      .el-table__empty-text {
        color: #9e9e9e;
      }
    }
  }

}


</style>
<style>
.avue-crud__header {
  min-height: 0;
}
</style>
