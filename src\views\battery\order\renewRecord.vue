<template>
  <div class="orderAll-container">
    <basic-container>
      <avue-crud
          style="width: 100%;"
          v-model:search="search"
          :data="tableData"
          :option="option"
          v-model:page="page"
          @search-change="searchChange"
          >
          <template #deviceNo-search="{ disabled, size }">
            <avue-select
                @change="changeDevice"
                v-model="search.deviceNo"
                placeholder="请选择内容"
                type="tree"
                style="width: 90%"
                :filterable="true"
                :dic="devEquList"
                :span="5">
            </avue-select>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {getBatterRenewRecordPages} from "@/api/pay/order";
import {mapGetters} from "vuex";

export default {
  name: "renewRecord",
  data() {
    return {
      tableData: [],
      search:{
        deviceNo:'',
      },
      query:[],
      devEquList:[],
      //日志功能
      option: {
        menu: false,
        calcHeight: 30,
        height:500,
        tip: false,
        searchShow: true,
        searchMenuSpan: 2,
        emptyBtn: false,
        addBtn: false,
        indexLabel: "序号",
        indexWidth: 60,
        columnBtn: true,
        index:true,
        align: "center",
        column: [
          {
            label: '设备编号',
            prop: 'deviceNo',
            editDisplay: false,
            width:220,
            slot: true,
            formslot: true,
            search: true,
            searchSpan: 5
          }, {
            label: '续费前到期时间',
            prop: 'currentDuration',
            span: 20,
          }, {
            label: '续费后到期时间',
            prop: 'renewDuration',
            editDisplay: false,
          }, {
            label: '续费时长',
            prop: 'duration',
            editDisplay: false,
            formatter: function (row, value, column) {
              return value + "年";
            },
            width:130,
          }, {
            label: '到账时间',
            prop: 'updateTime',
            editDisplay: false,
          }, {
            label: '操作人',
            prop: 'userId',
            editDisplay: false,
            width:160,
            formatter: function (row, value, column) {
              return "SLOC";
            },
          },
        ]
      },
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      form: {},
      isDestroyed: false,
    }
  },
  computed: {
    ...mapGetters([ 'userInfo']),
  },
  mounted() {
    this.isDestroyed = false;
    this.init();
    import('@/utils/eventBus').then(module => {
      this.eventBus = module.default;
      this.reloadHandler = (shouldReload) => {
        if (shouldReload) {
          this.onLoad(this.page);
        }
      };
      this.eventBus.$on('reload-device-price', this.reloadHandler);
    });
  },
  beforeUnmount() {
    this.isDestroyed = true;
    this.tableData = [];
    this.devEquList = [];

    if (this.eventBus && this.reloadHandler) {
      this.eventBus.$off('reload-device-price', this.reloadHandler);
    }
  },
  methods: {
    init() {
      this.onLoad(this.page);
      getBatterRenewRecordPages({pageNum: this.page.currentPage, pageSize: this.page.pageSize,customerId:this.userInfo.user_id,...this.query}).then(res => {
        if (this.isDestroyed) return;
        res.data.data.records.forEach(ele => {
          this.devEquList.push({label: ele.deviceNo, value: ele.deviceNo});
        })
      })
    },
    searchChange(val,done){

      this.query.deviceNo = val.deviceNo;
      this.onLoad(this.page,this.query);
      done();
    },
    changeDevice(value){

    },
    onLoad(page,params = {}) {
      getBatterRenewRecordPages({pageNum: page.currentPage, pageSize: page.pageSize,customerId:this.userInfo.user_id,...params}).then(res => {
        if (this.isDestroyed) return;
        this.page.total = res.data.data.total
        this.tableData = res.data.data.records;
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.orderAll-container {
  width: 100%;
  color: #ffffff;
  background-color: #000;
  height: 82vh;
  border: 1px solid white;
  overflow-y: auto;
  overflow-x: hidden;
  &-right {
    width: 100%;
    height: 100%;

  }
}
</style>


