<template>
    <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
            :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave"
            @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
            @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
            <template #menu="{ size, row, index }">
                <el-button @click.stop="handleView(row,index)" icon="el-icon-view" text type="primary" :size="size">查看</el-button>
                <el-button @click.stop="viewDevice(row,index)" icon="el-icon-view" text type="primary" :size="size">管理设备</el-button>
            </template>
        </avue-crud>

        <!-- tag弹窗 -->
        <el-dialog
        class="Tag-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="tagModel"
        width="400px"
        title="设置标签"
        >
            <el-form label-width="100px">
                <el-form-item
                    label="标签名称"
                    v-for="(item, index) in addTagValue"
                    :key="item"
                    :rules="{
                        required: true,
                        message: '必填',
                        trigger: 'blur',
                    }"
                    >
                    <el-select ref="addTagRef" v-model="item.label" clearable>
                        <el-option
                            v-for="item in tagOptions"
                            :key="item.id"
                            :label="item.markName"
                            :value="item.markName"
                        />
                    </el-select>
                    <!-- <el-button class="ml-5" @click.prevent="removeTag(item)">删除</el-button> -->
                </el-form-item>
                <!-- <el-form-item>
                    <span><el-link @click="addTag"><el-icon><CirclePlusFilled /></el-icon>新增标签</el-link></span>
                </el-form-item> -->
                <el-form-item>
                    <span class="ml-10"><el-button type="primary" @click="confirmAddTag">确定</el-button></span>
                    <span class="ml-5"><el-button type="info" @click="tagModel = false">取消</el-button></span>
                </el-form-item>
            </el-form>
        </el-dialog>

        <!-- 查看弹窗 -->
        <el-dialog
        class="Product-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="viewModel"
        width="1000px"
        title="查看产品信息"
        @close="closeViewDialog"
        >
            <el-form :model="form" label-width="120px">
            <el-row :gutter="20">
                <el-col :span="12">
                <el-form-item label="设备总量">
                    {{ viewData.deviceTotal }}
                </el-form-item>
                </el-col>
                <el-col :span="12">
                <el-form-item label="在线总量">
                    {{ viewData.onlineTotal }}
                </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                <el-form-item label="故障总量">
                    {{ viewData.faultsTotal }}
                </el-form-item>
                </el-col>
                <el-col :span="12">
                <el-form-item label="离线数量">
                    {{ viewData.offlineTotal }}
                    <span class="ml-5"><el-link type="primary">管理设备</el-link></span>
                </el-form-item>
                </el-col>
            </el-row>
            <Div class="tabsContent">
                <el-tabs
                    v-model="activeName"
                    type="card"
                    class="device-tabs"
                >
                    <el-tab-pane label="产品信息" name="first">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="产品编码">
                                    {{ viewData.productCode }}
                                </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                <el-form-item label="产品名称">
                                    {{ viewData.productName }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="创建时间">
                                    {{ viewData.createTime }}
                                </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                <el-form-item label="节点类型">
                                    {{ viewData.nodeTypeName }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="联网方式">
                                    {{ viewData.netProtocolName }}
                                </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                <el-form-item label="数据格式">
                                    {{ viewData.dataFormatName }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="产品描述">
                                    {{ viewData.productDesc }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-divider />
                        <div class="rowTitle">标签信息<Span class="ml-10"><el-link type="primary" @click="tagModel = true"><el-icon><Edit /></el-icon><span class="ml-5">编辑</span></el-link></Span></div>
                        <el-row :gutter="20">
                            <el-col :span="24">
                            <el-form-item label="设置标签">
                                <div class="flex gap-2">
                                <el-tag
                                    v-for="item in viewData.labelLists"
                                    :key="item.id"
                                    type="info"
                                    class="ml-5"
                                    effect="light"
                                    round
                                    closable
                                    @close="removeTag(item)"
                                >
                                    {{ item.name }}
                                </el-tag>
                                </div>
                            </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="功能定义" name="second">
                        <el-button type="primary" @click="addFunction(viewData.id)">新增产品功能</el-button>
                        <function-list ref="functionList" />
                    </el-tab-pane>
                </el-tabs>
            </Div>
            </el-form>
        </el-dialog>
        <function-dialog ref="functionDialog" @result="refreshFunction" />
    </basic-container>
</template>
  
<script>
import { getList, getDetail, add, update, remove, saveMark, removeMark } from "@/api/device/product";
import { getList as getTagList } from "@/api/identifying/list"
import { getDictionary } from "@/api/system/dictbiz";
import functionDialog from "./functionDialog.vue"
import functionList from "./functionList.vue"
import { mapGetters } from "vuex";

export default {
    components: { functionList, functionDialog },
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            viewModel: false,
            viewData: {},
            tagModel: false,
            addTagValue: [{
                id: '',
                name: '',
                type: 'info'
            }],
            tagOptions: [],
            activeName: 'first',
            addTagVisible: false,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                searchShow: true,
                span: 24,
                dialogWidth: '40%',
                searchMenuSpan: 8,
                border: true,
                index: true,
                viewBtn: false,
                selection: true,
                dialogClickModal: false,
                menuWidth: '320px',
                column: [
                    {
                        label: "产品名称",
                        prop: "productName",
                        search: true,
                        rules: [{
                            required: true,
                            message: "请输入产品名称",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "节点类型",
                        prop: "nodeType",
                        type: 'select',
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=productType',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        dataType: 'number',
                        rules: [{
                            required: true,
                            message: "请选择节点类型",
                            trigger: "blur",
                        }]
                    },
                    // {
                    //     label: "连网方式",
                    //     prop: "netMethod",
                    //     type: 'select',
                    //     search: true,
                    //     dicUrl: '/blade-system/dict-biz/getDictBizData?code=NetworkMode',
                    //     props: {
                    //         label: 'dictValue',
                    //         value: 'dictKey',
                    //     },
                    //     dataType: 'number',
                    //     rules: [{
                    //         required: true,
                    //         message: "请输入连网方式",
                    //         trigger: "blur"
                    //     }]
                    // },
                    {
                        label: "覆盖范围",
                        prop: "coverage",
                        type: "number",
                        dataType: 'number',
                        rules: [{
                            required: true,
                            message: "请输入覆盖范围",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "联网协议",
                        prop: "netProtocol",
                        type: 'select',
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=NetworkMode',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        dataType: 'number',
                        rules: [{
                            required: true,
                            message: "请选择联网协议",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "数据格式",
                        prop: "dataFormat",
                        type: 'select',
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=DataFormat',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        dataType: 'number',
                        rules: [{
                            required: true,
                            message: "请选择数据格式",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "产品描述",
                        prop: "productDesc",
                        type: "textarea",
                        rules: [{
                            required: false,
                            message: "请输入产品描述",
                            trigger: "blur"
                        }]
                    },
                ]
            },
            data: []
        };
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.dept_add, false),
                viewBtn: this.validData(this.permission.dept_view, false),
                delBtn: this.validData(this.permission.dept_delete, false),
                editBtn: this.validData(this.permission.dept_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        rowSave(row, done, loading) {
            add(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        async handleView(row, index) {
            this.loadTagList(1);
            if (row !== null) {
                await getDetail(row.id).then(res => {
                    this.viewData = res.data.data;
                    this.viewData.nodeTypeName = row.$nodeType;
                    this.viewData.netProtocolName = row.$netProtocol;
                    this.viewData.dataFormatName = row.$dataFormat;
                    this.viewModel = true;
                })
            }
            this.$refs.functionList.onLoad(this.page, { productId: this.viewData.id });
        },
        viewDevice(row, index) {},
        confirmAddTag() {
            // this.viewData.labelLists = [];
            // this.addTagValue.forEach(_item => {
            //     if (_item.label !== '') {
            //         this.viewData.labelLists.push(_item);
            //     }
            // })
            const arr = {
                markName: this.addTagValue[0].label,
                productId: this.viewData.id
            }
            saveMark(arr).then(() => {
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                this.handleView(this.viewData);
                this.tagModel = false;
            })
        },
        removeTag(e) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
            .then(() => {
                return removeMark(e.id);
            })
            .then(() => {
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                this.viewData.labelLists.splice(this.viewData.labelLists.indexOf(e), 1);
            });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            } else {
                this.form.nodeType = 1;
                this.form.netProtocol = 5;

            }
            done();
        },
        closeViewDialog() {
            this.activeName = 'first';
        }, 
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        async getDict(code) {
            let result = [];
            const query = {
                code: code
            }
            result = await getDictionary(query).then(res => {
                const data = res.data.data;
                return data;
            })
            return result;
        },
        loadTagList(type) {
            const query = {
                markType: type,
            }
            getTagList(1, 100, query).then(res => {
                const data = res.data.data;
                this.tagOptions = data.records;
            })
        },
        addFunction(productId) {
            this.$refs.functionDialog.openDialog(productId);
        },
        refreshFunction() {
            this.$refs.functionList.refreshChange();
        }
    }
};
</script>
  
<style lang="scss" scoped>
    .ml-5 {
        margin-left: 5px;
    }
    .ml-10 {
        margin-left: 10px;
    }
    .el-form-item {
        margin-bottom: 10px !important;
    }
    .Product-viewForm {
        .rowTitle {
            font-size: 18px;
            font-weight: bold;
            padding-left: 35px;
            padding-bottom: 20px;
        }
        .tabsContent {
            padding: 20px;
        }
        .el-divider--horizontal {
            margin-top: 0;
        }
    }
</style>