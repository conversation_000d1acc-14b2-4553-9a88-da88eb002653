<template>
  <basic-container>
    <el-form :inline="true" :model="queryForm" class="trajectory-haeder">
      <el-form-item label="设备号码" prop="devicesn" style="width: 100%">
        <avue-select
          v-model="queryForm.devicesn"
          placeholder="请选择内容"
          type="tree"
          :filterable="true"
          :dic="devEquList"
        ></avue-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime" style="width: 100%">
        <avue-date
          type="datetime"
          format="YYYY年MM月DD日 HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          v-model="queryForm.startTime"
          placeholder="请输入开始时间"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime" style="width: 100%">
        <avue-date
          type="datetime"
          format="YYYY年MM月DD日 HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          v-model="queryForm.endTime"
          placeholder="请输入结束时间"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">加载轨迹</el-button>
      </el-form-item>
      <!-- <el-form-item>
        <el-button type="primary" @click="handleStart">播放</el-button>
      </el-form-item> -->
    </el-form>
    <div class="AlarmapContainer" id="AlarmapContainer"></div>
  </basic-container>
</template>

<script>
let marker = null;
let lineArr = [];

import { AMapLoaderInitMap } from "@/utils/amap";
import { getGPSData, getDevList } from "@/api/device/card";
export default {
  data() {
    return {
      map: null,
      queryForm: {
        devicesn: this.$route.query.devicesn,
      },
      dataAlarmap: [
        {
          label: "剩余电量",
          value: 11,
        },
        {
          label: "设备状态",
          value: 11,
        },
        {
          label: "循环次数",
          value: 11,
        },
        {
          label: "电池总电压",
          value: `1V`,
        },
        {
          label: "电流",
          value: `2A`,
        },
        {
          label: "剩余容量百分比",
          value: `3%`,
        },
        {
          label: "累积循环放电次数",
          value: `4次`,
        },
        {
          label: "预计可用时间",
          value: "5h",
        },
        {
          label: "系统软件版本",
          value: "1.0.0",
        },
      ],
      devEquList: [],
    };
  },

  methods: {
    /**
     * 初始化
     **/
    init() {
      getDevList().then((res) => {
        var data = res.data.data;
        data.forEach((dt) => {
          this.devEquList.push({
            label: dt,
            value: dt,
          });
        });
        if (!this.queryForm.devicesn) {
          this.queryForm.devicesn = data[0];
        }

        this.queryForm.startTime = new Date().toISOString().split("T")[0] + " 00:00:00";
        this.queryForm.endTime = new Date().toISOString().split("T")[0] + " 23:59:59";
        this.handleQuery();
      });
    },
    /**
     * 播放轨迹
     **/
    handleStart() {
      marker.moveAlong(lineArr, 200);
    },
    /**
     * 加载轨迹
     **/
    handleQuery() {
      this.$nextTick(() => {
        getGPSData(
          this.queryForm.devicesn,
          this.queryForm.startTime,
          this.queryForm.endTime
        ).then((res) => {
          var data = res.data.data;
          lineArr = [];
          data.forEach((dt) => {
            lineArr.push([dt.lon, dt.lat]);
          });
          AMapLoaderInitMap({
            afterRequest: (AMap) => {
              this.map = new AMap.Map("AlarmapContainer", {
                //设置地图容器id
                resizeEnable: true,
                zoom: 17, //初始化地图级别
                center: [116.397428, 39.90923],
              });

              this.$nextTick(() => {
                let startIcon = new AMap.Icon({
                  size: new AMap.Size(25, 34),
                  image: "/img/货车.png",
                  size: new AMap.Size(57.125, 25),
                  imageSize: new AMap.Size(57.125, 25),
                });

                marker = new AMap.Marker({
                  map: this.map,
                  position: lineArr[0],
                  icon: startIcon,
                  autoRotation: true,
                  offset: new AMap.Pixel(-26, -13),
                  angle: -90,
                });

                // 绘制轨迹
                let polyline = new AMap.Polyline({
                  map: this.map,
                  path: lineArr,
                  showDir: true,
                  strokeColor: "#28F", //线颜色
                  strokeWeight: 6, //线宽
                });

                let passedPolyline = new AMap.Polyline({
                  map: this.map,
                  strokeColor: "#AF5", //线颜色
                  strokeWeight: 6, //线宽
                });

                marker.on("moving", (e) => {
                  passedPolyline.setPath(e.passedPath);
                });

                this.map.setFitView();
              });
            },
          });
        });
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.trajectory-haeder {
  width: 300px;
  position: absolute;
  top: 12vh;
  left: 10px;
  z-index: 2;
  background-color: #ffffff;
  padding: 20px 20px;
}
:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}

:global(#avue-view) {
  margin-bottom: 0px;
}
:global(.MapInfoWindow-content) {
  width: 300px;
  background-color: #ffffff;
}
.basic-container {
  padding: 0px !important;
  margin: 0px !important;
}

:deep(.basic-container__card) {
  height: 100%;
}

.AlarmapContainer {
  width: 100%;
  height: 100%;
}
</style>
