<template>
  <div class="battery-container">
    <!--    <div class="battery-container-left">
          <div class="battery-container-left-block battery-border" id="pieOptions" ></div>
          <div class="battery-container-left-block battery-border" id="barOptions"></div>
        </div>-->
    <div class="battery-container-center battery-border" id="rgisterMap" ></div>
    <div class="battery-container-right">
      <div class="battery-container-right-block battery-container-right-block1  device-overview-card" style="height: 35%">
        <div class="battery-container-right-block1-title" >设备概况</div>
        <div class="battery-container-right-block1-row">
          <div v-for="items in deviceArr" :key="items.label" style="font-weight: bold">
            <div>{{ items.label }}:</div>
            <div :style="{color: items.color}"><span
                @click="handleAlarmClick(items.key)">{{ DeviceSumData[items.key] || 0 }}</span></div>
          </div>
        </div>
      </div>
      <div
          class="battery-container-right-block device-overview-card "
          id="initCustomByEqus"></div>
      <!--      <div class="battery-container-right-block battery-border" id="lastDevOptions"></div>-->
      <div class="battery-container-left-block device-overview-card" id="pieOptions"></div>
    </div>
    <el-dialog title="设备分布" append-to-body v-model="equBox" width="40%">
      <el-row>
        <el-col :span="19">
          <div style="display: flex;">
            <!--<el-select v-model="params.province" clearable placeholder="请选择省份">
              <el-option
                v-for="item in provinceList"
                :key="item.label"
                :label="item.label"
                :value="item.label"
              >
              </el-option>
            </el-select>
            <div style="padding-top: 5px; padding-left: 10px">
              总共：{{equData.length}}
            </div>-->
          </div>
        </el-col>
        <el-col :span="5">
          <el-button type="primary" size="mini">导出全部设备</el-button>
        </el-col>
      </el-row>
      <el-table :data="equData" border size="mini" stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="70"></el-table-column>
        <el-table-column prop="province" label="省份" show-overflow-tooltip></el-table-column>
        <el-table-column prop="equCnt" label="数量" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center">
          <el-button size="mini" type="primary">导出设备</el-button>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {markRaw} from "vue";
import * as echarts from "echarts";
import $ from "jquery";
import {
  getDevCnt,
  getMapLocationCnt,
  getEnableEqu,
  getCustomByEqus,
} from "@/api/device/card";
import {
  mapOptions,
  pieOptions,
  barOptions,
  CustomByEqusOptions,
  lastDevOptions
} from "./home_echats";
// echarts.registerMap("china", mapCode);
let china = "/asset/data-1527045631990-r1dZ0IM1X.json"; //初始化全国地图
import {getAlarmCnt} from "@/api/device/card";

export default {
  data() {
    return {
      params: {},
      rgisterMap: null,
      timeFn: null,
      pieOptions: null,
      customByEqusOptions: null,
      lastDevOptions: null,
      barOptions: null,
      DeviceSumData: {
        deviceTotal: 0,
        onlineTotal: 0,
        faultsTotal: 0,
        offlineTotal: 0,
      },
      deviceArr: [
        {
          label: "设备总量",
          key: "deviceTotal",
          color: '#28a745'
        },
        {
          label: "在线总量",
          key: "onlineTotal",
          color: '#007bff'
        },
        {
          label: "故障设备",
          key: "faultsTotal",
          color: "#dc3545"
        },
        {
          label: "离线总量",
          key: "offlineTotal",
          color: '#ffc107'
        },
      ],
      // 设备展示
      provinceList: [],
      equCnt: 0,
      equBox: false,
      equOption: {
        addBtn: false,
        column: [
          {
            label: "地区",
            prop: "province",
          },
          {
            label: "设备个数",
            prop: "equCnt"
          },
        ]
      },
      equForm: {},
      equData: [],
      equAlarmList: [], //故障列表
      onlineList: [], //在线设备
      resizeListeners: []
    };
  },
  computed: {},
  mounted() {
    this.initRgisterMap();
    this.initPieOptions();
    // this.initBarOptions();
    this.initCustomByEqusOptions();
    // 累计设备数量
    getDevCnt().then((res) => {
      var data = res.data.data;
      this.DeviceSumData = {
        deviceTotal: data.devCnt,
        onlineTotal: data.onlieCnt,
        offlineTotal: data.offlineCnt,
        faultsTotal: data.alarmCnt,
      };
      this.equAlarmList = data.alarmEqus //故障列表
      this.onlineList = data.onlineList //在线列表
    });
  },
  beforeUnmount() {
    if (this.rgisterMap) {
      this.rgisterMap.dispose();
    }
    if (this.pieOptions) {
      this.pieOptions.dispose();
    }
    if (this.customByEqusOptions) {
      this.customByEqusOptions.dispose();
    }
    if (this.lastDevOptions) {
      this.lastDevOptions.dispose();
    }
    if (this.barOptions) {
      this.barOptions.dispose();
    }
    if (this.timeFn) {
      clearTimeout(this.timeFn);
    }
    // 移除所有 resize 事件监听器
    this.resizeListeners.forEach((listener) => {
      window.removeEventListener('resize', listener);
    });
    this.resizeListeners = []; // 清空数组
  },
  methods: {
    /**
     * 告警点击
     **/
    handleAlarmClick(key) {
      if (key === 'faultsTotal') {
        if (this.DeviceSumData[key] > 0) {
          this.changeStorageData("status", "电池故障")
          this.$router.push({
            path: "/equipment",
            query: {
              status: 100
            }
          });
        }
      } else if (key === 'deviceTotal') {
        this.changeStorageData("status", null);
        this.$router.push({
          path: "/equipment",
        });
      } else if (key === 'onlineTotal') {
        this.changeStorageData("status", "在线")
        this.$router.push({
          path: "/equipment",
          query: {
            status: -2
          }
        });
      } else if (key === 'offlineTotal') {
        this.changeStorageData("status", "离线")
        this.$router.push({
          path: "/equipment",
          query: {
            status: -1
          }
        });
      }
    },
    arrTostring(data) {
      let arrTostring = ''
      data.forEach((element, index) => {
        if (index == 0) {
          arrTostring = element;
        } else {
          arrTostring = arrTostring + "," + element;
        }
      })
      return arrTostring;
    },
    initCustomByEqusOptions() {
      let doms = document.getElementById("initCustomByEqus");
      if (!this.customByEqusOptions) {
        let myChart = echarts.init(doms);
        this.customByEqusOptions = markRaw(myChart);
      }
      getEnableEqu().then((res) => {
        var data = res.data.data;
        let arrTostring1 = this.arrTostring(data.enableCntNumbers);
        let arrTostring2 = this.arrTostring(data.eventCntNumbers);
        let arrTostring3 = this.arrTostring(data.dalyTimeNumbers)
        let echartData = [
          {value: data.enableCnt, name: "已过期", equData: arrTostring1},
          {value: data.eventCnt, name: "7天内过期", equData: arrTostring2},
          {value: data.dalyTime, name: "30天内过期", equData: arrTostring3},
        ];
        this.customByEqusOptions.on('click', (params) => {
          localStorage.removeItem('StorageData');
          this.$router.push({
            path: "/equipment",
            query: {
              batSn: params.data.equData
            }
          });

        });
        this.customByEqusOptions.setOption(CustomByEqusOptions(echartData));
      });
    },
    initRgisterMap() {
      // //单击切换到省级地图，当mapCode有值,说明可以切换到下级地图
      // this.rgisterMap.on("click", (params) => {
      //   console.log(params);
      //   clearTimeout(this.timeFn);
      //   //由于单击事件和双击事件冲突，故单击的响应事件延迟250毫秒执行
      //   this.timeFn = setTimeout(() => {
      //     let name = params.name; //地区name
      //     let mapCode = provinces[name]; //地区的json数据
      //     if (!mapCode) {
      //       alert("无此区域地图显示");
      //       return;
      //     }
      //     this.initLoadMap(mapCode, name);
      //   }, 250);
      // });

      // // 绑定双击事件，返回全国地图
      // this.rgisterMap.on("dblclick", (params) => {
      //   //当双击事件发生时，清除单击事件，仅响应双击事件
      //   clearTimeout(this.timeFn);
      //   console.log();
      //   //返回全国地图
      //   this.initLoadMap(china, "china");
      // });
      getMapLocationCnt().then((res) => {
        let provinceArr = res.data.data;
        let provinceObj = {};

        let doms = document.getElementById("rgisterMap");
        if (!this.rgisterMap) {
          this.rgisterMap = markRaw(echarts.init(doms));
        }
        provinceArr.map((item) => {
          let shortName;
          if (item.province === "内蒙古自治区") {
            shortName = "内蒙古";
          } else if (item.province === "黑龙江省") {
            shortName = "黑龙江";
          } else {
            shortName = item.province.substring(0, 2);
          }
          item.province = shortName
          provinceObj[item.province] = item.num;
        });
        //  this.initLastDevOptions(provinceObj);

        this.initLoadMap(china, "china", provinceObj); //初始化全国地图
      });
    },
    initLastDevOptions(provinceObj) {
      let doms = document.getElementById("lastDevOptions");
      if (!this.lastDevOptions) {
        let myChart = echarts.init(doms);
        const resizeListener = () => {
          myChart.resize();
        };
        window.addEventListener('resize', resizeListener);
        this.resizeListeners.push(resizeListener);
        this.lastDevOptions = markRaw(myChart);
      }
      this.lastDevOptions.setOption(
          lastDevOptions(Object.keys(provinceObj), Object.values(provinceObj))
      );
      this.lastDevOptions.on('click', (params) => {
        localStorage.removeItem('StorageData');
        this.$router.push({
          path: "/equipment",
          query: {
            province: params.name
          }
        });
      })
    },
    //变更缓存函数
    changeStorageData(type, value) {
      if (localStorage.getItem("StorageData") != null) {
        let StorageData = JSON.parse(localStorage.getItem("StorageData"))
        if (type == 'province') {
          StorageData.query.province = value;
          StorageData.query.status = null
        }
        if (type == 'status') {
          StorageData.query.status = value;
          StorageData.query.province = undefined;
        }
        localStorage.setItem("StorageData", JSON.stringify(StorageData));
      }
    },
    initLoadMap(mapCode, name, baseData) {
      $.get(mapCode, (data) => {
        if (data) {
          echarts.registerMap(name, data);
          const option = mapOptions(name, baseData);
          this.rgisterMap.setOption(option);
          this.rgisterMap.on('click', (params) => {
            localStorage.removeItem("node");
            this.changeStorageData("province", params.name)
            this.$router.push({
              path: "/equipment",
              query: {
                province: params.name
              }
            });
            // getProvinceByGroup().then(res => {
            //   var provData = res.data.data;
            //   var provinces = []
            //   provData.map(dt => {
            //     provinces.push({
            //       label: dt.province
            //     })
            //   })
            //   this.provinceList = provinces;
            //   this.equData = res.data.data;
            //   this.equBox = true;
            // })
          });
        } else {
          alert("无法加载该地图");
        }
      });
    },
    initPieOptions() {
      let doms = document.getElementById("pieOptions");
      if (!this.pieOptions) {
        let myChart = echarts.init(doms);
        const resizeListener = () => {
          myChart.resize();
        };
        window.addEventListener('resize', resizeListener);
        this.resizeListeners.push(resizeListener);
        this.pieOptions = markRaw(myChart);
      }
      getAlarmCnt().then((res) => {
        var data = res.data.data;
        let echartData = [
          {value: data.num5, name: "短路保护"},
          {value: data.num6, name: "AFE异常"},
          {value: data.num7, name: "预充次数超限"},
          {value: data.num9, name: "NTC异常"},
          {value: data.num10, name: "电池断线"},
          {value: data.num11, name: "放电过流"},
          {value: data.num13, name: "充电过流"},
        ];
        this.pieOptions.on('click', (params) => {
          let result = {};
          // 使用Object.keys()遍历对象的键
          Object.keys(data).forEach(key => {
            if (key.endsWith("alarmEqus")) {
              result[key] = data[key];
            }
          });
          const targetIndex = params.dataIndex;
          const keys = Object.keys(result);
          if (targetIndex < keys.length) {
            const targetKey = keys[targetIndex];
            const targetCollection = data[targetKey];
            localStorage.removeItem('StorageData');
            this.$router.push({
              path: "/equipment",
              query: {
                batSn: targetCollection.join(",")
              }
            });
          } else {
            // console.log("指定下标超出范围");
          }

        });
        this.pieOptions.setOption(pieOptions(echartData));
      });
    },
    initBarOptions() {
      let doms = document.getElementById("barOptions");
      if (!this.barOptions) {
        let myChart = echarts.init(doms);
        const resizeListener = () => {
          myChart.resize();
        };
        window.addEventListener('resize', resizeListener);
        this.resizeListeners.push(resizeListener);
        this.barOptions = markRaw(myChart);
      }

      getCustomByEqus().then((res) => {
        const reuslt = res.data.data;
        let customName = reuslt.map((item) => item.customName);
        let equCnt = reuslt.map((item) => item.equCnt);
        this.barOptions.setOption(barOptions(customName, equCnt));
        this.barOptions.on('click', (params) => {
          localStorage.removeItem('StorageData');
          this.$router.push({
            path: "/equipment",
            query: {
              deptName: params.name
            }
          });
        })
      });
    },
    equipmentCha() {
      localStorage.removeItem('StorageData');
      this.$router.push({path: '/battery/equipment'});
    }
  },
};
</script>

<style lang="scss" scoped>
/*.battery-container-right .battery-border:hover{
  border:2px solid red;
  cursor: pointer;
  &::before{
    content:'点击前往查询';
    position:absolute;
    color:white;
    padding: 10px;
  }
}*/

.battery-container {
  display: flex;
  justify-content: space-between;
  padding: 5px;
  width: 100%;
  height: 100%;

  &-left {
    width: 23%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-block {
      width: 100%;
      height: 50%;
    }
  }

  &-center {
    width: 78%;
    // display: flex;
    // flex-direction: column;
  }

  &-right {
    width: 21.5%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-block {
      width: 100%;
      height: 50%;
    }

    &-block1 {
      color: #ffffff;
      display: flex;
      flex-direction: column;

      &-title {
        font-size: 18px;
        text-align: center;
        margin-top: 3%;
      }

      &-row {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        padding-top: 10px;

        & > div {
          width: 50%;
          text-align: center;

          & > div:nth-child(1) {
            margin-top: 5px;
          }

          & > div:nth-child(2) {
            color: #5dfc36;
            font-size: 22px;
          }
        }
      }
    }
  }
}

:deep(.el-table__body) {
  tr:hover > td.el-table__cell {
    background-color: #ffffff;
  }
}

:deep(.el-dialog__body) {
  padding: 0px;
}

.el-select-dropdown__item.hover {
  background-color: #ffffff;
}

.el-dialog__wrapper {
  .el-dialog {
    border-radius: 10px; /* 设置圆角大小 */
  }
}

.device-overview-card {
  background-color: #1f2f38;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  color: #fff;
  margin-bottom: 2%;
  border: 1px solid #585858;
}
</style>
