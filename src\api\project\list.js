import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/base/projectinfo/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/base/projectinfo/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/base/projectinfo/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/base/projectinfo/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/base/projectinfo/submit',
    method: 'post',
    data: row
  })
}

export const getTreeData = (current, size, params) => {
  return request({
    url: '/api/base/projectinfo/getTreeData',
    method: 'get',
    params: {
      ...params,
      current,
      size,
  }
  })
}

export const saveMark = (row) => {
  return request({
      url: '/api/base/mark/save',
      method: 'post',
      data: row
  })
}

export const removeMark = (ids) => {
  return request({
      url: '/api/base/projectmark/remove',
      method: 'post',
      params: {
          ids,
      }
  })
}