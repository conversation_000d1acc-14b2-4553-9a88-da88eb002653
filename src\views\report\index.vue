<template>
  <basic-container>
    <avue-form :option="option" ref="form" @submit="handleSearch" @error="error">
      <template #text="{ disabled, size }">
        <div>
          <el-button type="primary" @click="$refs.form.submit()">查询</el-button>
          <el-button @click="handleUpload">导出</el-button>
        </div>
      </template>
    </avue-form>
    <div id="CoverCard">
      <el-descriptions title="蚊虫周数据变化周报" :column="4" :size="size" border>
        <el-descriptions-item span="2" label="项目名称">
          {{ reportObj.projectName }}
        </el-descriptions-item>
        <el-descriptions-item span="2" label="设备数量">
          {{ reportObj.deviceTotal }}
        </el-descriptions-item>
        <el-descriptions-item span="2" label="总灭蚊数量">
          {{ reportObj.extinguishNum }}
        </el-descriptions-item>
        <el-descriptions-item span="2" label="项目地址">
          {{ reportObj.region }}
        </el-descriptions-item>
      </el-descriptions>
      <el-table border :data="tableData" stripe style="width: 100%; margin-top: 10px">
        <el-table-column prop="createTime" label="日期" />
        <el-table-column prop="weather" label="天气" />
        <el-table-column prop="windpower" label="风力级别" />
        <el-table-column prop="temperature" label="当日气温" />
        <el-table-column prop="address" label="蚊虫总数量" />
        <el-table-column prop="address" label="是否超过网值" />
      </el-table>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header> 蚊虫数据总量变化图 </template>
        <div id="totalEchats"></div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>高危区蚊虫密度变化</template>
        <div id="densityEchats"></div>
      </el-card>
    </div>
  </basic-container>
</template>

<script>
import { mapGetters } from "vuex";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import * as echarts from "echarts";
import { initEchartsTotal, initDensityEchats } from "./ecahts";
import { getBladeWeatherList, getBaseProjectInfoOtherData } from "@/api/report/report";
import {
  getBaseProjectinfoExtinguishByDate,
  getBaseProjectinfoHighRiskMosquitores,
} from "@/api/common";
let myDensityEchats = null;
let myEchartsTotal = null;
export default {
  data() {
    return {
      tableData: [],
      page: {
        pageSize: 20,
      },
      reportObj: {
        cmf1: 0,
        deviceTotal: 0,
        extinguishNum: 0,
        highRisk: 0,
        projectName: "",
      },
      option: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "所属项目",
            prop: "projectId",
            type: "select",
            dicUrl: "/api/base/projectinfo/page",
            span: 8,
            dicFormatter: (res) => {
              return res.data.records;
            },
            props: {
              value: "id",
              label: "projectName",
            },
            rules: [
              {
                required: true,
                message: "请选择所属项目",
                trigger: "blur",
              },
            ],
          },
          {
            label: "日期",
            prop: "releaseTimeRange",
            type: "datetimerange",
            span: 8,
            searchRange: true,
            hide: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            rules: [
              {
                required: true,
                message: "请选择日期",
                trigger: "blur",
              },
            ],
          },
          {
            label: "",
            prop: "text",
            span: 8,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },

  methods: {
    handleUpload() {
      this.getShareImgBase64(document.getElementById("CoverCard")).then((canvas) => {
        let pagesHeight;
        var canvasWidth = canvas.width; //图片容器宽度
        var canvasHeight = canvas.height; //图片容器高度
        var imgWidth = 0;
        var imgHeight = 0;
        pagesHeight = (canvasWidth / 592.28) * 841.89;
        imgWidth = 592.28;
        imgHeight = (592.28 / canvasWidth) * canvasHeight;
        var pdf = new jsPDF("", "pt", "a4", true);
        var pageData = canvas.toDataURL("image/jpeg", 1.0);
        pdf.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight, "", "FAST");
        pdf.save("蚊虫数据报表导出.pdf");
      });
    },
    getShareImgBase64(dom) {
      return new Promise((resolve) => {
        setTimeout(() => {
          html2canvas(dom, {
            useCORS: true, // 【重要】开启跨域配置
            scale: window.devicePixelRatio < 3 ? window.devicePixelRatio : 2,
            allowTaint: true,
          }).then((canvas) => {
            resolve(canvas);
          });
        }, 300);
      });
    },
    initEchartsTotal(params) {
      // 基于准备好的dom，初始化echarts实例
      if (!myEchartsTotal) {
        myEchartsTotal = echarts.init(document.getElementById("totalEchats"));
      }

      getBaseProjectinfoExtinguishByDate({
        ...params,
        dateType: 2,
        dateLen: 10,
      }).then((res) => {
        const relust = res.data.data;
        const relust1 = relust
          .map((item) => item.date.split("-")[item.date.split("-").length - 1])
          .reverse();
        const relust2 = relust.map((item) => item.extinguishNum);
        // 绘制图表
        myEchartsTotal.setOption(initEchartsTotal(relust2, relust1), true);
      });
      window.addEventListener("resize", () => {
        myEchartsTotal.resize();
      });
    },
    initDensityEchats(params) {
      // 基于准备好的dom，初始化echarts实例
      if (!myDensityEchats) {
        myDensityEchats = echarts.init(document.getElementById("densityEchats"));
      }

      getBaseProjectinfoHighRiskMosquitores({
        ...params,
        dateType: 2,
        dateLen: 10,
      }).then((res) => {
        const relust = res.data.data;
        const relust1 = relust
          .map((item) => item.date.split("-")[item.date.split("-").length - 1])
          .reverse();
        const relust2 = relust.map((item) => item.extinguishNum);
        // 绘制图表1
        myDensityEchats.setOption(initDensityEchats(relust2, relust1), true);
      });
      window.addEventListener("resize", () => {
        myDensityEchats.resize();
      });
    },
    getBladeWeatherList(params) {
      getBladeWeatherList(params).then((res) => {
        res.data.data.map((item) => {
          item["createTime"] = this.$dayjs(item["createTime"]).format("MM月DD日");
        });
        this.tableData = res.data.data;
      });
    },
    getBaseProjectInfoOtherData(params) {
      getBaseProjectInfoOtherData(params).then((res) => {
        this.reportObj = res.data.data;
      });
    },
    handleSearch(form, done) {
      const params = form;
      if (params.releaseTimeRange.length >= 1) {
        params["startTime"] = this.$dayjs(params["releaseTimeRange"][0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        params["endTime"] = this.$dayjs(params["releaseTimeRange"][1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        delete params.releaseTimeRange;
      }
      this.getBladeWeatherList(params);
      this.initEchartsTotal(params);
      this.initDensityEchats(params);
      this.getBaseProjectInfoOtherData(params);
      done();
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
#CoverCard {
  width: 100%;
  height: 100%;
  padding: 20px;
}
#totalEchats {
  width: 100%;
  height: 400px;
}
#densityEchats {
  width: 100%;
  height: 400px;
}
</style>
