<template>
    <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
            :before-open="beforeOpen" v-model="form" ref="crud" @row-save="rowSave" @row-del="rowDel"
            @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
            @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
            <template #menu-left>
                <el-button
                type="primary"
                icon="el-icon-plus"
                @click="handleAdd"
                >新增
                </el-button>
            </template>
        </avue-crud>
        <controls-edit ref="controlsEdit" @result="searchReset" />
    </basic-container>
</template>

<script>
import { reactive, ref } from "vue";
import { getList, add, getDetail, remove } from "@/api/device/controls";
import controlsEdit from './controlsEdit.vue';
import { mapGetters } from "vuex";
export default {
    components: {
        controlsEdit
    },
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                searchShow: true,
                span: 24,
                dialogWidth: '40%',
                searchMenuSpan: 8,
                border: true,
                index: true,
                viewBtn: true,
                editBtn: false,
                delBtn: true,
                addBtn: false,
                selection: true,
                dialogClickModal: false,
                column: [
                    {
                        label: "工作模式",
                        prop: "workType",
                        type: 'select',
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=deviceWorkMode',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        rules: [{
                            required: true,
                            message: "请输入服务名称",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "产品名称",
                        prop: "productId",
                        type: 'select',
                        dicUrl: '/api/base/productinfo/list',
                        dicFormatter: (res => {
                            return res.data.records;
                        }),
                        props: {
                            label: 'productName',
                            value: 'id',
                        },
                        rules: [{
                            required: true,
                            message: "请输入发送类型",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "设备名称",
                        prop: "equIds",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择设备",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "心跳间隔",
                        prop: "heartStep",
                        rules: [{
                            required: true,
                            message: "请输入心跳间隔",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "创建时间",
                        prop: "createTime",
                        editDisplay: false,
                        addDisplay: false
                    },
                ]
            }
        }
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.dept_add, false),
                viewBtn: this.validData(this.permission.dept_view, false),
                delBtn: this.validData(this.permission.dept_delete, false),
                editBtn: this.validData(this.permission.dept_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {},
    methods: {
        handleAdd() {
            this.$refs.controlsEdit.init();
        },
        rowSave(row, done, loading) {
            add(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            } else {
                this.form.nodeType = 1;
                this.form.netProtocol = 5;

            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
    }
}
</script>

<style>

</style>