<template>
  <div class="container">
    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="dialogVisible"
        width="450px"
        @close="closeDialog"
        >
        <el-form ref="form" :model="form" label-width="120px" :inline="true">
          <el-form-item label="功能类型">
            <el-radio-group v-model="form.tslType">
              <el-radio-button v-for="item in tslTypeOption" :key="item.dictKey" :label="item.dictKey">{{ item.dictValue }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="功能名称">
            <el-input v-model="form.tslName" />
          </el-form-item> 
          <el-form-item label="标识符">
            <el-input v-model="form.tslMark" />
          </el-form-item>
          <div v-if="form.tslType == 1">
          <el-form-item label="数据类型">
            <el-select v-model="form.tslDataType">
              <el-option
                v-for="item in typeOptions"
                :key="item.dictKey"
                :label="item.dictValue"
                :value="item.dictKey"
              />
            </el-select>
          </el-form-item> 
          <div v-if="form.tslDataType == 'int' || form.tslDataType == 'long' || form.tslDataType == 'float' || form.tslDataType == 'double'">
            <el-form-item label="取值范围">
              <el-input v-model="form.jsondata.min" placeholder="最小值" style="width: 80px" /><span style="padding-left:10px; padding-right:10px;">~</span><el-input v-model="form.jsondata.max" placeholder="最大值" style="width: 80px" />
            </el-form-item>
            <el-form-item label="步长">
              <el-input v-model="form.jsondata.step" />
            </el-form-item> 
            <el-form-item label="单位">
              <el-input v-model="form.jsondata.unit" />
            </el-form-item>
          </div>
          <div v-if="form.tslDataType == 'text'">
            <el-form-item label="数据长度">
              <el-input type="number" v-model="form.jsondata.length" max="10240" style="width: 80px" /><span style="padding-left:10px; padding-right:10px;">字节</span>
            </el-form-item>
          </div>
          <div v-if="form.tslDataType == 'bool'">
            <el-form-item label="布尔值">
              0<span style="padding-left:10px; padding-right:10px;">-</span><el-input v-model="form.jsondata['0']" placeholder="如：开" style="width: 80px" />
              <span style="padding-left:20px;">1</span><span style="padding-left:10px; padding-right:10px;">-</span><el-input v-model="form.jsondata['1']" placeholder="如：关" style="width: 80px" />
            </el-form-item>
          </div>
          <div v-if="form.tslDataType == 'date'">
            <el-form-item label="时间类型">
              <el-input v-model="form.jsondata.dateType" placeholder="String类型的UTC时间戳（毫秒）" style="width: 300px" />
            </el-form-item>
          </div>
          <div v-if="form.tslDataType == 'enum'">
            <el-form-item label="参数值" v-for="(item, index) in enumList" :key="item.key">
              <el-input v-model="item.key" style="width: 100px" />
              <span style="padding-left:10px;padding-right:10px">参数描述</span><el-input v-model="item.label" style="width: 100px" />
            </el-form-item>
            <el-form-item label=" ">
              <el-link type="primary" @click="addEnum">添加枚举项</el-link>
            </el-form-item>
          </div>
          <el-form-item label="读写类型">
            <el-radio-group v-model="form.accessMode">
              <el-radio :label="item.dictKey" size="large" v-for="item in accessModeOptions" :key="item.dictKey">{{ item.dictValue }}</el-radio>
            </el-radio-group>
          </el-form-item>
          </div>
          <el-form-item label="描述">
            <el-input type="textarea" v-model="form.tslDesc" />
          </el-form-item> 
          <el-form-item label=" ">
            <el-button type="primary" @click="onSubmit">确认</el-button>
            <el-button @click="closeDialog">取消</el-button>
          </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getList, getDetail, add, update, remove } from "@/api/device/tsl";
import { getDictionary } from "@/api/system/dictbiz";
export default {
    data() {
        return {
            form: {
              productId: '',
              tslType: 1,
              specs: {}
            },
            enumList: [],
            dialogVisible: false,
            tslTypeOption: [],
            typeOptions: [],
            accessModeOptions: []
        }
    },
    async created() {
      this.tslTypeOption = await this.getDict("productFuncType");
      this.typeOptions = await this.getDict("dataType");
      this.accessModeOptions = await this.getDict("onlyRead");
    },
    methods: {
      openDialog(productId, id = null) {
        this.form.productId = productId;
        if (id !== null) {
          getDetail(id).then(res => {
            const arr = res.data.data;
            this.form = arr;
          })
        }
        this.dialogVisible = true;
      },
      closeDialog() {
        this.enumList = [];
        this.dialogVisible = false;
      },
      onSubmit() {
        add(this.form).then(res => {
          this.$message({
              type: "success",
              message: "操作成功!"
          });
          this.$emit('result');
          this.closeDialog();
        });
      },
      addEnum() {
        this.enumList.push({
          key: '',
          label: ''
        });
      },
      async getDict(code) {
          let result = [];
          const query = {
              code: code
          }
          result = await getDictionary(query).then(res => {
              const data = res.data.data;
              return data;
          })
          return result;
      },
    }
}
</script>

<style lang="scss" scoped>
  .Nubmer-viewForm {
  }
</style>