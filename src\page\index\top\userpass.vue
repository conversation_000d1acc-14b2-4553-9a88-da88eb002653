<template>
  <avue-form :option="option" v-model="form" @submit="handleSubmit"></avue-form>
</template>

<script>
import md5 from "js-md5";
import { updatePassword } from "@/api/system/user";
import "nprogress/nprogress.css";

export default {
  data() {
    return {
      option: {
        emptyBtn: false,
        submitText: "保存",
        column: [
          {
            label: "原密码",
            span: 20,
            row: true,
            type: "password",
            prop: "oldPassword",
          },
          {
            label: "新密码",
            span: 20,
            row: true,
            type: "password",
            prop: "newPassword",
          },
          {
            label: "确认密码",
            span: 20,
            row: true,
            type: "password",
            prop: "newPassword1",
          },
        ],
      },
    };
  },
  methods: {
    handleSubmit(form, done) {
      updatePassword(
        md5(form.oldPassword),
        md5(form.newPassword),
        md5(form.newPassword1)
      ).then(
        (res) => {
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "修改密码成功!",
            });
            this.$emit("colse");
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
          done();
        },
        (error) => {
          window.console.log(error);
          done();
        }
      );
    },
  },
};
</script>

<style></style>
