<template>
  <div class="dark-customer-management">
    <!-- 顶部操作栏 -->
    <div class="header-actions">

      <!-- 新增搜索框 -->
      <el-input
          id="input"
          v-model="searchText"
          placeholder="输入客户名称进行搜索"
          clearable
          style="width: 220px; margin-right: 10px;margin-left: 10px;"
          @clear="handleSearchClear"
          @keyup.enter="handleSearch">
      </el-input>
      <el-button
          style="color: #ffffff"
          slot="append"
          icon="el-icon-search"
          @click="handleSearch"/>
      <el-button type="warning" class="add-user-btn" @click="addCustomer">
        <i class="el-icon-plus"></i>新增用户
      </el-button>
    </div>

    <!-- 客户数据表格 -->
    <div class="table-container">
      <el-table
          ref="treeTable"
          :data="processedTableData"
          style="width: 100%;"
          row-key="id"
          :expand-row-keys="expandedRowKeys"
          :tree-props="{ children: 'children' }"
          class="dark-table"
          @row-click="toggleExpand"
          @selection-change="handleSelectionChange">

        <!-- 自定义复选框列 -->
        <el-table-column type="selection" width="55">
          <template #default="{ row }">
            <el-checkbox
                v-model="row._checked"
                @change="(val) => handleCheckChange(val, row)"
                @click.stop>
            </el-checkbox>
          </template>
        </el-table-column>

        <!-- 客户名称列 -->
        <el-table-column prop="deptName" label="客户名称" width="260">
          <template #default="{ row }">
    <span class="customer-name">
            <i :class="row.expanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
               v-if="row.children && row.children.length > 0"></i>
            {{ row.deptName.replace('〉', '') }}
            </span>
          </template>
        </el-table-column>

        <!-- 联系人列 -->
        <el-table-column prop="realName" label="联系人" align="center" width="230"/>

        <!-- 联系方式列 -->
        <el-table-column prop="phone" label="联系方式" align="center" width="200">
          <template #default="{ row }">
            <span class="phone-number">{{ row.phone }}</span>
          </template>
        </el-table-column>

        <!-- 创建时间列 -->
        <el-table-column prop="detailAdr" label="地址" align="center"/>

        <!-- 操作列 -->
        <el-table-column label="操作" align="center">
          <template #default="{ row }">

            <div class="action-buttons" @click.stop>

              <el-button size="small" type="info" @click="showEquipment(row)">
                查看设备
              </el-button>

              <el-button size="small" class="edit-btn" @click="handleEdit(row)">
                <i class="el-icon-edit"></i>编辑
              </el-button>
              <el-button size="small" class="permission-btn" @click="handlePermission(row)"
                         v-if="userInfo.dept_id==='1123598813738675201'">
                <i class="el-icon-key"></i>权限分配
              </el-button>
              <el-button size="small" class="reset-btn" @click="handleReset(row)"
                         v-if="userInfo.dept_id==='1123598813738675201'">
                <i class="el-icon-refresh"></i>重置密码
              </el-button>
              <el-button size="small" class="delete-btn" @click="handleDelete(row)">
                <i class="el-icon-delete"></i>删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog
        class="Nubmer-viewForm"
        v-model="TreeDialogVisible"
        width="600px"
        title="权限分配">
      <el-divider content-position="left">参数设置页面</el-divider>
      <el-checkbox-group v-model="checkList">
        <el-checkbox label="controlBtn" value="controlBtn">
          参数设置页面权限
        </el-checkbox>
        <el-checkbox label="logPanel" value="logPanel">
          参数设置操作记录
        </el-checkbox>
      </el-checkbox-group>

      <el-divider content-position="left">核心参数</el-divider>
      <el-checkbox
          :indeterminate="isIndeterminate1"
          v-model="checkAll1"
          @change="handleCheckAllChange('core')">
        全选
      </el-checkbox>
      <el-checkbox-group
          v-model="checkList1"
          @change="handleCheckedParamsChange('core')"
          class="core-params-group">

        <br>
        <el-checkbox
            v-for="param in coreParamsOptions"
            :key="param"
            :label="param"
        >
          {{ getParamLabel(param, 'core') }}
        </el-checkbox>
      </el-checkbox-group>

      <el-divider content-position="left">常用参数</el-divider>
      <el-checkbox
          :indeterminate="isIndeterminate2"
          v-model="checkAll2"
          @change="handleCheckAllChange('common')">
        全选
      </el-checkbox>
      <el-checkbox-group
          v-model="checkList2"
          @change="handleCheckedParamsChange('common')"
          class="common-params-group">
        <br>
        <el-checkbox
            v-for="param in commonParamsOptions"
            :key="param"
            :label="param"
        >
          {{ getParamLabel(param, 'common') }}
        </el-checkbox>
      </el-checkbox-group>
      <el-divider content-position="left">操作指令</el-divider>
      <el-checkbox
          :indeterminate="isIndeterminate3"
          v-model="checkAll3"
          @change="handleCheckAllChange('instructions')">
        全选
      </el-checkbox>

      <el-checkbox-group v-model="instructionsList" @change="handleCheckedParamsChange('instructions')">
        <el-checkbox
            v-for="instruction in instructionsOptions"
            :key="instruction"
            :label="instruction"
        >
          {{ instructionsLabels[instruction] || instruction }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="savePremSign">确 定</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 新增用户弹窗   -->
    <el-dialog
        :title="title"
        v-model="dialogCustomerVisible"
        width="50%">
      <el-form ref="ruleForm" :model="customeForm" :rules="rules" label-width="100px" inline="true">
        <el-form-item label="上级运营商" style="width: 45%">
          <el-input v-model="customeForm.lastDeptName" :disabled="true"></el-input>
        </el-form-item>

        <el-form-item label="运营商名称" prop="deptName" style="width: 45%">
          <el-input v-model="customeForm.deptName"></el-input>
        </el-form-item>
        <el-form-item label="登陆账号" prop="account" style="width: 45%" >
          <el-input v-model="customeForm.account" :disabled="disableEdit" placeholder="登陆账号只能为英文或数字,或俩种混合"></el-input>
        </el-form-item>
        <el-form-item label="登录密码" v-if="!disableEdit" prop="password" style="width: 45%">
          <el-input
              autocomplete="new-password"
              v-model="customeForm.password"
              :type="passwordVisible ? 'text' : 'password'"
              class="password-input-with-icon">
            <template #suffix>
              <el-button
                  icon="el-icon-view"
                  @click="togglePasswordVisibility"
                  class="password-toggle-btn"
                  size="mini"
                  circle
                  plain>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="确定密码" v-if="!disableEdit" prop="newPassword1" style="width: 45%">
          <el-input
              v-model="customeForm.newPassword1"
              :type="confirmPasswordVisible ? 'text' : 'password'"
              class="password-input-with-icon">
            <template #suffix>
              <el-button
                  icon="el-icon-view"
                  @click="toggleConfirmPasswordVisibility"
                  class="password-toggle-btn"
                  size="mini"
                  circle
                  plain>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="联系人名称" prop="realName" style="width: 45%">
          <el-input v-model="customeForm.realName"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone" style="width: 45%">
          <el-input v-model="customeForm.phone"></el-input>
        </el-form-item>
        <el-form-item label="身份证号码" style="width: 45%">
          <el-input v-model="customeForm.idCard"></el-input>
        </el-form-item>

        <el-form-item label="平台昵称" style="width: 45%" prop="name">
          <el-input v-model="customeForm.name"></el-input>
        </el-form-item>

        <el-form-item label="详细地址" style="width: 45%">
          <el-input v-model="customeForm.detailAdr"></el-input>
        </el-form-item>

        <el-form-item label="营业执照" style="width: 47%">
          <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              :http-request="handleAvatar"
              drag
              :before-upload="beforeAvatarUpload">
            <img v-if="imageUrl" :src="imageUrl" class="avatar">

            <el-icon v-else class="el-icon-plus avatar-uploader-icon">
              <upload-filled/>
            </el-icon>

            <div v-if="!imageUrl">
              <em>点击上传jpg/png类型的营业执照图片，且大小不超过2M</em>
            </div>
          </el-upload>

        </el-form-item>
        <el-button v-if="imageUrl" icon="el-icon-view" circle @click="queryImg"></el-button>
        <el-button v-if="imageUrl" type="danger" icon="el-icon-delete" circle @click="deleteImg"></el-button>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetForm('ruleForm')">取 消</el-button>
          <el-button type="primary" @click="submitCustomeForm('ruleForm')">确 定</el-button>
        </div>
      </template>

    </el-dialog>

    <!-- 查看当前用户名下设备弹窗   -->
    <el-dialog title="设备列表" v-model="dialogEquipmentTableVisible" @close="handleClose">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="设备编号">
            <el-input v-model="query.keyword" clearable placeholder="请输入设备编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" icon="el-icon-search" @click="showEquipment">搜索</el-button>
        </el-col>
      </el-row>

      <el-table :data="equipmentData" border stripe>
        <el-table-column
            label="序列"
            type="index"
            align="center"
            width="60"/>

        <el-table-column property="devicesn" label="设备编号" align="center"  width="120">
          <template #="{ row }">
            <div @click="BindingUserIsOpen(row)" style="cursor: pointer;">{{ row.devicesn }}</div>
          </template>
        </el-table-column>
        <el-table-column property="status" label="设备状态" align="center">
          <template #="{ row }">
<!--            <el-tag type="success" v-if="row.status===-2"> 在线</el-tag>-->
            <el-tag type="info" v-if="row.status===-1"> 离线</el-tag>
            <el-tag type="danger" v-if="row.status===100">电池故障</el-tag>
            <el-tag type="success" v-if="row.status===101">开机</el-tag>
            <el-tag type="success" v-if="row.status===2">放电</el-tag>
            <el-tag type="success" v-if="row.status===3">充电</el-tag>
            <el-tag type="success" v-if="row.status===4">待机</el-tag>
            <el-tag type="success" v-if="row.status===8">关机</el-tag>
            <el-tag type="success" v-if="row.status===12">电池低电</el-tag>
            <el-tag type="success" v-if="row.status===14">充电完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="province" label="地区" align="center"></el-table-column>
        <el-table-column property="resiEle" label="剩余电量" align="center"></el-table-column>
        <el-table-column property="sumCycEle" label="循环次数" align="center"></el-table-column>
        <el-table-column property="timeEnable" label="到期时间" align="center">
          <template #="{ row }">
            {{ row.timeEnable.split(" ")[0] }}
          </template>
        </el-table-column>

        <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="110">
          <template #="{ row }">
            <el-button type="text" size="small" @click="handleQrCode(row)">生成二维码</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加一个居中容器 -->
      <div class="pagination-container">
        <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="query.pageNum"
            :page-sizes="[10, 15, 20, 30]"
            :page-size="query.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>
      </div>
    </el-dialog>


    <!-- 查看设备绑定用户弹窗 -->
    <el-dialog
        title="当前设备绑定用户"
        v-model="BindingUserOpen"
        width="600">
      <div class="BindingUserEquit">
        <div class="equipmentContent"><span class="equipmentContentTitle">设备编号：{{ userBindDevicesn || '暂无用户使用' }}</span>
        </div>
        <el-table
            border
            :data="bindUser"
            style="width: 100%">
          <el-table-column
              align="center"
              label="用户名"
              prop="name">
          </el-table-column>
          <el-table-column
              align="center"
              label="车牌号"
              prop="carNo">
          </el-table-column>
          <el-table-column
              align="center"
              label="手机号"
              prop="phone">
          </el-table-column>
          <el-table-column prop="menu" label="操作" align="center">
            <template #="{ row }">
              <el-button type="primary" @click="UnbindDevice(row)" size="small">
                解绑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  buildDeptTree,
  deleteBladeSystemUserRemoveCustom, equipmentAdd, generateQrCode, getAssociationUserInfo, getBladeSystemDeptByDetai,
  getUserByname, liftDeviceBind, resetUserPwd, updateUserPremByDeptId, uploadAvatar
} from "@/api/battery/equipment";
import {mapGetters} from "vuex";
import md5 from "js-md5";
import {getlastDevPage} from "@/api/device/card";

export default {
  data() {
    return {
      userBindDevicesn: null,
      bindUser: [],
      BindingUserOpen: false,
      total: 0,
      query: {
        pageSize: 10,
        pageNum: 1,
        keyword: null,
      },
      dialogEquipmentTableVisible: false,
      equipmentData: [],
      passwordVisible: false,
      confirmPasswordVisible: false,
      searchText: '',          // 搜索文本
      originalTableData: [],   // 原始数据备份
      rules: {
        account: [{required: true, message: '请输入账号', trigger: 'blur'},],
        password: [{required: true, message: '请输入密码', trigger: 'blur'},],
        newPassword1: [{required: true, message: '请确定密码', trigger: 'blur'},],
        deptName: [{required: true, message: '请输入运营商名称', trigger: 'blur'},],
        realName: [{required: true, message: '请输入联系人名称', trigger: 'blur'},],
        phone: [{required: true, message: '请输入联系电话', trigger: 'blur'},],
        name: [{required: true, message: '请输入昵称', trigger: 'blur'},]
      },
      disableEdit: false,
      title: null,
      customeForm: {
        id: "",
        parentId: "",
      },
      rawTableData: [],
      processedTableData: [],
      expandedRowKeys: [],
      coreParamsOptions: [
        'Hargingprotectionvoltage',
        'Chargingrecoveryvoltage',
        'Dischargeprotectionvoltage',
        'Dischargerecoveryvoltage',
        'Dischargeshutdownvoltage',
        'MOStemperatureprotection',
        'MOStemperaturerecovery',
        'Batterytemperatureprotection',
        'Batterytemperaturerecovery',
        'Lowtemperatureprotectionforcharging',
        'Lowtemperaturechargingrecovery',
        'Lowtemperatureprotectionfordischarge',
        'Lowtemperaturedischargerecovery',
        'Heatingactivationtemperature',
        'Heatingstoptemperature',
        'Reportingintervaltime'
      ],
      commonParamsOptions: [
        'Batterycapacity',
        'switch',
      ],
      instructionsOptions: [
        'shutdown',
        'strongStart',
        'autoHeatOn',
        'autoHeatOff',
        'reportData',
        'reportCCID',
        'quickReport',
        'dontallProtect',
        'donttemperatureProtect',
      ],
      TreeDialogVisible: false,
      dialogCustomerVisible: false,
      checkList: [],
      checkList1: [],
      checkList2: [],
      isIndeterminate1: false,
      isIndeterminate2: false,
      isIndeterminate3: false,
      checkAll1: false,
      checkAll2: false,
      checkAll3: false,
      instructionsList: [],
      instructionsLabels: {
        'shutdown': '设备关机',
        'strongStart': '启动强启',
        'autoHeatOn': '开启自动加热',
        'autoHeatOff': '关闭自动加热',
        'reportData': '上报设置数据',
        'reportCCID': '上报CCID',
        'quickReport': '快速上报',
        'dontallProtect': '屏蔽所有保护',
        'donttemperatureProtect': '屏蔽温度保护',
      },
      commonParamLabels: {
        'Batterycapacity': '电池容量',
        'switch': '输出开关',
      },
      paramLabels: {
        'Hargingprotectionvoltage': '充电保护电压',
        'Chargingrecoveryvoltage': '充电恢复电压',
        'Dischargeprotectionvoltage': '放电保护电压',
        'Dischargerecoveryvoltage': '放电恢复电压',
        'Dischargeshutdownvoltage': '放电关断电压',
        'MOStemperatureprotection': 'MOS温度保护',
        'MOStemperaturerecovery': 'MOS温度恢复',
        'Batterytemperatureprotection': '电池温度保护',
        'Batterytemperaturerecovery': '电池温度恢复',
        'Lowtemperatureprotectionforcharging': '充电低温保护',
        'Lowtemperaturechargingrecovery': '充电低温恢复',
        'Lowtemperatureprotectionfordischarge': '放电低温保护',
        'Lowtemperaturedischargerecovery': '放电低温恢复',
        'Heatingactivationtemperature': '加热开启温度',
        'Heatingstoptemperature': '加热停止温度',
        'Reportingintervaltime': '上报间隔时间'
      },
      deptId: null,
      selectedRows: [],
      imageUrl: '',
      currentDeptId: null,
    };
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted() {
    this.onLoad()
  },
  beforeDestroy() {
    this.cleanup();
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {

    //生成设备编号二维码
    handleQrCode(row) {
      console.log(row)
      generateQrCode({content: row.equNo, width: 200, height: 200})
          .then(res => {
            // 创建临时 URL
            const url = URL.createObjectURL(res.data);

            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = `设备二维码_${row.equNo}.png`; // 使用设备编号作为文件名

            // 触发下载
            document.body.appendChild(link);
            link.click();

            // 清理
            document.body.removeChild(link);
            setTimeout(() => URL.revokeObjectURL(url), 100);
          })
          .catch(error => {
            console.error('生成二维码失败:', error);
            // 提示用户错误信息
            this.$message.error('二维码生成失败，请重试');
          });
    },
    handleClose() {
      this.query.keyword = null
    },
    //解绑事件
    UnbindDevice(data) {
      let userId = data.id;
      liftDeviceBind({deviceNo: this.userBindDevicesn, userId: userId}).then(res => {
        if (res.data.data === true) {
          this.$message.success('操作成功！');
          this.BindingUserOpen = false
        } else {
          this.$message.error(res.data.msg);
        }
      })
    },
    //设备列被点击事件
    BindingUserIsOpen(row) {
      this.bindUser = []
      this.BindingUserOpen = true;
      getAssociationUserInfo(row.devicesn).then(res => {
        if (Object.keys(res.data.data).length !== 0) {
          this.bindUser = res.data.data
          this.userBindDevicesn = row.devicesn

        } else {
          this.bindUser = []
          this.bindUser.devicesn = row.devicesn
          this.$message.warning("当前设备暂时没有绑定用户信息！")
        }
      })
    },
    queryImg() {
      if (this.imageUrl !== null) {
        window.open(this.imageUrl, '_blank');
      }
    },
    deleteImg() {
      this.imageUrl = null
    },

    handleAvatar(fileData) {

      const file = new FormData();
      file.append('file', fileData.file);
      uploadAvatar(file).then(res => {
        this.imageUrl = res.data.data.link
      })
    },
    beforeAvatarUpload(file) {
      // 允许 JPG 和 PNG 格式
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg';
      const isPNG = file.type === 'image/png';
      const isAllowedFormat = isJPG || isPNG;

      const isLt2M = file.size / 1024 / 1024 < 2;

      // 验证格式
      if (!isAllowedFormat) {
        this.$message.error('上传营业执照图片只能是 JPG 或 PNG 格式!');
        return false;
      }

      // 验证大小
      if (!isLt2M) {
        this.$message.error('上传营业执照图片大小不能超过 2MB!');
        return false;
      }

      return true;
    },
    handleSizeChange(val) {
      this.query.pageSize = val
      let params = {
        id: this.currentDeptId
      }
      this.showEquipment(params);
    },
    handleCurrentChange(val) {
      this.query.pageNum = val
      let params = {
        id: this.currentDeptId
      }
      this.showEquipment(params)
    },
    // 统一的清理方法
    cleanup() {
      // 清理数据引用
      this.originalTableData = [];
      this.processedTableData = [];
      this.rawTableData = [];
      this.selectedRows = [];
      this.expandedRowKeys = [];

      // 清理表单数据
      this.customeForm = {};
      this.searchText = '';

      // 清理权限相关数据
      this.checkList = [];
      this.checkList1 = [];
      this.checkList2 = [];
      this.instructionsList = [];
      this.deptId = null;

      // 重置状态
      this.isIndeterminate1 = false;
      this.isIndeterminate2 = false;
      this.isIndeterminate3 = false;
      this.checkAll1 = false;
      this.checkAll2 = false;
      this.checkAll3 = false;
      this.passwordVisible = false;
      this.confirmPasswordVisible = false;
      this.disableEdit = false;
      this.TreeDialogVisible = false;
      this.dialogCustomerVisible = false;
    },

    // 安全的深拷贝方法
    safeDeepClone(obj) {
      try {
        if (obj === null || typeof obj !== 'object') {
          return obj;
        }

        // 对于简单对象，使用结构化克隆
        if (obj instanceof Date) {
          return new Date(obj.getTime());
        }

        if (obj instanceof Array) {
          return obj.map(item => this.safeDeepClone(item));
        }

        if (typeof obj === 'object') {
          const cloned = {};
          for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
              cloned[key] = this.safeDeepClone(obj[key]);
            }
          }
          return cloned;
        }

        return obj;
      } catch (error) {
        console.error('深拷贝失败:', error);
        // 如果深拷贝失败，返回浅拷贝
        return {...obj};
      }
    },

    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible;
    },
    toggleConfirmPasswordVisibility() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    },
// 处理搜索
    handleSearch() {
      if (!this.searchText.trim()) {
        this.onLoad();
        return;
      }

      const searchKey = this.searchText.toLowerCase();
      this.processedTableData = this.filterTreeData(
          this.safeDeepClone(this.originalTableData),
          searchKey
      );

      // 自动展开匹配的节点
      this.$nextTick(() => {
        this.expandMatchedNodes();
      });
    },
    // 清空搜索
    handleSearchClear() {
      this.searchText = '';
      this.onLoad();
    },
    // 展开匹配的节点
    expandMatchedNodes() {
      const traverse = (nodes) => {
        nodes.forEach(node => {
          if (node._show && node.children) {
            this.$refs.treeTable.toggleRowExpansion(node, true);
            traverse(node.children);
          }
        });
      };
      traverse(this.processedTableData);
    },
    // 树形数据过滤（核心方法）
    filterTreeData(data, searchKey) {
      return data.filter(item => {
        // 当前节点是否匹配
        const isMatch = item.deptName.toLowerCase().includes(searchKey)
        /*          ||
                  (item.realName && item.realName.toLowerCase().includes(searchKey)) ||
                  (item.phone && item.phone.toLowerCase().includes(searchKey));*/

        item._match = isMatch;

        // 处理子节点
        if (item.children && item.children.length > 0) {
          item.children = this.filterTreeData(item.children, searchKey);

          // 如果子节点有匹配，当前节点也需要显示
          const hasChildMatch = item.children.length > 0;
          item._show = isMatch || hasChildMatch;

          // 展开有匹配的父节点
          if (hasChildMatch) {
            item.expanded = true;
          }
        } else {
          item._show = isMatch;
        }

        return item._show;
      });
    },
    toggleExpand(row) {
      // 只有有子节点的行才能展开/收起
      if (row.children && row.children.length > 0) {
        // 切换展开状态
        row.expanded = !row.expanded;
        // 更新Element UI的展开状态
        this.$refs.treeTable.toggleRowExpansion(row, row.expanded);
      }
    },

    //重置表单
    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields();
      }
      this.dialogCustomerVisible = false
    },
    // 初始化表格数据（添加_checked属性）
    initTableData(data) {
      this.originalTableData = this.safeDeepClone(data); // 保存原始数据

      const processData = (items) => {
        return items.map(item => {
          const newItem = {
            ...item,
            _checked: false,
            _show: true,       // 新增显示标记
            _match: false      // 新增匹配标记
          };
          if (item.children && item.children.length > 0) {
            newItem.children = processData(item.children);
          }
          return newItem;
        });
      };

      this.processedTableData = processData(data);
    },

    // 复选框变化处理
    handleCheckChange(checked, row) {
      row._checked = checked;

      // 手动同步到el-table的选中状态
      this.$nextTick(() => {
        if (this.$refs.treeTable) {
          this.$refs.treeTable.toggleRowSelection(row, checked);
        }
      });

      // 更新选中行列表
      if (checked) {
        this.selectedRows.push(row);
      } else {
        this.selectedRows = this.selectedRows.filter(r => r.id !== row.id);
      }
    },

    // 处理表格选中状态变化
    handleSelectionChange(selection) {
      // 这里可以处理全选等操作，但不会影响独立选中逻辑

    },
    showEquipment(row) {
      this.equipmentData = []
      this.dialogEquipmentTableVisible = true
      let params = {
        deptId: row.id,
      }
      if (this.query.keyword !== null) {
        params.devicesn = this.query.keyword
        params.deptId = this.currentDeptId
      }
      this.currentDeptId = row.id
      getlastDevPage({current: this.query.pageNum, size: this.query.pageSize, ...params}).then(res => {
        this.total = res.data.data.total;
        this.equipmentData = res.data.data.records;
        this.equipmentData.forEach(element => {
          if (element.status === 2) {
            element.province = "--";
            element.resiEle = "--";
          }
          if (element.softVer.length !== 4 || element.sumCycEle < 0) {
            element.softVer = "--";
            element.sumCycEle = "--";
          }
          if (element.timeEnable === "") {
            element.timeEnable = "--"
          }

          element.status = element.status === 1 ? element.eleStatus : -1;
          const targetStatus1 = [5, 6, 7, 9, 10, 11, 13, 15];
          const targetStatus2 = [0, 1];
          if (element.status !== "-1") {
            if (targetStatus1.includes(parseInt(element.status, 10))) {
              element.status = 100;
            } else if (targetStatus2.includes(parseInt(element.status, 10))) {
              element.status = 101;
            }
          }
        })
      })
    },
    //保存新增客户
    submitCustomeForm(formName) {
      if (!this.$refs[formName]) {
        this.$message.error('表单引用不存在');
        return;
      }

      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 创建表单数据副本，避免直接修改原表单
          const formData = {...this.customeForm};

          // 如果是新增用户（没有id）且需要处理密码
          if (!this.customeForm.id) {
            const password = this.customeForm.password;
            const newPassword1 = this.customeForm.newPassword1;

            if (newPassword1 !== password) {
              this.$message.warning("两次输入的密码不一致！");
              return;
            }
            // 只在提交数据中加密密码，不修改原表单
            formData.parentId = this.selectedRows.length > 0 ? this.selectedRows[0].id : null;
            formData.password = md5(password);
          }
          if (this.imageUrl !== null) {
            formData.license = this.imageUrl
          } else {
            formData.license = ""
          }
          equipmentAdd(formData)
              .then((res) => {
                this.$message.success("操作成功!");
                this.dialogCustomerVisible = false;
                this.selectedRows = []; // 清空选中行列表
                this.onLoad();
              })
              .catch((err) => {
                // 错误处理

                // 不需要恢复密码，因为我们没有修改原表单
              });
        } else {

          return false;
        }
      });

    },
    //删除用户
    handleDelete(row) {
      if (row.id === '1123598813738675201') {
        this.$message.warning("SLOC账号默认为最高账号,无法进行删除！");
        return;
      }
      if (row.id === this.userInfo.dept_id) {
        this.$message.warning("无法进行自我删除！");
        return;
      }
      this.$confirm('此操作将永久删除客户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteBladeSystemUserRemoveCustom({id: row.id}).then((res) => {
          this.$message.success("删除成功");
          // 清空复选框勾选状态
          const processData = (items) => {
            return items.map(item => {
              const newItem = {
                ...item,
                _checked: false
              };
              if (item.children && item.children.length > 0) {
                newItem.children = processData(item.children);
              }
              return newItem;
            });
          };
          this.processedTableData = processData(this.processedTableData);
          this.selectedRows = []; // 清空选中行列表
          this.onLoad();
        }).catch((err) => {
          this.$message.error(err.message || '删除失败，请稍后重试');
        });
      }).catch((err) => {
        this.$message.error(err.message || '删除失败，请稍后重试');
      });
    },
    handleEdit(row) {
      if (row.id === '1123598813738675201') {
        this.$message.warning("SLOC账号默认为最高权限,暂时无法调整！");
        return;
      }
      if(row.id === this.userInfo.dept_id){
        this.$message.warning("只能编辑下级，无法自我编辑！");
        return;
      }
      this.disableEdit = true;
      this.title = "编辑客户"
      getBladeSystemDeptByDetai({id: row.id}).then((res) => {
        this.customeForm = res.data.data
        this.imageUrl = res.data.data.license
      }).catch((error) => {
        this.$message.error('获取用户信息失败');
        console.error('获取用户信息失败:', error);
      });
      this.dialogCustomerVisible = true;
    },
    handlePermission(row) {
      if (row.id === '1123598813738675201') {
        this.$message.warning("SLOC账号默认为最高权限,暂时无法调整！");
        return;
      }
      getUserByname({deptId: row.id}).then(res => {
        if (res.data.data.premSign) {
          const allPermissions = res.data.data.premSign.split(',');

          this.checkList = allPermissions.filter(perm =>
              !this.coreParamsOptions.includes(perm) &&
              !this.commonParamsOptions.includes(perm) &&
              !this.instructionsOptions.includes(perm)
          );

          this.checkList1 = allPermissions.filter(perm =>
              this.coreParamsOptions.includes(perm)
          );

          this.checkList2 = allPermissions.filter(perm =>
              this.commonParamsOptions.includes(perm)
          );

          this.instructionsList = allPermissions.filter(perm =>
              this.instructionsOptions.includes(perm)
          );

          this.handleCheckedParamsChange('core');
          this.handleCheckedParamsChange('common');
          this.handleCheckedParamsChange('instructions');
        } else {
          this.checkList = [];
          this.checkList1 = [];
          this.checkList2 = [];
          this.instructionsList = [];
          this.checkAll1 = false;
          this.isIndeterminate1 = false;
          this.checkAll2 = false;
          this.isIndeterminate2 = false;
          this.checkAll3 = false;
          this.isIndeterminate3 = false;
        }
      }).catch((error) => {
        this.$message.error('获取用户权限失败');
        console.error('获取用户权限失败:', error);
      });
      this.deptId = row.id;
      this.TreeDialogVisible = true;
    },
    // 自动展开当前用户的下一级客户
    autoExpandCurrentUserChildren() {
      const currentUserId = this.userInfo.dept_id;
      const findAndExpand = (items) => {
        for (const item of items) {
          if (item.id === currentUserId) {
            if (item.children && item.children.length > 0) {
              item.expanded = true;
              if (this.$refs.treeTable) {
                this.$refs.treeTable.toggleRowExpansion(item, true);
              }
              this.expandedRowKeys = [...this.expandedRowKeys, item.id];
            }
            return true;
          }
          if (item.children && item.children.length > 0) {
            if (findAndExpand(item.children)) {
              return true;
            }
          }
        }
        return false;
      };

      findAndExpand(this.processedTableData);
    },
    addCustomer() {
      this.customeForm = {}
      this.title = "新增客户"
      this.disableEdit = false
      if (this.selectedRows.length <= 0) {
        this.$message.warning("请选择勾选左边的上级机构框")
        return;
      }
      if (this.selectedRows.length > 1) {
        this.$message.warning("上级机构只能选择一个,请重新选择")
        return;
      }

      this.customeForm.lastDeptName = this.selectedRows[0].deptName.toUpperCase()
      this.dialogCustomerVisible = true;
    },
    handleReset(row) {
      let name = row.deptName;
      this.$confirm('此操作将重置用户为 "' + name + '" 的密码,重置后默认密码为:123456, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resetUserPwd({id: row.id, password: md5('123456')}).then(res => {
          this.$message({
            type: 'success',
            message: res.data.msg
          });
        }).catch((error) => {
          this.$message.error('重置密码失败');
          console.error('重置密码失败:', error);
        });
      }).catch(() => {
        // 用户取消操作
      });
    },
    handleCheckAllChange(type) {
      const options =
          type === 'core' ? this.coreParamsOptions :
              type === 'common' ? this.commonParamsOptions :
                  type === 'instructions' ? this.instructionsOptions : [];

      const checkList =
          type === 'core' ? this.checkList1 :
              type === 'common' ? this.checkList2 :
                  type === 'instructions' ? this.instructionsList : [];

      if (type === 'core') {
        this.checkList1 = checkList.length === options.length ? [] : [...options];
        this.isIndeterminate1 = false;
      } else if (type === 'common') {
        this.checkList2 = checkList.length === options.length ? [] : [...options];
        this.isIndeterminate2 = false;
      } else if (type === 'instructions') {
        this.instructionsList = checkList.length === options.length ? [] : [...options];
        this.isIndeterminate3 = false;
      }
    },
    handleCheckedParamsChange(type) {
      const options =
          type === 'core' ? this.coreParamsOptions :
              type === 'common' ? this.commonParamsOptions :
                  type === 'instructions' ? this.instructionsOptions : [];

      const checkList =
          type === 'core' ? this.checkList1 :
              type === 'common' ? this.checkList2 :
                  type === 'instructions' ? this.instructionsList : [];

      const checkedCount = checkList.length;
      const totalOptions = options.length;

      if (type === 'core') {
        this.checkAll1 = checkedCount === totalOptions;
        this.isIndeterminate1 = checkedCount > 0 && checkedCount < totalOptions;
      } else if (type === 'common') {
        this.checkAll2 = checkedCount === totalOptions;
        this.isIndeterminate2 = checkedCount > 0 && checkedCount < totalOptions;
      } else if (type === 'instructions') {
        this.checkAll3 = checkedCount === totalOptions;
        this.isIndeterminate3 = checkedCount > 0 && checkedCount < totalOptions;
      }
    },
    getParamLabel(param, type) {
      const labels = type === 'core' ? this.paramLabels : this.commonParamLabels;
      return labels[param] || param;
    },
    savePremSign() {
      const allSelectedPermissions = [
        ...this.checkList,
        ...this.checkList1,
        ...this.checkList2,
        ...this.instructionsList
      ];

      updateUserPremByDeptId({
        deptId: this.deptId,
        premSign: allSelectedPermissions.join(",")
      }).then(res => {
        this.$message.success("权限分配成功,重新登录后生效");
        this.TreeDialogVisible = false;
        this.checkList = [];
        this.checkList1 = [];
        this.checkList2 = [];
        this.instructionsList = [];
        this.checkAll1 = false;
        this.isIndeterminate1 = false;
        this.checkAll2 = false;
        this.isIndeterminate2 = false;
        this.checkAll3 = false;
        this.isIndeterminate3 = false;
      }).catch(error => {
        this.$message.error("权限分配失败");
        console.error('权限分配失败:', error);
      });
    },
    onLoad() {
      buildDeptTree().then(res => {
        this.rawTableData = res.data.data;
        this.initTableData(this.rawTableData);
        this.$nextTick(() => {
          this.autoExpandCurrentUserChildren();
        });
        this.selectedRows = []; // 清空选中行列表
      }).catch((error) => {
        this.$message.error('加载数据失败');
        console.error('加载数据失败:', error);
      });
    }
  }
}
</script>

<style lang="scss">
/* 黑色主题客户管理系统 - 全屏宽度样式 */
.dark-customer-management {
  padding: 20px;
  background-color: #0d0d0d;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

/* 顶部操作按钮 */
.header-actions {
  height: 50px;
  line-height: 50px;
  background-color: #1e1e1e;
  border: 1px solid #333;
  margin-bottom: 10px;
}

.add-user-btn {
  background: linear-gradient(135deg, #ff8f1e 0%, #ff6b00 100%);
  border: none;
  color: white;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(255, 107, 0, 0.3);
  transition: all 0.3s;
  margin-left: 10px;
}

.add-user-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 0, 0.4);
}

/* 全屏宽度表格容器 */
.table-container {
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 暗黑主题表格 */
.dark-table {
  width: 100%;
  background-color: #121212;
  color: rgba(255, 255, 255, 0.87);
  border-collapse: separate;
  border-spacing: 0;
}

/* 表头样式 */
.dark-table .el-table__header th {
  background-color: #1a1a1a !important;
  color: #e0e0e0;
  font-weight: 600;
  border-bottom: 1px solid #333;
  padding: 12px 0;
}

/* 表格行样式 */
.dark-table .el-table__body tr {
  background-color: #121212;
  transition: background-color 0.2s;
}

.dark-table .el-table__body tr:hover td {
  background-color: #1e1e1e !important;
}

.dark-table .el-table__body td {
  border-bottom: 1px solid #333;
  padding: 12px 0;
}

/* 客户名称高亮 */
.customer-name {
  color: #409EFF;
  font-weight: 500;
}

/* 联系方式等宽字体 */
.phone-number {
  font-family: monospace;
  letter-spacing: 1px;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  min-width: 60px;
  border: none;
  color: white;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.2s;
}

.action-buttons .el-button i {
  margin-right: 4px;
}

.action-buttons .el-button:hover {
  transform: translateY(-2px);
  opacity: 0.9;
}

/* 删除按钮 */
.delete-btn {
  background: linear-gradient(135deg, #ff4d4d 0%, #cc0000 100%);
}

/* 编辑按钮 */
.edit-btn {
  background: linear-gradient(135deg, #4d9eff 0%, #0066cc 100%);
}

/* 权限按钮 */
.permission-btn {
  background: linear-gradient(135deg, #ffb04d 0%, #ff8c00 100%);
}

/* 重置按钮 */
.reset-btn {
  background: linear-gradient(135deg, #48bb78 0%, #2f855a 100%);
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .action-buttons {
    flex-wrap: wrap;
  }

  .action-buttons .el-button {
    min-width: 50px;
    font-size: 12px;
    padding: 4px 8px;
  }
}

@media (max-width: 768px) {
  .dark-customer-management {
    padding: 10px;
  }

  .add-user-btn {
    padding: 8px 16px;
    font-size: 14px;
  }

  .dark-table .el-table__header th,
  .dark-table .el-table__body td {
    padding: 8px 0;
    font-size: 13px;
  }

  .hide-on-mobile {
    display: none;
  }
}

.el-input.is-disabled .el-input__wrapper {
  background-color: #ffffff;
}

/* 复选框样式调整 */
.el-table .el-checkbox {
  margin-right: 8px;
}

/* 隐藏默认箭头 */
.el-table__expand-icon > .el-icon {
  color: #ffffff;
}

#input {
  color: #ffffff;
}

.password-toggle-btn {
  border: none;
  background: none;
  color: #909399;
  transition: color 0.2s;
  margin-right: -5px; /* 微调位置 */
}

.password-toggle-btn:hover {
  color: #409EFF;
}

.password-toggle-btn:focus {
  outline: none;
  box-shadow: none;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 50px;
  width: 25%;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 100%;
  height: 100%;
  display: block;
}

.el-upload-dragger {
  padding: 15px;
}

.BindingUserEquit {
  height: 400px;
  width: 100%;
  border: 1px dashed #ccc;
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;

  .equipmentContent {
    margin: 11px;
    font-size: 16px;
    font-weight: 650;
    width: 60%;

  }

  .equipmentContentTitle {
    display: inline-block;
    width: 100%;
    line-height: 20px;
    overflow: hidden; /* 隐藏溢出的内容 */
    text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
    white-space: nowrap;
  }

  // &:hover{
  //   border: 1px solid #ccc;
  // }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 16px 0 16px 0;
}

</style>
