import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/base/deviceinfo/getDeviceAllStatus',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/api/base/deviceinfo/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/base/deviceinfo/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/api/base/deviceinfo/save',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/base/deviceinfo/submit',
        method: 'post',
        data: row
    })
}

export const send = (row) => {
    return request({
        url: '/api/base/deviceinfo/deviceSend',
        method: 'post',
        data: row
    })
}

export const getGPSData = (devicesn, startTime, endTime) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getGPSData',
        method: 'get',
        params: {
            devicesn,
            startTime,
            endTime
        }
    })
}

export const PcIssuedData = (data) => {
    return request({
        url: '/api/blade-base/batterControl/wxSendComC8',
        method: 'get',
        params: {
            ...data
        }
    })
}
//获取gps点位数据 intervalMinutes 分钟
export const getIotdbGpsPointData = (devicesn, startTime, endTime,intervalMinutes) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getIotdbGpsPointData',
        method: 'get',
        params: {
            devicesn,
            startTime,
            endTime,
            intervalMinutes
        }
    })
}

export const exportGPSData = (devicesn, startTime, endTime) => {
    return request({
        url: '/api/iotdb/IotEquipSource/exportGPSData',
        method: 'post',
        params: {
            devicesn,
            startTime,
            endTime,
        },
        responseType: 'blob'
    })
}

//获取当前设备最后五条数据
export const getTop5PointData = (devicesn) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getTop5PointData',
        method: 'get',
        params: {
            devicesn,
        }
    })
}

export const getDevData = (params) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getDevData',
        method: 'get',
        params: params
    })
}
//获取日志记录
export const recordAll = (params) => {
    return request({
        url: '/api/record/recordAll',
        method: 'get',
        params: params
    })
}
//日志修改或提交接口  json参数
export const saveRecord = (row) => {
    return request({
        url: '/api/record/saveRecord',
        method: 'post',
        data: row
    })
}
export const getDeviceSynchronizeByNumber = (devicesn) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getDeviceSynchronizeByNumber',
        method: 'get',
        params: {devicesn}
    })
}

//数据监控接口预留
export const exportHisData = (params) => {
    return request({
        url: '/api/iotdb/IotEquipSource/exportHisData',
        method: 'post',
        params: params,
        responseType: 'blob'
    })
}

export const getDevList = () => {
    return request({
        url: '/api/iotdb/IotEquipSource/getDevList',
        method: 'get',
        params: {}
    })
}

export const getDevCnt = () => {
    return request({
        url: '/api/iotdb/IotEquipSource/getDevCnt',
        method: 'get',
        params: {}
    })
}

export const getAlarmCnt = () => {
    return request({
        url: '/api/iotdb/IotEquipSource/getAlarmCnt',
        method: 'get',
        params: {}
    })
}

export const getMapLocationCnt = () => {
    return request({
        url: '/api/iotdb/IotEquipSource/getMapLocationCnt',
        method: 'get',
        params: {}
    })
}

export const getMonthList = () => {
    return request({
        url: '/api/iotdb/IotEquipSource/getMonthList',
        method: 'get',
        params: {}
    })
}

export const getlastDevList = () => {
    return request({
        url: '/api/iotdb/IotEquipSource/getlastDevList',
        method: 'get',
        params: {}
    })
}

export const getDevPage = (devicesn) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getDevPage',
        method: 'get',
        params: {
            devicesn
        }
    })
}


export const getlastDevPage = (params) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getlastDevPage',
        method: 'get',
        params: params
    })
}
//设备所在客户层级
export const devicelevelTree = (deviceNo) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getDeviceLevelTree',
        method: 'get',
        params: {deviceNo}
    })
}


export const getCustomByEqus = (devicesn) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getCustomByEqus',
        method: 'get',
        params: {
            devicesn
        }
    })
}


export const getEnableEqu = (devicesn) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getEnableEqu',
        method: 'get',
        params: {
            devicesn
        }
    })
}

//获取全部转移
export const getTransferRecordPageAll = (params) => {
    return request({
        url: '/api/transfer/getTransferRecordPageAll',
        method: 'get',
        params: params
    })
}

//保存转移记录
export const saveTransferRecord = (data) => {
    return request({
        url: '/api/transfer/saveTransferRecord',
        method: 'post',
        data: data
    })
}

//获取用户反馈数据
export const getFeedbackLogPages = (params) => {
    return request({
        url: '/api/blade-base/feedback/getFeedbackLogPages',
        method: 'get',
        params: params
    })

}

//查询未读的数量
export const readFeedbackLog = () => {
    return request({
        url: '/api/blade-base/feedback/readFeedbackLog',
        method: 'get',
    })
}

export const saveFeedbackLog = (data) => {
    return request({
        url: '/api/blade-base/feedback/saveFeedbackLog',
        method: 'post',
        data:data
    })
}

//短信回复
export const sendSmsReply = (data) => {
    return request({
        url: '/api/blade-base/batterInfo/smsSend',
        method: 'post',
        data:data
    })
}
