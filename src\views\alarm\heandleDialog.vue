<template>
  <div class="container">
    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="dialogVisible"
        width="450px"
        @close="closeDialog"
        title="告警处理"
        >
        <el-form ref="form" :model="form" label-width="120px" :inline="true">
            <el-form-item label="处理方式">
                <el-radio-group v-model="form.alarmHandle">
                    <el-radio v-for="item in wayOptions" :key="item.dictKey" :label="item.dictKey">{{ item.dictValue }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.alarmHandle === '5'" label="忽略时长">
                <el-select
                    v-model="form.ignoreDuration"
                    placeholder="请选择忽略时长"
                    clearable
                >
                    <el-option v-for="(item, index) in ignoreDurationOption" :key="item.dictKey" :label="item.dictValue" :value="item.dictKey" />
                </el-select>
            </el-form-item>
            <el-form-item label="告警处理描述">
                <el-input
                    v-model="form.alarmRuleDesc"
                    :rows="2"
                    type="textarea"
                    placeholder=""
                />
            </el-form-item>
            <el-form-item label=" ">
            <el-button type="primary" @click="onSubmit">确认</el-button>
            <el-button @click="closeDialog">取消</el-button>
          </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { update } from "@/api/alarm/alarmcurrent";
import { getDictionary } from "@/api/system/dictbiz";
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                id: '',
                alarmHandle: '3',
                alarmRuleDesc: '',
                ignoreDuration: ''
            },
            wayOptions: [],
            ignoreDurationOption: []
        }
    },
    async created() {
        this.wayOptions = await this.getDict("excuteWay");
        this.ignoreDurationOption = await this.getDict("ignoreDuration");
    },
    methods: {
        onSubmit() {
            update(this.form).then(() => {
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                this.$emit('result');
                this.dialogVisible = false;
            }, error => {
                console.log(error);
            });
        },
        async getDict(code) {
          let result = [];
          const query = {
              code: code
          }
          result = await getDictionary(query).then(res => {
              const data = res.data.data;
              return data;
          })
          return result;
      },
      openDialog(id) {
        this.form.id = id;
        this.dialogVisible = true;
      },
      closeDialog() {
        this.form = {
            id: '',
            alarmHandle: '3',
            alarmRuleDesc: ''
        };
        this.dialogVisible = false;
      },
    }
}
</script>

<style lang="scss" scoped>

</style>