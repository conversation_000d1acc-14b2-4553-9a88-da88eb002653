<template>
    <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
            :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave"
            @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
            @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">

            <template #menu-left>
                <el-button type="success" plain icon="el-icon-upload" @click="handleImport">导入</el-button>
                <el-button
                    type="warning"
                    plain
                    icon="el-icon-download"
                    @click="handleExport"
                    >导出
                </el-button>
            </template>
        </avue-crud>
        <el-dialog title="设备SN数据导入" append-to-body v-model="excelBox" width="555px">
            <avue-form :option="excelOption" v-model="excelForm" :upload-after="uploadAfter">
                <template #excelTemplate>
                <el-button type="primary" @click="handleTemplate">
                    点击下载<i class="el-icon-download el-icon--right"></i>
                </el-button>
                </template>
            </avue-form>
        </el-dialog>
    </basic-container>
</template>
  
<script>
import { getImpotList } from "@/api/device/list";
import { mapGetters } from "vuex";
import { exportBlob } from '@/api/common';
import website from '@/config/website';
import { getToken } from '@/utils/auth';
import { downloadXls } from '@/utils/util';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import func from '@/utils/func';

export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            excelBox: false,
            excelForm: {},
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            excelOption: {
                submitBtn: false,
                emptyBtn: false,
                column: [
                {
                    label: '模板上传',
                    prop: 'excelFile',
                    type: 'upload',
                    drag: true,
                    loadText: '模板上传中，请稍等',
                    span: 24,
                    propsHttp: {
                        res: 'data',
                    },
                    tip: '请上传 .xls,.xlsx 标准格式文件',
                    action: '/api/base/deviceinfo/importDevice',
                },
                {
                    label: '数据覆盖',
                    prop: 'isCovered',
                    type: 'switch',
                    align: 'center',
                    span: 8,
                    width: 80,
                    dicData: [
                    {
                        label: '否',
                        value: 0,
                    },
                    {
                        label: '是',
                        value: 1,
                    },
                    ],
                    value: 0,
                    slot: true,
                    rules: [
                    {
                        required: true,
                        message: '请选择是否覆盖',
                        trigger: 'blur',
                    },
                    ],
                },
                {
                    label: '模板下载',
                    prop: 'excelTemplate',
                    formslot: true,
                    span: 24,
                },
                ],
            },
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                searchShow: true,
                searchMenuSpan: 6,
                border: true,
                index: true,
                viewBtn: false,
                addBtn: false,
                editBtn: false,
                delBtn: false,
                selection: true,
                dialogClickModal: false,
                menu: false,
                column: [
                    {
                        label: "IMEI",
                        prop: "deviceSn",
                        search: true,
                        rules: [{
                            required: true,
                            message: "请选择设备SN",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "简易码",
                        prop: "deviceRandomCode",
                        rules: [{
                            required: true,
                            message: "请输入设备随机码",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "导入日期",
                        prop: "createTime",
                        search: true,
                        type: 'date'
                    },
                ]
            },
            data: []
        };
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.dept_add, false),
                viewBtn: this.validData(this.permission.dept_view, false),
                delBtn: this.validData(this.permission.dept_delete, false),
                editBtn: this.validData(this.permission.dept_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        // 导入
        handleImport() {
            this.excelBox = true;
        },
        // 导出
        handleExport() {
            const deviceSn = func.toStr(this.query.deviceSn);
            const createTime = func.toStr(this.query.createTime);
            this.$confirm('是否导出设备SN数据?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                NProgress.start();
                exportBlob(
                `/api/base/deviceinfo/exportDeviceData?${
                    this.website.tokenHeader
                }=${getToken()}&deviceSn=${deviceSn}&createTime=${createTime}`
                ).then(res => {
                downloadXls(res.data, `设备SN数据表${this.$dayjs().format('YYYY-MM-DD')}.xlsx`);
                NProgress.done();
                });
            });
        },
        rowSave(row, done, loading) {
            add(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getImpotList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        uploadAfter(res, done, loading, column) {
            console.log(column);
            this.excelBox = false;
            this.refreshChange();
            done();
        },
        handleTemplate() {
            exportBlob(
                `/api/base/device/export-template?${this.website.tokenHeader}=${getToken()}`
            ).then(res => {
                downloadXls(res.data, '设备SN数据模板.xlsx');
            });
        },
    }
};
</script>
  
<style></style>
  