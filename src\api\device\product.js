import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/base/productinfo/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/api/base/productinfo/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/base/productinfo/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/api/base/productinfo/submit',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/base/productinfo/update',
        method: 'post',
        data: row
    })
}

export const saveMark = (row) => {
    return request({
        url: '/api/base/productmark/save',
        method: 'post',
        data: row
    })
}

export const removeMark = (ids) => {
    return request({
        url: '/api/base/productmark/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}