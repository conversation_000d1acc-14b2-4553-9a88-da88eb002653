// 全局变量
@import './variables.scss';

a {
  text-decoration: none;
  color: #333;
}

* {
  outline: none;
  box-sizing: border-box;
  padding: 0px;
  margin: 0;
}

body {
  // background-color: #000;
  // hjl
  background-color: #1c1c1c;
}

.avue-sidebar,
.avue-top,
.avue-logo,
.avue-layout .login-logo,
.avue-main {
  transition: all .3s;
}

.avue-layout {
  display: flex;
  height: 100%;
  overflow: hidden;

  .avue-sidebar-lefts {
    display: flex;
    align-items: center;
  }

  .el-menu {
    background-color: transparent;
    font-weight: 700;
    border-bottom: solid 2px var(--el-menu-border-color);
  }

  .el-menu--horizontal>.el-menu-item.is-active {
    background-color: var(--el-color-default);
    color: #ffffff !important;
    border-bottom: 2px solid var(--el-color-default)
  }

  &--horizontal {
    flex-direction: column;

    .avue-sidebar {
      width: 100%;
      height: $top_height;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // background-color: #000;
      // hjl
      background-color: #1c1c1c;
      .avue-menu,
      .el-menu-item,
      .el-sub-menu__title {
        height: $top_height;
        line-height: $top_height;
      }

      .is-active:before {
        display: none;
      }
    }

    .avue-logo {
      // width: $sidebar_width;
      margin: 20px;
      font-size: 24px;
      font-weight: bold;
      color: #f7f7f8;
    }
  }
}

.avue-contail {
  width: 100%;
  height: 100%;
  //background: #f0f2f5;
  background-size: 100%;
  background-repeat: no-repeat;
}

.avue--collapse {

  .avue-sidebar,
  .avue-logo {
    width: $sidebar_collapse;
  }
}

.avue-main {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
  // background: #EEF1F3;
  border-left: 1px solid rgba(0, 0, 0, 0.08);
}

#avue-view {
  flex: 1;
  // overflow-y: auto;
  // overflow-x: hidden;
  overflow: hidden;
  // margin-bottom: 15px;
  display: flex;
  // margin-bottom: 10px;
}

.avue-view {
  width: 100%;
  box-sizing: border-box;
}

.avue-crud {

  .avue-crud__header {
    background: transparent !important;
  }

  th.el-table-fixed-column--right {
    // background-color: #ffffff !important;
    // color: #000 !important;

    // .cell {
    //   color: #000000 !important;
    // }
  }

  .el-pagination.is-background .btn-prev:disabled,
  .el-pagination.is-background .btn-next:disabled {
    background-color: var(--el-pagination-button-bg-color)
  }

  .el-scrollbar__thumb {
    background: #3f9eff;
    opacity: 0.8;
  }

  // .el-table__body-wrapper tr td.el-table-fixed-column--right {
  //   background-color: #ffffff !important;
  // }

  .avue-crud__right {
    .el-button.is-circle {
      color: #ffffff;
    }
  }

  .el-table-fixed-column--left .cell{
    color: #FFF !important;
  }

  .el-table__cell {
    padding: 0px !important;
    background: #F5F6F7;
    color: #ffffff;
    background: #1c1c1c !important;
    border: 1px solid #ffffff !important;
  }

  .table__cell {
    border: 1px solid #ffffff;
  }

  .el-table .cell {
    color: #ffffff;
    // padding: 0px !important;
  }

  .el-table tr {
    height: 50px;
  }

  .el-collapse-item:last-child {
    padding-top: 20px;
  }

  .avue-crud__header {
    line-height: 38px;
  }

  .hover-row,
  .el-form-item__label,
  .el-input__inner {
    color: #ffffff;
  }
}

.avue-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  text-align: center;

  .copyright {
    color: #666;
    font-size: 12px;
  }
}

.mac_bg {
  background-image: url("/img/bg.jpg");
  background-color: #000;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}


.custom-content-marker-header {
  width: 101npx;
  height: 42npx;
  background: #06b668;
  opacity: 1;
  border: 1px solid;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-image: linear-gradient(270deg,
      rgba(6.000000117346644, 182.00000435113907, 105.00000134110451, 1),
      rgba(255, 255, 255, 1),
      rgba(6.000000117346644, 182.00000435113907, 105.00000134110451, 1)) 1 1;
}

.custom-content-marker {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14npx;
  font-weight: 400;
  color: #ffffff;

  img {
    width: 43.21npx;
    height: 32.89npx;
  }
}

.battery-border {
  border: 2px solid #89dff5;
  border-radius: 10px;
}

// ele样式覆盖
@import './element-ui.scss';
// 顶部右侧显示
@import './top.scss';
// 导航标签
@import './tags.scss';
// 工具类函数
@import './mixin.scss';
// 侧面导航栏
@import './sidebar.scss';
//主题
@import './theme/index.scss';
//通用配置
@import './normalize.scss';
//图标配置
@import './iconfont.scss';
//登录样式
@import "./login.scss";
//适配
@import './media.scss';
//滚动条样式
@include scrollBar;
