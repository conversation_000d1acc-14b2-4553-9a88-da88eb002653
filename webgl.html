<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<style>
    * {
        padding: 0px;
        margin: 0px;
        border: 0px;
        box-sizing: border-box;
    }

    #myCanvas {
        width: 100%;
        height: 100%;
    }
</style>

<body>

    <script>

        const canvas = document.createElement('canvas')
        canvas.width = canvas.height = 300
        document.body.append(canvas)
        const gl = canvas.getContext('webgl')

        gl.viewport(0, 0, gl.canvas.width, gl.canvas.height)
        // 设置 webgl 视口，将 -1 到 1 映射为 canvas 上的坐标

        const vertexShader = gl.createShader(gl.VERTEX_SHADER) // 创建一个顶点着色器
        gl.shaderSource(vertexShader, `
            attribute vec4 a_position;

            void main() {
                gl_Position = a_position; // 设置顶点位置
            }
        `) // 编写顶点着色器代码
        gl.compileShader(vertexShader) // 编译着色器

        const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER) // 创建一个片元着色器
        gl.shaderSource(fragmentShader, `
            precision mediump float;
            uniform vec4 u_color;

            void main() {
                gl_FragColor = u_color; // 设置片元颜色
            }
        `) // 编写片元着色器代码
        gl.compileShader(fragmentShader) // 编译着色器

        const program = gl.createProgram() // 创建一个程序
        gl.attachShader(program, vertexShader) // 添加顶点着色器
        gl.attachShader(program, fragmentShader) // 添加片元着色器
        gl.linkProgram(program) // 连接 program 中的着色器

        gl.useProgram(program) // 告诉 webgl 用这个 program 进行渲染

        const colorLocation = gl.getUniformLocation(program, 'u_color') // 获取 u_color 变量位置
        gl.uniform4f(colorLocation, 0.93, 0, 0.56, 1) // 设置它的值

        const positionLocation = gl.getAttribLocation(program, 'a_position')
        // 获取 a_position 位置
        const positionBuffer = gl.createBuffer()
        // 创建一个顶点缓冲对象，返回其 ID，用来放三角形顶点数据，
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer)
        // 将这个顶点缓冲对象绑定到 gl.ARRAY_BUFFER
        // 后续对 gl.ARRAY_BUFFER 的操作都会映射到这个缓存
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
            0, 0.5,
            0.5, 0,
            -0.5, -0.5,
            0.5, 1,
            0.5, 1,
            0.5, 1,
        ]),  // 三角形的三个顶点
            // 因为会将数据发送到 GPU，为了省去数据解析，这里使用 Float32Array 直接传送数据
            gl.STATIC_DRAW // 表示缓冲区的内容不会经常更改
        )
        // 将顶点数据加入的刚刚创建的缓存对象

        gl.enableVertexAttribArray(positionLocation);
        // 开启 attribute 变量，使顶点着色器能够访问缓冲区数据
        gl.vertexAttribPointer( // 告诉 OpenGL 如何从 Buffer 中获取数据
            positionLocation, // 顶点属性的索引
            3, // 组成数量，必须是1，2，3或4。我们只提供了 x 和 y
            gl.FLOAT, // 每个元素的数据类型
            false, // 是否归一化到特定的范围，对 FLOAT 类型数据设置无效
            0, // stride 步长 数组中一行长度，0 表示数据是紧密的没有空隙，让OpenGL决定具体步长
            0 // offset 字节偏移量，必须是类型的字节长度的倍数。
        )

        gl.clearColor(0, 1, 1, 1) // 设置清空颜色缓冲时的颜色值
        gl.clear(gl.COLOR_BUFFER_BIT) // 清空颜色缓冲区，也就是清空画布

        gl.drawArrays( // 从数组中绘制图元
            gl.TRIANGLES, // 渲染三角形
            0,  // 从数组中哪个点开始渲染
            3   // 需要用到多少个点，三角形的三个顶点
        )

    </script>
</body>

</html>