import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/base/surveyinfo/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/api/base/surveyinfo/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/base/surveyinfo/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/api/base/surveyinfo/submit',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/base/surveyinfo/submit',
        method: 'post',
        data: row
    })
}

