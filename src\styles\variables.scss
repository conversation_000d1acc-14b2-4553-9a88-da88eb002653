$sidebar_width: 230px;
$sidebar_collapse: 60px;
$top_height: 60px;

.body-scrollbar::-webkit-scrollbar {
  width: 4npx;
  background: #F3F7FF;
  border-radius: 2px 2px 2px 2px;
  opacity: 0.05;
}

/*滚动条*/
.body-scrollbar::-webkit-scrollbar-thumb {
  background-color: #CECECE;
}

:root {
  --el-color-default: red;
  // --el-color-primary: red;
  --el-menu-text-color: #ffffff;
  --el-button-text-color: #ffffff;
  // --el-text-color-regular: #ffffff;
  // --el-fill-color-light: rgb(196, 7, 7);
  --el-fill-color-light: #1c1c1c;
  --el-color-primary-dark-2: rgb(196, 7, 7);
  --el-color-primary-light-3: rgb(211, 25, 25);
  --el-color-primary-light-9: rgb(136, 142, 151);
  --el-menu-item-height: 42px;
  --el-menu-item-font-size: 18px;
  --el-menu-border-color: red;
  --el-fill-color-blank: transparent;

  .el-card {
    --el-card-padding: 16px;
  }

  .avue-crud__pagination {
    padding: 25px 0px 0px 0px;
  }

  // --el-menu-base-level-padding: 15px
  // --el-input-border-color: rgba(0, 0, 0, 0.08);
  // --el-table-border-color: rgba(0, 0, 0, 0.08);
  // --el-border-color-lighter: rgba(0, 0, 0, 0.08);
}
