<template>
  <div class="location-container">
    <div class="location-container-left battery-border">
      <div class="location-container-left-title">
        <div>客户列表</div>
      </div>
      <avue-tree
        ref="userThree"
        :option="treeOption"
        :data="treeData"
        v-model="treeForm"
        @node-click="treeNodeClick"
      >
      </avue-tree>
    </div>
    <div class="location-container-right battery-border">
      <el-form :inline="true" :model="queryForm" class="location-container-haeder">
        <el-form-item label="设备号码" prop="devicesn" style="width: 100%">
          <avue-select
            v-model="queryForm.devicesn"
            placeholder="请选择内容"
            type="tree"
            :filterable="true"
            :dic="devEquList"
          ></avue-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" style="width: 100%">
          <avue-date
            type="datetime"
            format="YYYY年MM月DD日 HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            v-model="queryForm.startTime"
            placeholder="请输入开始时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime" style="width: 100%">
          <avue-date
            type="datetime"
            format="YYYY年MM月DD日 HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            v-model="queryForm.endTime"
            placeholder="请输入结束时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">加载轨迹</el-button>
        </el-form-item>
        <!-- <el-form-item>
          <el-button type="primary" @click="handleStart">播放</el-button>
        </el-form-item> -->
      </el-form>
      <div class="AlarmapContainer" id="AlarmapContainer"></div>
    </div>
  </div>
</template>

<script>
import { AMapLoaderInitMap, AMapGPS } from "@/utils/amap";
import { getGPSData } from "@/api/device/card";
import { getBladeSystemDeptThree, getBatterList} from "@/api/battery/equipment.js";

export default {
  data() {
    return {
      lineArr: [],
      checkboxGroup1: ["全部"],
      cities: ["全部", "到期设备", "离线设备"],
      marker: null,
      treeData: [],
      treeForm: {},
      treeData: [],
      treeOption: {
        defaultExpandAll: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        props: {
          label: "title",
          value: "id",
        },
        formOption: {
          labelWidth: 100,
          column: [
            {
              label: "运营商名称",
              prop: "deptName",
              rules: [
                {
                  required: true,
                  message: "请输入运营商名称",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "地区",
              prop: "regionId",
              type: "tree",
              parent: false,
              dicQuery: {
                topCode: "00",
              },
              props: {
                label: "title",
                value: "id",
              },
              dicUrl: "/blade-system/region/lazy-tree",
              dicFormatter: (res) => {
                return res.data.filter((item) => +item.id !== 0);
              },
            },
            {
              label: "登陆账号",
              prop: "account",
              span: 24,
              rules: [
                {
                  required: true,
                  message: "请输入登陆账号",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "登录密码",
              type: "password",
              prop: "password",
              rules: [
                {
                  required: true,
                  message: "请输入登录密码",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "确认密码",
              type: "password",
              prop: "newPassword1",
              rules: [
                {
                  required: true,
                  message: "请输入确认密码",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "联系人",
              prop: "realName",
              rules: [
                {
                  required: true,
                  message: "请输入联系人",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "联系电话",
              prop: "phone",
            },
            {
              label: "身份证号码",
              prop: "idCard",
            },
            {
              label: "详细地址",
              prop: "detailAdr",
            },
          ],
        },
      },
      option: {
        column: [
          {
            label: "设备名称",
            prop: "deviceName",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入设备名称",
                trigger: "blur",
              },
            ],
          },

          {
            label: "设备状态",
            prop: "deviceSwitch",
            type: "select",

            dicUrl: "/blade-system/dict-biz/getDictBizData?code=currentState",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择设备状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "电池编码",
            prop: "deviceCode",

            display: false,
            rules: [
              {
                required: true,
                message: "请输入设备编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用时长",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入使用时长",
                trigger: "blur",
              },
            ],
          },
          {
            label: "物联网卡号",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入物联网卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "所在位置",
            prop: "simCode",
            rules: [
              {
                required: false,
                message: "请输入所在位置",
                trigger: "blur",
              },
            ],
          },
          {
            label: "状态",
            prop: "deviceDesc",
            hide: true,
            type: "textarea",
            rules: [
              {
                required: false,
                message: "请输入状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "设备名称",
            prop: "deviceName",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入设备名称",
                trigger: "blur",
              },
            ],
          },

          {
            label: "设备状态",
            prop: "deviceSwitch",
            type: "select",

            dicUrl: "/blade-system/dict-biz/getDictBizData?code=currentState",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择设备状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "电池编码",
            prop: "deviceCode",

            display: false,
            rules: [
              {
                required: true,
                message: "请输入设备编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用时长",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入使用时长",
                trigger: "blur",
              },
            ],
          },
          {
            label: "物联网卡号",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入物联网卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "所在位置",
            prop: "simCode",
            rules: [
              {
                required: false,
                message: "请输入所在位置",
                trigger: "blur",
              },
            ],
          },
          {
            label: "状态",
            prop: "deviceDesc",
            hide: true,
            type: "textarea",
            rules: [
              {
                required: false,
                message: "请输入状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "设备名称",
            prop: "deviceName",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入设备名称",
                trigger: "blur",
              },
            ],
          },

          {
            label: "设备状态",
            prop: "deviceSwitch",
            type: "select",

            dicUrl: "/blade-system/dict-biz/getDictBizData?code=currentState",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择设备状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "电池编码",
            prop: "deviceCode",

            display: false,
            rules: [
              {
                required: true,
                message: "请输入设备编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用时长",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入使用时长",
                trigger: "blur",
              },
            ],
          },
          {
            label: "物联网卡号",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入物联网卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "所在位置",
            prop: "simCode",
            rules: [
              {
                required: false,
                message: "请输入所在位置",
                trigger: "blur",
              },
            ],
          },
          {
            label: "状态",
            prop: "deviceDesc",
            hide: true,
            type: "textarea",
            rules: [
              {
                required: false,
                message: "请输入状态",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      map: null,
      queryForm: {
        devicesn: this.$route.query.devicesn,
      },
      devEquList: [],
    };
  },
  mounted() {
    this.init();
    this.initAMapLoaderInitMap();
  },
  methods: {
    /**
     * 初始化
     **/
    init() {
      this.getTreeData();
      getBatterList().then((res) => {
        var data = res.data.data;
        data.forEach((item) => {
          this.devEquList.push({
            label: item.equNo,
            value: item.equNo,
          });
        });
        if (!this.queryForm.devicesn) {
          this.queryForm.devicesn = data[0];
        }

        this.queryForm.startTime = new Date().toISOString().split("T")[0] + " 00:00:00";
        this.queryForm.endTime = new Date().toISOString().split("T")[0] + " 23:59:59";
        this.handleQuery();
      });
    },
    getTreeData() {
      getBladeSystemDeptThree().then((res) => {
        this.treeData = res.data.data;
      });
    },
    /**
     * 播放轨迹
     **/
    handleStart() {
      this.marker.moveAlong(this.lineArr, 1500);
    },
    /**
     * 加载轨迹
     **/
    handleQuery() {

      if (this.queryForm.startTime > this.queryForm.endTime) {
        this.$message.warning("选择开始时间不能大于结束时间");
        return;
      }
      //清除地图的所有覆盖物
      this.map.clearMap();
      //电池经纬度,也用于画线
      this.lineArr = [];

      this.$nextTick(() => {
        getGPSData(
          this.queryForm.devicesn,
          this.queryForm.startTime,
          this.queryForm.endTime
        ).then((res) => {
          var data = res.data.data;
          let lineArr = data.map((items) => {
            return [items.lon, items.lat];
          });
          this.lineArr = lineArr;
          this.$nextTick(() => {
            if (this.map) {
              // this.map.clearMap();

              let startIcon = new AMap.Icon({
                size: new AMap.Size(25, 34),
                image: "/img/货车.png",
                size: new AMap.Size(57.125, 25),
                imageSize: new AMap.Size(57.125, 25),
              });

              this.marker = new AMap.Marker({
                map: this.map,
                position: lineArr[0],
                icon: startIcon,
                autoRotation: true,
                offset: new AMap.Pixel(-26, -13),
                angle: -90,
              });

              // 绘制轨迹
              let polyline = new AMap.Polyline({
                map: this.map,
                path: lineArr,
                showDir: true,
                strokeColor: "#28F", //线颜色
                strokeWeight: 6, //线宽
              });

              let passedPolyline = new AMap.Polyline({
                map: this.map,
                strokeColor: "#AF5", //线颜色
                strokeWeight: 6, //线宽
              });

              this.marker.on("moving", (e) => {
                passedPolyline.setPath(e.passedPath);
              });

              this.map.setFitView();
              this.$nextTick(() => {
                setTimeout(() => {
                  this.handleStart();
                }, 1000);
              });
            }
          });
        });
      });
    },

    initAMapLoaderInitMap() {
      AMapLoaderInitMap({
        afterRequest: (AMap) => {
          this.map = new AMap.Map("AlarmapContainer", {
            //设置地图容器id
            resizeEnable: true,
            zoom: 17, //初始化地图级别
            center: [116.397428, 39.90923],
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
  :root {
    --el-text-color-regular: #000000;
  }
  .location-container {
    width: 100%;
    color: #ffffff;
    display: flex;
    justify-content: space-between;
    padding: 5px;

    &-haeder {
      width: 340px;
      position: absolute;
      top: 30px;
      left: 30px;
      z-index: 2;
      background-color: #ffffff;
      padding: 20px 20px;

      :deep(.el-form-item__label) {
        color: #000;
      }
      :deep(.el-input__inner) {
        color: #000;
      }
    }
    &-left {
      width: 20%;
      height: 100%;
      padding: 5px;
      &-title {
        display: flex;
        justify-content: space-between;
        padding: 10px;
      }
    }
    &-right {
      width: 79.5%;
      height: 100%;
      position: relative;
      padding: 10px;
    }
  }
  .AlarmapContainer {
    width: 100%;
    height: 100%;
  }
</style>
