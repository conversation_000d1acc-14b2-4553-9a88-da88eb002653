<template>
  <div class="cockpit">
    <div class="cockpit-header">
      <div class="cockpit-header-row">
        <div class="cockpit-header-row-left">
          <img src="/img/cockpit/logo.png"/>
        </div>
        <div class="cockpit-header-block" @click="handleBack">
          <img src="/img/cockpit/home.png"/>
        </div>
        <div class="cockpit-header-row-center">智慧蚊虫数据平台</div>
        <div class="cockpit-header-row-right">
          <div class="cockpit-header-row-right-count">{{ getTime }}</div>
          <img src="/img/cockpit/weather.png"/>
          <div>多云</div>
          <div>{{ getDD }}</div>
          <div>{{ getMMDD }}</div>
          <img @click="handleScreen" src="/img/cockpit/screen.png"/>
        </div>
      </div>
    </div>
    <div class="cockpit-container">
      <div class="welMap" id="welMap"></div>
      <div class="cockpit-container-left">
        <div class="cockpit-container-left-t1">
          <div class="cockpit-container-left-header cockpit-container-left-header-max">
            <img
                class="cockpit-container-left-header-icon"
                src="/img/cockpit/left1.png"
            />
            <div class="cockpit-container-left-header-text">灭蚊数据总量变化</div>
          </div>

          <div
              class="cockpit-container-block"
              style="overflow: hidden"
              id="totalEchats"
          ></div>
        </div>
        <div class="cockpit-container-left-t2">
          <div class="cockpit-container-left-header cockpit-container-left-header-min">
            <img
                class="cockpit-container-left-header-icon"
                src="/img/cockpit/left2.png"
            />
            <div class="cockpit-container-left-header-text">设备信息</div>
          </div>
          <div class="cockpit-container-block">
            <div class="cockpit-container-block-table">
              <TableView :dataSource="deviceDataSource" :column="deviceColumn"/>
            </div>
          </div>
        </div>
        <div class="cockpit-container-left-t3">
          <div class="cockpit-container-left-header cockpit-container-left-header-min">
            <img
                class="cockpit-container-left-header-icon"
                src="/img/cockpit/left3.png"
            />
            <div class="cockpit-container-left-header-text">消杀建议</div>
          </div>
          <div class="cockpit-container-block">
            <div class="cockpit-container-block-table">
              <TableView :dataSource="deviceDataSource1" :column="deviceColumn1"/>
            </div>
          </div>
        </div>
      </div>
      <div class="cockpit-container-right">
        <div class="cockpit-container-right-t1">
          <div class="cockpit-container-right-t1-block">
            <div class="cockpit-container-right-t1-block-row" v-for="items in deviceArr">
              <img src="/img/cockpit/device.png"/>
              <div class="cockpit-container-right-t1-block-row-text">
                <div>{{ items.label }}</div>
                <div>
                  <avue-count-up :end="deviceSumData[items.key]"></avue-count-up
                  >
                  <span>台</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="cockpit-container-right-t2">
          <div class="cockpit-container-right-header cockpit-container-right-header-max">
            <img
                class="cockpit-container-left-header-icon"
                src="/img/cockpit/right1.png"
            />
            <div class="cockpit-container-left-header-text">高危蚊虫密度变化</div>
          </div>
          <div
              class="cockpit-container-block"
              style="overflow: hidden"
              id="densityEchats"
          ></div>
        </div>
        <div class="cockpit-container-right-t3">
          <div class="cockpit-container-right-header cockpit-container-right-header-min">
            <img
                class="cockpit-container-left-header-icon"
                src="/img/cockpit/right2.png"
            />
            <div class="cockpit-container-left-header-text">告警信息</div>
          </div>
          <div class="cockpit-container-block">
            <div class="cockpit-container-block-table">
              <TableView :dataSource="deviceDataSource2" :column="deviceColumn2">
                <template #a4>
                  <div class="cockpit-container-block-deviceColumn2">
                    <span>异常</span>
                    <img src="/img/cockpit/navigation.png"/>
                  </div>
                </template>
              </TableView>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import {initEchartsTotal, initDensityEchats} from "./ecahts";
import {AMapLoaderInitMap} from "@/utils/amap";
import TableView from "./table.vue";
// import {
//   getBaseDeviceinfoSumData,
//   getBaseAlarminfoPage,
//   getBaseProjectinfoExtinguishByDate,
//   getBaseProjectinfoHighRiskMosquitores,
//   getBaseProjectinfoPage,
// } from "@/api/common";
import {getList as getDeviceList, getTreeData} from "@/api/project/list";
import {fullscreenToggel, listenfullscreen} from "utils/util";

let myDensityEchats = null;
let myEchartsTotal = null;
let TimeSetInterval = null;
import getBaseData from "../wel/data";

export default {
  components: {
    TableView,
  },
  data() {
    return {
      timeNum: new Date().getTime(),
      isFullScren: false,
      deviceDataSource: [],
      deviceDataSource1: [],
      deviceColumn: [
        {title: "序号", dataIndex: "index", width: 60},
        {title: "项目名称", dataIndex: "projectName", align: "left"},
        {title: "设备总量", dataIndex: "a3", width: 80},
      ],
      deviceColumn1: [
        {title: "序号", dataIndex: "index", width: 30},
        {title: "项目名称", dataIndex: "projectName", width: 120, align: "left"},
        {title: "建议内容", dataIndex: "cmf1", width: 120, align: "left"},
      ],
      deviceColumn2: [
        {title: "序号", dataIndex: "index", width: 50},
        {title: "设备名称", dataIndex: "alarmDevice", width: 100, align: "left"},
        {title: "告警内容", dataIndex: "alarmName", width: 100, align: "left"},
        {title: "位置导航", dataIndex: "a4", width: 80, slot: true, align: "left"},
      ],
      deviceDataSource2: [],
      deviceSumData: {
        deviceTotal: 0,
        onlineTotal: 0,
        faultsTotal: 0,
        offlineTotal: 0,
      },
      deviceArr: [
        {
          label: "设备总量",
          key: "deviceTotal",
        },
        {
          label: "在线总量",
          key: "onlineTotal",
        },
        {
          label: "当前故障设备",
          key: "faultsTotal",
        },
        {
          label: "离线总量",
          key: "offlineTotal",
        },
      ],
    };
  },
  //页面销毁
  destroyed() {
    window.clearInterval(TimeSetInterval);
  },
  beforeUnmount() {
    window.clearInterval(TimeSetInterval);
    if (myEchartsTotal) {
      myEchartsTotal.dispose();
      myEchartsTotal = null;
    }
    if (myDensityEchats) {
      myDensityEchats.dispose();
      myDensityEchats = null;
    }
  },
  computed: {
    getDD() {
      return this.$dayjs(this.timeNum).format("dddd");
    },
    getMMDD() {
      return this.$dayjs(this.timeNum).format("MM月DD日");
    },
    getTime() {
      return this.$dayjs(this.timeNum).format("HH:mm:ss");
    },
  },
  mounted() {
    AMapLoaderInitMap({
      afterRequest: (AMap) => {
        var position = new AMap.LngLat(116.397428, 39.90923);
        this.map = new AMap.Map("welMap", {
          //设置地图容器id
          viewMode: "3D", //是否为3D地图模式
          zoom: 8, //初始化地图级别
          resizeEnable: true,
        });
        this.map.setMapStyle("amap://styles/darkblue");

        getTreeData(1, 1000).then((res) => {
          res.data.data.map((item) => {
            if (item.selectRange) {
              const markerContent = `<div class="custom-content-marker">
              <div class="custom-content-marker-header">${item.projectName}</div>
              <img src="/img/map-home.png">
              </div>`;
              let selectRange = JSON.parse(item.selectRange);
              const polygon = new AMap.Polygon({
                path: selectRange,
              });
              // 计算多边形中心经纬度
              var path = polygon.getPath();
              var centerLng = 0;
              var centerLat = 0;

              for (var i = 0; i < path.length; i++) {
                centerLng += path[i].getLng();
                centerLat += path[i].getLat();
              }

              centerLng /= path.length;
              centerLat /= path.length;

              const marker = new AMap.Marker({
                position: [centerLng, centerLat],
                // 将 html 传给 content
                content: markerContent,
                // 以 icon 的 [center bottom] 为原点
                offset: new AMap.Pixel(-13, -30),
              });
              // 将 markers 添加到地图
              this.map.add(marker);
            }
          });
        });
      },
    });
    this.initEchartsTotal();
    this.initDensityEchats();

    getBaseDeviceinfoSumData().then((res) => {
      this.deviceSumData = res.data.data;
    });
    getDeviceList(1, 1000).then((res) => {
      res.data.data.records.map((item, index) => {
        item["index"] = index + 1;
        item["a3"] = parseInt(Math.random() * 10);
      });
      this.deviceDataSource = res.data.data.records;
    });
    getBaseAlarminfoPage({current: 1, size: 9999}).then((res) => {
      res.data.data.records.map((item, index) => {
        item["index"] = index + 1;
      });
      this.deviceDataSource2 = res.data.data.records;
    });
    getBaseProjectinfoPage({current: 1, size: 9999, highRisk: 3}).then((res) => {
      res.data.data.records.map((item, index) => {
        item["index"] = index + 1;
      });
      this.deviceDataSource1 = res.data.data.records;
    });

    TimeSetInterval = setInterval(() => {
      this.timeNum = new Date().getTime();
    }, 1000);
  },
  methods: {
    handleBack() {
      this.$router.push({
        path: "/",
      });
    },
    setScreen() {
      this.$store.commit("SET_FULLSCREN");
    },
    handleScreen() {
      fullscreenToggel();
    },
    initEchartsTotal() {
      if (!myEchartsTotal) {
        myEchartsTotal = echarts.init(document.getElementById("totalEchats"));
      }
      const relust = getBaseData({
        dateLen: 2,
        dateType: 2,
      });
      const relust1 = relust.map(
          (item) => item.date.split("-")[item.date.split("-").length - 1]
      );
      const relust2 = relust.map((item) => item.extinguishNum);
      // 绘制图表
      myEchartsTotal.setOption(initEchartsTotal(relust2, relust1), true);
      window.addEventListener("resize", () => {
        myEchartsTotal.resize();
      });
      // 基于准备好的dom，初始化echarts实例
      // myDensityEchats = echarts.init(document.getElementById("totalEchats"));
      // getBaseProjectinfoExtinguishByDate({
      //   dateType: 2,
      //   dateLen: 10,
      // }).then((res) => {
      //   let relust = res.data.data;
      //   const relust1 = relust
      //     .map((item) => item.date.split("-")[item.date.split("-").length - 1])
      //     .reverse();
      //   const relust2 = relust.map((item) => +item.extinguishNum);
      //   console.log(relust);
      //   // 绘制图表
      //   myChart.setOption(initEchartsTotal(relust2, relust1));
      // });
      // window.addEventListener("resize", () => {
      //   myChart.resize();
      // });
    },
    initDensityEchats() {
      if (!myDensityEchats) {
        myDensityEchats = echarts.init(document.getElementById("densityEchats"));
      }

      const relust = getBaseData({
        dateLen: 2,
        dateType: 2,
      });
      const relust1 = relust.map(
          (item) => item.date.split("-")[item.date.split("-").length - 1]
      );
      const relust2 = relust.map((item) => item.extinguishNum);
      // 绘制图表
      myDensityEchats.setOption(initDensityEchats(relust2, relust1, 2), true);
      window.addEventListener("resize", () => {
        myDensityEchats.resize();
      });
      // 基于准备好的dom，初始化echarts实例
      // var myChart = echarts.init(document.getElementById("densityEchats"));
      // getBaseProjectinfoHighRiskMosquitores({
      //   dateType: 2,
      //   dateLen: 10,
      // }).then((res) => {
      //   let relust = res.data.data;
      //   console.log("getBaseProjectinfoHighRiskMosquitores");
      //   console.log(relust);
      //   const relust1 = relust
      //     .map((item) => item.date.split("-")[item.date.split("-").length - 1])
      //     .reverse();
      //   const relust2 = relust.map((item) => item.extinguishNum);
      //   // 绘制图表
      //   myChart.setOption(initDensityEchats(relust2, relust1));
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.welMap {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 2;
  top: 0px;
  right: 0px;
}

* {
  box-sizing: border-box;
}

.cockpit {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &-postion {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
  }

  &-header,
  &-container {
    width: 100%;
    position: relative;
  }

  &-header {
    height: 11%;
    background: url(/img/cockpit/header.png);
    background-size: 100% 100%;
    // background-color: #000000;
    background-color: #1c1c1c;

    &-block {
      width: 30 npx;
      height: 30 npx;
      background-color: #ffffff17;
      border-radius: 40 npx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: absolute;
      left: 5 npx;
      top: 50%;
      transform: translate(0px, -50%);

      img {
        width: 60%;
        height: 60%;
      }
    }

    &-row {
      display: flex;
      justify-content: space-between;
      padding: 0px 40 npx;
      position: relative;

      &-left,
      &-right {
        height: 71 npx;
        display: flex;
        align-items: center;
      }

      &-left {
        position: relative;

        img {
          width: 148.88 npx;
          height: 40 npx;
        }
      }

      &-center {
        height: 96 npx;
        font-size: 50 npx;
        font-weight: 600;
        color: #ffffff;
        position: absolute;
        left: 50%;
        top: 0px;
        transform: translate(-50%, 0px);
        display: flex;
        align-items: center;
      }

      &-right {
        display: flex;
        align-items: center;
        font-size: 16 npx;
        font-weight: 400;
        color: #ffffff;

        &-count {
          font-size: 24 npx;
          font-weight: 500;
          color: #ffffff;
          margin-right: 30 npx;
        }

        img {
          width: 32 npx;
          height: 32 npx;
          cursor: pointer;
        }
      }

      &-right div:nth-child(2) {
        margin-right: 10 npx;
      }

      &-right div:nth-child(3) {
        margin-right: 10 npx;
      }

      &-right div:nth-child(4) {
        margin-right: 13 npx;
      }

      &-right div:nth-child(5) {
        margin-right: 20 npx;
      }
    }
  }

  &-container {
    height: 89%;
    // padding: 30npx;
    display: flex;
    justify-content: space-between;
    position: relative;

    &-block {
      flex: 1;
      background-color: #1b1c21;
      padding: 10 npx 0px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;

      &-table {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
      }

      &-deviceColumn2 {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #ff3939;
        justify-content: center;

        img {
          width: 16 npx;
          height: 16 npx;
          margin-left: 4 npx;
        }
      }
    }

    &-left,
    &-right {
      width: 22.58064516129032%;
      height: 97%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: absolute;
      top: 0px;
      z-index: 3;
    }

    &-left {
      left: 30 npx;

      &-header {
        display: flex;
        align-items: center;
        padding-left: 16 npx;

        &-icon {
          width: 16 npx;
          height: 16 npx;
          margin-right: 9 npx;
        }

        &-text {
          font-size: 15px;
          font-weight: 500;
          color: #ffffff;
        }
      }

      &-header-max {
        width: 100%;
        height: 36 npx;
        background: url(/img/cockpit/title_max.png);
        background-size: 100% 100%;
      }

      &-header-min {
        width: 100%;
        height: 36 npx;
        background: url(/img/cockpit/title_min.png);
        background-size: 100% 100%;
      }

      &-t1,
      &-t2,
      &-t3 {
        width: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      &-t1 {
        height: 34%;
      }

      &-t2 {
        height: 30%;
      }

      &-t3 {
        height: 30%;
      }
    }

    &-center {
      width: 52.1505376344086%;

      img {
        width: 100%;
      }
    }

    &-right {
      right: 30 npx;
      display: flex;
      align-items: center;

      &-header {
        display: flex;
        align-items: center;
        justify-content: end;
        padding-right: 16 npx;

        &-icon {
          width: 16 npx;
          height: 16 npx;
          margin-right: 9 npx;
        }

        &-text {
          font-size: 15px;
          font-weight: 500;
          color: #ffffff;
        }
      }

      &-header-max {
        width: 100%;
        height: 36 npx;
        background: url(/img/cockpit/title_right_max.png);
        background-size: 100% 100%;
      }

      &-header-min {
        width: 100%;
        height: 36 npx;
        background: url(/img/cockpit/title_right_min.png);
        background-size: 100% 100%;
      }

      &-t1,
      &-t2,
      &-t3 {
        width: 100%;
        display: flex;
        flex-direction: column;
        // margin-bottom: 32npx;
      }

      &-t1 {
        height: 24%;

        &-block {
          flex: 1;
          display: flex;
          flex-wrap: wrap;

          &-row {
            width: 50%;
            height: 50%;
            display: flex;
            // justify-content: center;
            align-items: center;
            padding-left: 30px;

            img {
              width: 50 npx;
              height: 50 npx;
              margin-right: 14 npx;
            }

            &-text div:nth-child(1) {
              font-size: 14 npx;
              font-weight: 400;
              color: #ffffff;
            }

            &-text div:nth-child(2) {
              display: flex;
              align-items: center;

              span:nth-child(1) {
                font-size: 20 npx;
                font-weight: bold;
                color: #07b667;
                text-shadow: 0px 1px 11px rgba(7, 182, 103, 0.5);
              }

              span {
                font-size: 14 npx;
                font-weight: 400;
                color: #ffffff;
                margin-left: 4 npx;
              }
            }
          }
        }
      }

      &-t2 {
        height: 35%;
      }

      &-t3 {
        height: 35%;
      }
    }
  }
}
</style>
