import request from '@/axios';


export const equipmentAdd = (row) => {
    return request({
        url: '/api/blade-system/user/submitCustom',
        method: 'post',
        data: row
    })
}

export const getBladeSystemDeptThree = (row) => {
    return request({
        url: '/api/blade-system/dept/tree',
        method: 'get',
        params: row
    })
}

export const deleteBladeSystemUserRemoveCustom = (row) => {
    return request({
        url: '/api/blade-system/user/removeCustom',
        method: 'post',
        data: row
    })
}
export const resetUserPwd = (row) => {
    return request({
        url: '/api/blade-system/user/restPwd',
        method: 'post',
        data: row
    })
}
export const upBatterByDeptIds = (deptId, equIds) => {
    return request({
        url: '/api/blade-base/batterInfo/upBatterByDeptIds',
        method: 'post',
        params: {
          deptId,
          equIds
        }
    })
}

export const getBladeSystemDeptByDetai = (row) => {
    return request({
        url: '/api/blade-system/user/getByDetai',
        method: 'get',
        params: row
    })
}

export const getBatterByProvince = (province) => {
    return request({
        url: '/api/blade-base/batterInfo/getBatterByProvince',
        method: 'get',
        params: {
          province
        }
    })
}

export const getProvinceByGroup = () => {
    return request({
        url: '/api/blade-base/batterInfo/getProvinceByGroup',
        method: 'get'
    })
}

export const getBatterList = () => {
    return request({
        url: '/api/blade-base/batterInfo/getBatterList',
        method: 'get'
    })
}
export const upBatterInfo = (params) => {
    return request({
        url: '/api/blade-base/batterInfo/update',
        method: 'post',
        data:{
            ...params
        }
    })
}
//设备解绑
export const liftDeviceBind = (params) => {
    return request({
        url: '/api/blade-base/batterInfo/liftDeviceBind',
        method: 'post',
        data:{
            ...params
        }
    })
}

//下载小程序二维码
export const getExhCode = (equNo) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getExhCode',
        method: 'get',
       params: {
           equNo
       }
    })
}

export const getlastDevPage = (devicesn) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getlastDevPage',
        method: 'get',
        params: {
          devicesn
        }
    })
}

export const getLastDevInfo = (devicesn) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getLastDevInfo',
        method: 'get',
        params: {
          devicesn
        }
    })
}

export const getLastDevInfoOne = (devicesn) => {
    return request({
        url: '/api/iotdb/IotEquipSource/getLastDevInfo1',
        method: 'get',
        params: {
          devicesn
        }
    })
}

export const updateUserPremByDeptId = (user) => {
    return request({
        url: '/api/blade-system/user/updateUserPremByDeptId',
        method: 'post',
        data: user
    })
}

export const getUserInfoByDeptIdOrTitle = (deptId,title) => {
    return request({
        url: '/api/blade-system/user/getUserInfoByDeptIdOrTitle',
        method: 'get',
        params: {
            deptId,
            title
        }
    })
}
export const getUserByname = (user) => {
    return request({
        url: '/blade-system/user/userByname',
        method: 'post',
      data:user
    });
};
export const getAssociationUserInfo = (devicesn) => {
    return request({
        url: '/iotdb/IotEquipSource/getAssociationUserInfo',
        method: 'get',
        params: {
            devicesn
        }
    });
};

export const getAdventDeviceByDeptId = (params) => {
    return request({
        url: '/iotdb/IotEquipSource/getAdventDeviceByDeptId',
        method: 'get',
        params: {
            ...params
        }
    })
}

//客户维护页面
export const buildDeptTree = (row) => {
    return request({
        url: '/api/blade-system/dept/buildDeptTree',
        method: 'get',
        params: row
    })
}
// 图片上传接口
export const uploadAvatar = (file) => {
    return request({
        url: '/blade-resource/oss/endpoint/put-file',
        method: 'post',
        // 去掉手动设置的 headers
         headers: { 'Content-Type': 'multipart/form-data' },
        data: file
    });
};

//生成设备编号二维码
export const generateQrCode = (params) => {
    return request({
        url: '/api/blade-base/batterInfo/generateQrCode',
        method: 'get',
        params: params,
        responseType: 'blob' // 关键：指定响应类型为二进制
    })
}
