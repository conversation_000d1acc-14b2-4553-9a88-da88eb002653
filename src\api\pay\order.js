import request from "@/axios";


/**
 * 全部订单跟待操作订单为同一个接口 根据 orderStatus区分 需要传递1
 * @param params
 * @returns {AxiosPromise}
 */
export const getOrderPages = (params) => {
    return request({
        url: '/order/getOrderPages',
        method: 'get',
        params: {
            ...params
        }
    })
}

export const getOrderPagesStutas = () => {
    return request({
        url: '/order/callback',
        method: 'POST',
    })
}

//获取设备续费到账详情接口
export const getBatterRenewRecordPages = (params) => {
    return request({
        url: '/renewRecord/getBatterRenewRecordPages',
        method: 'get',
        params: params
    })
}
//确认续费接口
export const submitDeviceRenew = (data) => {
    return request({
        url: '/order/submitDeviceRenew',
        method: 'post',
        data: data
    })
}



