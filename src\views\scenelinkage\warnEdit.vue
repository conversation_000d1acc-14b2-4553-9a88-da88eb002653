<template>
  <div class="container">
    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="dialogVisible"
        width="1050px"
        @close="closeDialog"
        title="编辑规则"
        >
        <div class="dialogContent">
          <el-form ref="form" :model="form" label-width="120px" :inline="true">
            <div class="title">触发器</div>
            <el-row :gutter="12" class="item" v-for="(item, index) in form.jsondata.listeners" :key="index">
              <div class="name">设备触发器{{ index+1 }}</div>
              <el-col :span="6">
                <el-select v-model="form.jsondata.listeners[index].type" clearable placeholder="请选择触发器类型">
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].type == 1" :span="6">
                <el-select v-model="form.jsondata.listeners[index].projectId" clearable placeholder="请选择项目">
                  <el-option
                    label="所有项目"
                    value=""
                  />
                  <el-option
                    v-for="item in projectList"
                    :key="item.id"
                    :label="item.projectName"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].type == 1" :span="6">
                <el-select v-model="form.jsondata.listeners[index].productId" clearable placeholder="请选择产品">
                  <el-option
                    label="所有产品"
                    value=""
                  />
                  <el-option
                    v-for="item in productList"
                    :key="item.id"
                    :label="item.productName"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].type == 1" :span="6">
                <el-select v-model="form.jsondata.listeners[index].deviceId" clearable placeholder="请选择设备">
                  <el-option
                    label="所有设备"
                    value=""
                  />
                  <el-option
                    v-for="item in deviceList"
                    :key="item.id"
                    :label="item.deviceName"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].type == 1" :span="6">
                <el-select v-model="form.jsondata.listeners[index].triggerMode" clearable placeholder="请选择触发方式">
                  <el-option
                    v-for="item in triggerModeOptions"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].triggerMode == 1" :span="6">
                <el-select v-model="form.jsondata.listeners[index].property" clearable placeholder="请选择属性">
                  <el-option
                    v-for="item in propertyList"
                    :key="item.id"
                    :label="item.tslName"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].triggerMode == 1" :span="6">
                <el-select v-model="form.jsondata.listeners[index].comparator" clearable placeholder="请选择比较模式">
                  <el-option
                    v-for="item in comparatorOptions"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].triggerMode == 1" :span="6">
                <el-input v-model="form.jsondata.listeners[index].value" style="width: 200px" placeholder="请输入比较值" />
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].triggerMode == 2" :span="6">
                <el-select v-model="form.jsondata.listeners[index].event" clearable placeholder="请选择事件">
                  <el-option
                    v-for="item in eventOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].triggerMode == 3" :span="6">
                <el-select v-model="form.jsondata.listeners[index].fluctuate" clearable placeholder="请选择上下线">
                  <el-option
                    v-for="item in fluctuateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.listeners[index].type == 2" :span="6">
                <el-input v-model="form.jsondata.listeners[index].cron" style="width: 200px" placeholder="请输入Cron表达式" />
              </el-col>
            </el-row>
            <el-button @click="addTrigger">新增触发器</el-button>
            
            <div class="title mt-20">执行条件</div>
            <el-row :gutter="12" class="item" v-for="(item, index) in form.jsondata.filters" :key="index">
              <div class="name">执行条件{{ index+1 }}</div>
              <el-col :span="6">
                <el-select v-model="form.jsondata.filters[index].condition" clearable placeholder="请选择触发器类型">
                  <el-option
                    v-for="item in conditionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 1" :span="6">
                <el-select v-model="form.jsondata.filters[index].currentState" clearable placeholder="请选择设备状态">
                  <el-option
                    v-for="item in currentStateOptions"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 1" :span="6">
                <el-input v-model="form.jsondata.filters[index].timeLong" style="width: 200px" placeholder="请输入最小持续时长" />
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 2"  :span="6">
                <el-select v-model="form.jsondata.filters[index].projectId" clearable placeholder="请选择项目">
                  <el-option
                    label="所有项目"
                    value=""
                  />
                  <el-option
                    v-for="item in projectList"
                    :key="item.id"
                    :label="item.projectName"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 2" :span="6">
                <el-select v-model="form.jsondata.filters[index].productId" clearable placeholder="请选择产品">
                  <el-option
                    label="所有产品"
                    value=""
                  />
                  <el-option
                    v-for="item in productList"
                    :key="item.id"
                    :label="item.productName"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 2" :span="6">
                <el-select v-model="form.jsondata.filters[index].deviceId" clearable placeholder="请选择设备">
                  <el-option
                    label="所有设备"
                    value=""
                  />
                  <el-option
                    v-for="item in deviceList"
                    :key="item.id"
                    :label="item.deviceName"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 2" :span="6">
                <el-select v-model="form.jsondata.filters[index].property" clearable placeholder="请选择属性">
                  <el-option
                    v-for="item in propertyList"
                    :key="item.id"
                    :label="item.tslName"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 2" :span="6">
                <el-select v-model="form.jsondata.filters[index].comparator" clearable placeholder="请选择比较模式">
                  <el-option
                    v-for="item in comparatorOptions"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"
                  />
                </el-select>
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 2" :span="6">
                <el-input v-model="form.jsondata.filters[index].value" style="width: 200px" placeholder="请输入比较值" />
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 3" :span="6">
                <el-input v-model="form.jsondata.filters[index].value" style="width: 200px" placeholder="起始时间 YYYY-MM-DD hh:mm:ss" />
              </el-col>
              <el-col v-if="form.jsondata.filters[index].condition == 3" :span="6">
                <el-input v-model="form.jsondata.filters[index].value" style="width: 200px" placeholder="结束时间YYYY-MM-DD hh:mm:ss" />
              </el-col>
            </el-row>
            <el-button @click="addCondition">新增执行条件</el-button>
            <div class="title mt-20">执行动作</div>
            <el-row :gutter="12" class="item">
              <div class="name">执行动作1</div>
              <el-col>  
                <el-select v-model="form.jsondata.actions[0].inputData" clearable placeholder="请选择执行动作">
                  <el-option
                    v-for="item in actionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
            </el-row>
            <div class="btns">
              <el-button type="primary" @click="onSubmit">确认</el-button>
              <el-button @click="closeDialog">取消</el-button>
            </div>
          </el-form>
        </div>
    </el-dialog>
  </div>
</template>

<script>
import { update, getDetail } from "@/api/scenelinkage/warn";
import { getList as projectList } from '@/api/project/list';
import { getList as deviceList } from "@/api/device/list";
import { getList as tslList } from "@/api/device/tsl";
import { getList as productList } from "@/api/device/product";
import { getDictionary } from "@/api/system/dictbiz";
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
              jsondata: {
                listeners: [],
                filters: [],
                actions: [{
                  inputData: ''
                }]
              }
            },
            typeOptions: [{
              label: '设备触发',
              value: '1'
            }, {
              label: '定时触发',
              value: '2'
            }],
            currentStateOptions: [],
            triggerModeOptions: [],
            comparatorOptions: [],
            eventOptions: [],
            actionOptions: [{
              label: '告警输出',
              value: 1
            }],
            fluctuateOptions: [],
            conditionOptions: [{
              label: '设备状态持续时长判断',
              value: 1
            }, {
              label: '设备状态',
              value: 2
            }, {
              label: '时间范围',
              value: 3
            }],
            propertyList: [],
            page: {
              pageSize: 100,
              currentPage: 1,
              total: 0,
            },
            projectList: [],
            productList: [],
            deviceList: []
        }
    },
    methods: {
      async init(e) {
        this.form.id = e.id;
        this.form.sceneName = e.sceneName;
        if (e.id !== null) {
          getDetail(e.id).then(res => {
              const data = res.data.data;
              this.form.jsondata = JSON.parse(data.sceneJson);
          })
        }
        this.getProjectList(this.page);
        this.getProductList(this.page);
        this.getDeviceList(this.page);
        this.getpropertyList(this.page);
        this.currentStateOptions = await this.getDict("currentState");
        this.triggerModeOptions = await this.getDict("triggerMode");
        this.comparatorOptions = await this.getDict("comparatorMode");
        this.dialogVisible = true;
      },
      getpropertyList(page) {
        tslList(page.currentPage, page.pageSize, null).then(res => {
          this.propertyList = res.data.data.records;
        })
      },
      getProjectList(page) {
        projectList(page.currentPage, page.pageSize, null).then(res => {
          this.projectList = res.data.data.records;
        })
      },
      getProductList(page) {
        productList(page.currentPage, page.pageSize, null).then(res => {
          this.productList = res.data.data.records;
        })
      },
      getDeviceList(page) {
        deviceList(page.currentPage, page.pageSize, null).then(res => {
          this.deviceList = res.data.data.records;
        })
      },
      onSubmit () {
        update(this.form).then(res => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.closeDialog();
        })
      },
      async getDict(code) {
          let result = [];
          const query = {
              code: code
          }
          result = await getDictionary(query).then(res => {
              const data = res.data.data;
              return data;
          })
          return result;
      },
      openDialog(e) {
        this.dialogVisible = true;
      },
      closeDialog() {
        this.dialogVisible = false;
      },
      addTrigger() {
        this.form.jsondata.listeners.push({
          type: ''
        })
      },
      addCondition() {
        this.form.jsondata.filters.push({
          condition: ''
        })
      }
    }
}
</script>

<style lang="scss">
  .mt-20 {
    margin-top: 20px;
  }
  .Nubmer-viewForm {
    .dialogContent {
      padding: 20px;
      .title {
        font-size: 16px;
        padding-bottom: 5px;
      }
      .item {
        padding: 0px 0 0px 0;
        background-color: #F7F8FA;
        margin-bottom: 5px;
        .name {
          display: block;
          width: 100%;
          padding: 10px 10px;
        }
      }
      .btns {
        padding-top: 20px;
      }
    }

  }
</style>