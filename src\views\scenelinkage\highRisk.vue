<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
        :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave"
        @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
        @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
        <template #menu="{ size, row, index }">
            <el-button v-if="row.riskRuleStatus == 2" icon="el-icon-view" text type="primary" :size="size" @click="setStatus(row,1)">停动</el-button>
            <el-button v-else icon="el-icon-view" text type="primary" :size="size" @click="setStatus(row,2)">启动</el-button>
            <!-- <el-button text type="primary" :size="size">启动</el-button>
            <el-button text type="primary" :size="size">停止</el-button>
            <el-button text type="primary" :size="size">触发</el-button> -->
        </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove } from "@/api/scenelinkage/highrisk";
import { getDictionary } from "@/api/system/dictbiz";
import { mapGetters } from "vuex";
import common from '@/store/modules/common';
export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                searchShow: true,
                span: 24,
                dialogWidth: '40%',
                searchMenuSpan: 8,
                border: true,
                index: true,
                viewBtn: false,
                selection: true,
                dialogClickModal: false,
                menuWidth: '320px',
                column: [
                    {
                        label: "规则名称",
                        prop: "riskRuleName",
                        search: true,
                        rules: [{
                            required: true,
                            message: "请输入高危规则名称",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "项目名称",
                        prop: "projectId",
                        type: 'select',
                        dicUrl: '/api/base/projectinfo/page',
                        dicFormatter: (res => {
                            return res.data.records;
                        }),
                        props: {
                            value: 'id',
                            label: 'projectName',
                        },
                        rules: [{
                            required: true,
                            message: "请选择所属项目",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "高危阈值",
                        prop: "riskThreshold",
                        rules: [{
                            required: true,
                            message: "请输入高危阈值（个）",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "设备占比",
                        prop: "deviceRatio",
                        rules: [{
                            required: true,
                            message: "请输入设备占比（%）",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "高危判断",
                        prop: "riskDecide",
                        rules: [{
                            required: true,
                            message: "请输入高危判定（天）",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "高危撤销",
                        prop: "riskRevoke",
                        rules: [{
                            required: true,
                            message: "请输入高危撤销（天）",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "运行状态",
                        prop: "riskRuleStatus",
                        type: 'select',
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=runnStatus',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        dataType: 'number',
                        slot: true,
                        rules: [{
                            required: true,
                            message: "请选择运行状态",
                            trigger: "blur"
                        }]
                    },
                ]
            }
        }
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.dept_add, false),
                viewBtn: this.validData(this.permission.dept_view, false),
                delBtn: this.validData(this.permission.dept_delete, false),
                editBtn: this.validData(this.permission.dept_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        setStatus(row, val) {
            row.riskRuleStatus = val;
            update(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                console.log(error);
            });
        },
        rowSave(row, done, loading) {
            add(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        async getDict(code) {
            let result = [];
            const query = {
                code: code
            }
            result = await getDictionary(query).then(res => {
                const data = res.data.data;
                return data;
            })
            return result;
        },
    }
}
</script>

<style>

</style>