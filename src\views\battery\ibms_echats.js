

const gaugeOptions = (batterVol1, batterVol2) => {
    let data1 = [batterVol1, batterVol2];
    let nameList = [
        "总电压(V)",
        "电流(A)",
    ];
    let newData = data1.map((item, idx) => {
        return {
            value: item,
            // name: '师资力量',
            unit: "",
        };
    });

    let centers = {
        1: '52%',
        2: '84%'
    }

    const option = {
        grid: {},
        series: newData.map((item, idx) => {
            if (idx === 0) {
                return {
                    name: "",
                    type: "gauge",
                    radius: "98%",
                    splitNumber: 5,
                    data: [item],
                    center: [`20%`, "50%"],
                    splitLine: {
                        show: false,
                    },
                    progress: {
                        show: true,
                    },
                    itemStyle: {
                        color: "#FF8C00",
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            width: 10,
                            color: [
                                [item.value ? item.value / 100 : 0, "#FF8C00"],
                                [1, "#01A0FE"],
                            ],
                        },
                    },
                    axisLabel: {
                        show: false,
                        distance: -40,
                        color: "#888888",
                    },
                    detail: {
                        offsetCenter: [0, "65%"],
                        fontWeight: "bold",
                        formatter: function (value) {
                            return `{a|${value + item.unit}}\n{b|${nameList[idx]}}`;
                        },
                        rich: {
                            a: {
                                color: "#ffffff",
                                fontSize: 16,
                            },
                            b: {
                                fontSize: 14,
                                color: "#ffffff",
                            },
                        },
                    },
                };
            } else {
                return {
                    name: "",
                    type: "gauge",
                    radius: "98%",
                    splitNumber: 5,
                    data: [item],
                    center: [`${centers[idx]}%`, "50%"],
                    splitLine: {
                        show: false,
                    },
                    progress: {
                        show: true,
                    },
                    itemStyle: {
                        color: "#FF8C00",
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            width: 10,
                            color: [
                                [item.value ? item.value / 100 : 0, "#FF8C00"],
                                [1, "#01A0FE"],
                            ],
                        },
                    },
                    axisLabel: {
                        show: false,
                        distance: -40,
                        color: "#888888",
                    },
                    detail: {
                        offsetCenter: [0, "65%"],
                        fontWeight: "bold",
                        formatter: function (value) {
                            return `{a|${value + item.unit}}\n{b|${nameList[idx]}}`;
                        },
                        rich: {
                            a: {
                                color: "#ffffff",
                                fontSize: 16,
                            },
                            b: {
                                fontSize: 14,
                                color: "#ffffff",
                            },
                        },
                    },
                };
            }
        }),
    };

    return option
}

let legendOptions = [
    {
        "time": "2023-11-30 00:00:23",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "17.1",
        "batT": "16.1",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.8",
            "exT1": "17.1",
            "exT2": "16.9"
        }
    },
    {
        "time": "2023-11-30 00:10:23",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "17.1",
        "batT": "16.1",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.8",
            "exT1": "17.1",
            "exT2": "16.8"
        }
    },
    {
        "time": "2023-11-30 00:20:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "17.1",
        "batT": "16",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.8",
            "exT1": "17.1",
            "exT2": "16.8"
        }
    },
    {
        "time": "2023-11-30 00:30:24",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "17.1",
        "batT": "16",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.7",
            "exT1": "17.1",
            "exT2": "16.8"
        }
    },
    {
        "time": "2023-11-30 00:40:24",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "17",
        "batT": "16",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.7",
            "exT1": "17",
            "exT2": "16.8"
        }
    },
    {
        "time": "2023-11-30 00:50:23",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "17",
        "batT": "16",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.7",
            "exT1": "17",
            "exT2": "16.8"
        }
    },
    {
        "time": "2023-11-30 01:00:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "17",
        "batT": "16",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.7",
            "exT1": "17",
            "exT2": "16.8"
        }
    },
    {
        "time": "2023-11-30 01:10:23",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "17",
        "batT": "16",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.7",
            "exT1": "17",
            "exT2": "16.8"
        }
    },
    {
        "time": "2023-11-30 01:20:23",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "17",
        "batT": "16",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.7",
            "exT1": "17",
            "exT2": "16.8"
        }
    },
    {
        "time": "2023-11-30 01:30:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "17",
        "batT": "16",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.7",
            "exT1": "17",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 01:40:24",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "17",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.7",
            "exT1": "17",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 01:50:23",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "16.9",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.6",
            "exT1": "17",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 02:00:23",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "16.9",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.6",
            "exT1": "16.9",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 02:10:24",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "16.9",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.6",
            "exT1": "16.9",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 02:20:24",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "16.9",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.6",
            "exT1": "16.9",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 02:30:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.8",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.6",
            "exT1": "16.9",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 02:40:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.8",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.6",
            "exT1": "16.9",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 02:50:24",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "16.8",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.6",
            "exT1": "16.9",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 03:00:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.8",
        "batT": "15.8",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.6",
            "exT1": "16.9",
            "exT2": "16.7"
        }
    },
    {
        "time": "2023-11-30 03:10:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.8",
        "batT": "15.9",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.5",
            "exT1": "16.8",
            "exT2": "16.6"
        }
    },
    {
        "time": "2023-11-30 03:20:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.8",
        "batT": "15.8",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.5",
            "exT1": "16.8",
            "exT2": "16.6"
        }
    },
    {
        "time": "2023-11-30 03:30:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.8",
        "batT": "15.8",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.5",
            "exT1": "16.8",
            "exT2": "16.6"
        }
    },
    {
        "time": "2023-11-30 03:40:24",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "16.7",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.5",
            "exT1": "16.8",
            "exT2": "16.6"
        }
    },
    {
        "time": "2023-11-30 03:50:23",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.7",
        "batT": "15.8",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.5",
            "exT1": "16.8",
            "exT2": "16.5"
        }
    },
    {
        "time": "2023-11-30 04:00:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.6",
        "batT": "15.8",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.5",
            "exT1": "16.7",
            "exT2": "16.5"
        }
    },
    {
        "time": "2023-11-30 04:10:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.6",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.4",
            "exT1": "16.7",
            "exT2": "16.5"
        }
    },
    {
        "time": "2023-11-30 04:20:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.6",
        "batT": "15.8",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.4",
            "exT1": "16.7",
            "exT2": "16.5"
        }
    },
    {
        "time": "2023-11-30 04:30:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.4",
            "exT1": "16.7",
            "exT2": "16.5"
        }
    },
    {
        "time": "2023-11-30 04:40:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.4",
            "exT1": "16.6",
            "exT2": "16.5"
        }
    },
    {
        "time": "2023-11-30 04:50:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.4",
            "exT1": "16.5",
            "exT2": "16.4"
        }
    },
    {
        "time": "2023-11-30 04:55:24",
        "cmd": "0900",
        "batV": "26.6",
        "mosT": "16.4",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.4",
            "exT1": "16.5",
            "exT2": "16.4"
        }
    },
    {
        "time": "2023-11-30 05:05:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.4",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.3",
            "exT1": "16.6",
            "exT2": "16.4"
        }
    },
    {
        "time": "2023-11-30 05:15:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.3",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.3",
            "exT1": "16.6",
            "exT2": "16.4"
        }
    },
    {
        "time": "2023-11-30 05:25:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.3",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.3",
            "exT1": "16.5",
            "exT2": "16.4"
        }
    },
    {
        "time": "2023-11-30 05:35:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.2",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.3",
            "exT1": "16.4",
            "exT2": "16.3"
        }
    },
    {
        "time": "2023-11-30 05:45:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.1",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.3",
            "exT1": "16.4",
            "exT2": "16.3"
        }
    },
    {
        "time": "2023-11-30 05:55:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.1",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.3",
            "exT1": "16.4",
            "exT2": "16.2"
        }
    },
    {
        "time": "2023-11-30 06:05:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.1",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.3",
            "exT1": "16.3",
            "exT2": "16.3"
        }
    },
    {
        "time": "2023-11-30 06:15:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.2",
            "exT1": "16.4",
            "exT2": "16.2"
        }
    },
    {
        "time": "2023-11-30 06:25:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.2",
            "exT1": "16.2",
            "exT2": "16.2"
        }
    },
    {
        "time": "2023-11-30 06:35:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.1",
            "exT1": "16.4",
            "exT2": "16.2"
        }
    },
    {
        "time": "2023-11-30 06:45:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.2",
            "exT1": "16.2",
            "exT2": "16.1"
        }
    },
    {
        "time": "2023-11-30 06:55:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.9",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.2",
            "exT1": "16.1",
            "exT2": "16.1"
        }
    },
    {
        "time": "2023-11-30 07:05:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.8",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.3",
            "exT2": "16.1"
        }
    },
    {
        "time": "2023-11-30 07:15:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.8",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.3",
            "exT2": "16.1"
        }
    },
    {
        "time": "2023-11-30 07:25:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.8",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.1",
            "exT1": "16.2",
            "exT2": "16.1"
        }
    },
    {
        "time": "2023-11-30 07:35:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.9",
        "batT": "15.3",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.2",
            "exT2": "16.1"
        }
    },
    {
        "time": "2023-11-30 07:45:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.8",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.1",
            "exT2": "16"
        }
    },
    {
        "time": "2023-11-30 07:55:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.8",
        "batT": "15.3",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.2",
            "exT2": "16"
        }
    },
    {
        "time": "2023-11-30 08:05:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.7",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16",
            "exT2": "15.9"
        }
    },
    {
        "time": "2023-11-30 08:15:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.6",
        "batT": "15.3",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16",
            "exT2": "15.9"
        }
    },
    {
        "time": "2023-11-30 08:25:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.4",
        "batT": "15.2",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.9",
            "exT1": "15.8",
            "exT2": "15.7"
        }
    },
    {
        "time": "2023-11-30 08:35:25",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.2",
        "batT": "15.2",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.9",
            "exT1": "15.8",
            "exT2": "15.6"
        }
    },
    {
        "time": "2023-11-30 08:45:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.2",
        "batT": "15.2",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.8",
            "exT1": "15.7",
            "exT2": "15.4"
        }
    },
    {
        "time": "2023-11-30 08:55:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.1",
        "batT": "15.2",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.8",
            "exT1": "15.7",
            "exT2": "15.5"
        }
    },
    {
        "time": "2023-11-30 09:05:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15",
        "batT": "15.1",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.8",
            "exT1": "15.8",
            "exT2": "15.5"
        }
    },
    {
        "time": "2023-11-30 09:15:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "14.8",
        "batT": "15.1",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "15.7",
            "exT2": "15.4"
        }
    },
    {
        "time": "2023-11-30 09:25:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "14.8",
        "batT": "15",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "15.5",
            "exT2": "15.3"
        }
    },
    {
        "time": "2023-11-30 09:35:25",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "14.7",
        "batT": "15",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.6",
            "exT1": "15.8",
            "exT2": "15.4"
        }
    },
    {
        "time": "2023-11-30 09:45:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "14.7",
        "batT": "15.1",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "15.9",
            "exT2": "15.6"
        }
    },
    {
        "time": "2023-11-30 09:55:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "14.9",
        "batT": "15",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "15.7",
            "exT2": "15.6"
        }
    },
    {
        "time": "2023-11-30 10:05:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15",
        "batT": "15.1",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "16",
            "exT2": "15.7"
        }
    },
    {
        "time": "2023-11-30 10:15:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15",
        "batT": "15",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "15.9",
            "exT2": "15.6"
        }
    },
    {
        "time": "2023-11-30 10:25:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.1",
        "batT": "15",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "15.9",
            "exT2": "15.6"
        }
    },
    {
        "time": "2023-11-30 10:35:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15",
        "batT": "15",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.6",
            "exT1": "15.9",
            "exT2": "15.5"
        }
    },
    {
        "time": "2023-11-30 10:45:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.1",
        "batT": "15.1",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "16",
            "exT2": "15.8"
        }
    },
    {
        "time": "2023-11-30 10:55:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.2",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.8",
            "exT1": "16.1",
            "exT2": "15.4"
        }
    },
    {
        "time": "2023-11-30 11:05:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.5",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.6",
            "exT1": "16.2",
            "exT2": "15.1"
        }
    },
    {
        "time": "2023-11-30 11:10:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.6",
        "batT": "15.3",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.5",
            "exT1": "16",
            "exT2": "14.6"
        }
    },
    {
        "time": "2023-11-30 11:20:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.6",
        "batT": "15.3",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.4",
            "exT1": "15.9",
            "exT2": "14.5"
        }
    },
    {
        "time": "2023-11-30 11:30:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.6",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.6",
            "exT1": "16.2",
            "exT2": "15.3"
        }
    },
    {
        "time": "2023-11-30 11:40:25",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.7",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "16.2",
            "exT2": "15.4"
        }
    },
    {
        "time": "2023-11-30 11:50:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.8",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.7",
            "exT1": "16.1",
            "exT2": "14.8"
        }
    },
    {
        "time": "2023-11-30 12:00:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.9",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.5",
            "exT2": "15.7"
        }
    },
    {
        "time": "2023-11-30 12:10:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.1",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.9",
            "exT1": "16.4",
            "exT2": "15.5"
        }
    },
    {
        "time": "2023-11-30 12:20:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.1",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.5",
            "exT2": "15.5"
        }
    },
    {
        "time": "2023-11-30 12:30:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.2",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.1",
            "exT1": "16.5",
            "exT2": "16"
        }
    },
    {
        "time": "2023-11-30 12:40:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.3",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.5",
            "exT2": "15.3"
        }
    },
    {
        "time": "2023-11-30 12:50:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.3",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15",
            "exT1": "16.5",
            "exT2": "15.5"
        }
    },
    {
        "time": "2023-11-30 13:00:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.3",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.9",
            "exT1": "16.4",
            "exT2": "15.3"
        }
    },
    {
        "time": "2023-11-30 13:10:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.4",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.9",
            "exT1": "16.3",
            "exT2": "15"
        }
    },
    {
        "time": "2023-11-30 13:20:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.3",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.9",
            "exT1": "16.4",
            "exT2": "15.2"
        }
    },
    {
        "time": "2023-11-30 13:30:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.3",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.2",
            "exT1": "16.6",
            "exT2": "15.8"
        }
    },
    {
        "time": "2023-11-30 13:40:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.2",
            "exT1": "16.7",
            "exT2": "16"
        }
    },
    {
        "time": "2023-11-30 13:50:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.2",
            "exT1": "16.7",
            "exT2": "15.9"
        }
    },
    {
        "time": "2023-11-30 14:00:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.1",
            "exT1": "16.6",
            "exT2": "15.6"
        }
    },
    {
        "time": "2023-11-30 14:10:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.2",
            "exT1": "16.7",
            "exT2": "16.1"
        }
    },
    {
        "time": "2023-11-30 14:20:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.9",
            "exT1": "16.3",
            "exT2": "15.3"
        }
    },
    {
        "time": "2023-11-30 14:30:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.5",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.1",
            "exT1": "16.6",
            "exT2": "15.6"
        }
    },
    {
        "time": "2023-11-30 14:40:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.4",
        "batT": "15.7",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "15.1",
            "exT1": "16.6",
            "exT2": "15.6"
        }
    },
    {
        "time": "2023-11-30 14:50:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.3",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.8",
            "exT1": "16.3",
            "exT2": "15"
        }
    },
    {
        "time": "2023-11-30 15:00:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.2",
        "batT": "15.6",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.8",
            "exT1": "16.2",
            "exT2": "15.2"
        }
    },
    {
        "time": "2023-11-30 15:10:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16.1",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.6",
            "exT1": "16",
            "exT2": "14.8"
        }
    },
    {
        "time": "2023-11-30 15:15:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "16",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.6",
            "exT1": "16",
            "exT2": "14.4"
        }
    },
    {
        "time": "2023-11-30 15:25:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.9",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.6",
            "exT1": "16",
            "exT2": "14.8"
        }
    },
    {
        "time": "2023-11-30 15:35:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.9",
        "batT": "15.5",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.6",
            "exT1": "16.2",
            "exT2": "15"
        }
    },
    {
        "time": "2023-11-30 15:45:24",
        "cmd": "0900",
        "batV": "26.61",
        "mosT": "15.8",
        "batT": "15.4",
        "cc": "0",
        "dcc": "0",
        "leftPer": "88",
        "more": {
            "batT2": "14.3",
            "exT1": "15.9",
            "exT2": "14.6"
        }
    }
]

const ibmsLineOptions = (xAxis, legend, series, title) => {
    const option = {
        title: {
            text: title,
            textStyle: {
                fontSize: 18,
                color: "#ffffff",
            },
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            },
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            position: function (pos, params, el, elRect, size) {
                var obj = { top: 30 };
                obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
                return obj;
            },
            valueFormatter: (value) => ' ' + value,
            extraCssText: 'width: 215px',
        },
        axisPointer: {
            link: { xAxisIndex: 'all' },
            label: {
                backgroundColor: '#18bef1'
            }
        },
        toolbox: {
            show: false,
            feature: {
                dataZoom: {
                    yAxisIndex: false
                },
                brush: {
                    type: ['lineX', 'clear']
                }
            }
        },
        legend: {
            bottom: '12%',
            data: legend,
            width: '90%',
            left: '6%',
            pageButtonGap: 10,
            textStyle: {
                fontSize: 11,
                color: "#ffffff",
            },
            selector: [{
                type: 'allOrInverse',
                title: '全选/反选'
            }],
            selectorLabel: {
                color: '#ffffff',
                fontSize: 10,
                borderRadius: 5,
                padding: [5, 10],
                backgroundColor: '#333',
                borderColor: '#666'
            },
            selectorPosition: 'start'
        },
        grid: {
            top:'3%',
            left: '5%',
            right: '5%',
            bottom: '21%',
            containLabel: true,
        },
        dataZoom: [
            {
                type: "inside",
                start: 0,
                end: 100,
            },
            {
                show: true,
                type: "slider",
                y: "90%",
                start: 0,
                end: 100,
            },
        ],
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxis,
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#ffffff",
                },
            },
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#ffffff",
                },
            },
        },
        series: series
    };
    return option;
}

export {
    gaugeOptions,
    ibmsLineOptions
}
