// utils/permission.js

// 权限ID映射表
export const permissionIdMap = {
    1: 'Hargingprotectionvoltage',
    2: 'Chargingrecoveryvoltage',
    3: 'Dischargeprotectionvoltage',
    4: 'Dischargerecoveryvoltage',
    5: 'Dischargeshutdownvoltage',
    6: 'MOStemperatureprotection',
    7: 'MOStemperaturerecovery',
    8: 'Batterytemperatureprotection',
    9: 'Batterytemperaturerecovery',
    10: 'Lowtemperatureprotectionforcharging',
    11: 'Lowtemperaturechargingrecovery',
    12: 'Lowtemperatureprotectionfordischarge',
    13: 'Lowtemperaturedischargerecovery',
    14: 'Heatingactivationtemperature',
    15: 'Heatingstoptemperature',
    18: 'Batterycapacity',
    19: 'Reportingintervaltime',
    20: 'switch',
    25: 'shutdown',
    26: 'strongStart',
    27: 'autoHeatOn',
    28: 'autoHeatOff',
    29: 'reportData',
    30: 'reportCCID',
    31: 'quickReport',
    32: 'dontallProtect',
    33: 'donttemperatureProtect',
};

// 特殊权限列表（需要排除的ID）
const SPECIAL_PERMISSION_KEYS = ['Batterycapacity', 'switch'];

// 检查是否拥有 Batterycapacity(18) 或 switch(20) 中的任意权限
export function hasBatteryOrSwitchPermission(perms) {
    return perms['Batterycapacity'] || perms['switch'];
}

// 检查是否拥有其他权限中的任意一个或多个
export function hasOtherPermission(perms) {
    const specifiedPermissions = [
        'Hargingprotectionvoltage',
        'Chargingrecoveryvoltage',
        'Dischargeprotectionvoltage',
        'Dischargerecoveryvoltage',
        'Dischargeshutdownvoltage',
        'MOStemperatureprotection',
        'MOStemperaturerecovery',
        'Batterytemperatureprotection',
        'Batterytemperaturerecovery',
        'Lowtemperatureprotectionforcharging',
        'Lowtemperaturechargingrecovery',
        'Lowtemperatureprotectionfordischarge',
        'Lowtemperaturedischargerecovery',
        'Heatingactivationtemperature',
        'Heatingstoptemperature',
        'Reportingintervaltime',
    ];

    return specifiedPermissions.some(perm => perms[perm]);
}

// 检查是否拥有其他权限中的全部权限
export function hasAllOtherPermissions(perms) {
    const otherPermissions = Object.values(permissionIdMap).filter(
        key => !SPECIAL_PERMISSION_KEYS.includes(key)
    );

    return otherPermissions.every(perm => perms[perm]);
}

// 检查是否拥有所有权限（包括特殊权限）
export function hasAllPermissions(perms) {
    return Object.values(permissionIdMap).every(perm => perms[perm]);
}

// 检查是否拥有权限ID为25到33中的任意权限
export function hasPermissionFrom25To33(perms) {
    const targetPermissions = [
        'shutdown',
        'strongStart',
        'autoHeatOn',
        'autoHeatOff',
        'reportData',
        'reportCCID',
        'quickReport',
        'dontallProtect',
       'donttemperatureProtect',
    ];
    return targetPermissions.some(perm => perms[perm]);
}

// 检查是否拥有指定的 20 个权限中的任意一个
export function hasAnyOfSpecifiedPermissions(perms) {
    const specifiedPermissions = [
        'Hargingprotectionvoltage',
        'Chargingrecoveryvoltage',
        'Dischargeprotectionvoltage',
        'Dischargerecoveryvoltage',
        'Dischargeshutdownvoltage',
        'MOStemperatureprotection',
        'MOStemperaturerecovery',
        'Batterytemperatureprotection',
        'Batterytemperaturerecovery',
        'Lowtemperatureprotectionforcharging',
        'Lowtemperaturechargingrecovery',
        'Lowtemperatureprotectionfordischarge',
        'Lowtemperaturedischargerecovery',
        'Heatingactivationtemperature',
        'Heatingstoptemperature',
        'Batterycapacity',
        'Reportingintervaltime',
        'switch'
    ];

    return specifiedPermissions.some(perm => perms[perm]);
}
