<template>
  <avue-form :option="option" @submit="handleSubmit" v-model="form"></avue-form>
</template>

<script>
export default {
  data() {
    return {
      paymentTab: 1,
      form: {
        input: 1,
      },
      option: {
        emptyBtn: false,
        labelPosition: "right",
        labelWidth: 120,
        column: [
          {
            label: "30 币 (1 年)",
            prop: "a1",
            type: "number",
            span: 24,
            controls: false,
          },
          {
            label: "60 币 (2 年)",
            prop: "a2",
            type: "number",
            span: 24,
            controls: false,
          },
          {
            label: "120 币 (终身)",
            prop: "a3",
            type: "number",
            span: 24,
            controls: false,
          },
        ],
      },
    };
  },

  computed: {},
  methods: {
    handleSubmit() {
      this.paymentTab = 2;
    },
    handlePay() {
      console.log(this);
      alert("支付成功 谢谢您呢！");
      this.$parent.paymentModel = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 20px;
}
.payment-submit {
  display: flex;
  flex-direction: column;
  align-items: center;
  div {
    margin-bottom: 10px;
  }
  div:nth-child(1) {
    font-size: 18px;
  }
  div:nth-child(2) {
    font-size: 16px;
  }
  a {
    color: red;
    cursor: pointer;
  }
  img {
    width: 300px;
    height: 300px;
    background-color: aqua;
  }
}
</style>
