<template>
  <div class="mapModel">
    <div class="mapModel-header">
      <div class="mapModel-header-search">
        <el-input
          class="mapModel-header-search-input"
          placeholder="请输入关键字回车键定位"
          v-model="keywords"
          @change="remoteMethod"
        />
        <el-button :disabled="!marker" type="primary" @click="handleSubmit"
          >确定</el-button
        >
      </div>
    </div>
    <div class="AlarmapContainer" id="AlarmapContainer"></div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { AMapLoaderInitMap } from "@/utils/amap";
let polygonObj = [];
let placeSearch = null;
export default {
  props: {
    info: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      loading: false,
      map: null,
      polygon: null,
      isPolygon: false,
      options: [],
      keywords: "",
      marker: "",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },

  methods: {
    remoteMethod(query) {
      placeSearch.search(query, (status, result) => {
        this.options = result.poiList.pois;
        this.loading = false;
      });
    },

    handleSubmit() {
      this.$emit("change", { ...this.marker });
    },
  },
  mounted() {
    AMapLoaderInitMap({
      afterRequest: (AMap) => {
        this.map = new AMap.Map("AlarmapContainer", {
          //设置地图容器id
          viewMode: "3D", //是否为3D地图模式
          zoom: 8, //初始化地图级别
          resizeEnable: true,
        });
        //搜索
        let autoOptions = {
          city: "全国",
          pageSize: 5, // 单页显示结果条数
          pageIndex: 1, // 页码
          citylimit: true, //是否强制限制在设置的城市内搜索
          map: this.map, // 展现结果的地图实例
          panel: "panel", // 结果列表将在此容器中进行展示。
          autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
        };
        placeSearch = new AMap.PlaceSearch(autoOptions);

        let marker;
        if (this.info.latitude) {
          const longitude = this.info.longitude;
          const latitude = this.info.latitude;
          marker = new AMap.Marker({
            icon:
              "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
            position: [longitude, latitude],
            offset: new AMap.Pixel(-13, -30),
          });
          marker.setMap(this.map);
          this.marker = {
            selectRange: {
              latitude,
              longitude,
            },
          };
          this.map.setFitView(null, false, [150, 60, 100, 60]);
        }

        this.map.on("click", (e) => {
          if (marker) {
            marker.setMap(null);
          }
          marker = new AMap.Marker({
            icon:
              "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
            position: [e.lnglat.lng, e.lnglat.lat],
            offset: new AMap.Pixel(-13, -30),
          });
          this.marker = {
            selectRange: {
              latitude: `${e.lnglat.lat}`,
              longitude: `${e.lnglat.lng}`,
            },
          };
          marker.setMap(this.map);
        });
      },
    });
  },
};
</script>

<style lang="scss" scoped>
.mapModel {
  width: 100%;
  height: 100%;
}

.mapModel-header {
  width: 100%;
  height: 6%;
  background-color: #fff;
  padding: 0px 10px;

  &-search {
    width: 400px;
    height: 100%;
    display: flex;
    align-items: center;

    &-input {
      width: 300px;
      margin-right: 10px;
    }
  }
}

.AlarmapContainer {
  width: 100%;
  height: 94%;
}
</style>
