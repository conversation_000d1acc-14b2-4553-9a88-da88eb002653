import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-base/batterControl/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-base/batterControl/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-base/batterControl/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-base/batterControl/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-base/batterControl/submit',
    method: 'post',
    data: row
  })
}

export const getListByType = (dataType) => {
  return request({
    url: '/blade-base/batterControl/getListByType',
    method: 'get',
    params: {
      dataType
    }
  })
}

export const getDeviceVersion = (devicesn) => {
  return request({
    url: '/iotdb/IotEquipSource/getDeviceVersion',
    method: 'get',
    params: {
      devicesn
    }
  })
}

export const sendComC8 = (params) => {
  return request({
    url: '/blade-base/batterControl/sendComC8',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const sendComC9 = (params) => {
  return request({
    url: '/blade-base/batterControl/sendComC9',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const sendComB9 = (params) => {
  return request({
    url: '/blade-base/batterControl/sendComB9',
    method: 'get',
    params: {
      ...params
    }
  })
}
