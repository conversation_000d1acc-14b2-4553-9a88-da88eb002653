<template>
  <div class="userInfo">
    <avue-form
        style="width: 100%"
        :option="option"
        v-model="form"
        @submit="handleSubmit"
    ></avue-form>

    <!-- 添加编辑按钮 -->
    <div class="mt-4 flex justify-end">
      <el-button
          type="primary"
          @click="toggleEdit"
          :loading="loading"
      >
        {{ isEdit ? '保存' : '编辑信息' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { getUserInfo, updateInfo } from "@/api/system/user";
import "nprogress/nprogress.css";

export default {
  data() {
    return {
      isEdit: false, // 编辑状态
      loading: false, // 加载状态
      form: {},
      option: {
        emptyBtn: false,
        submitBtn: false,
        column: [
          {
            label: "头像",
            type: "upload",
            listType: "picture-img",
            propsHttp: {
              res: "data",
              url: "link",
            },
            canvasOption: {
              text: " ",
              ratio: 0.1,
            },
            action: "/blade-resource/oss/endpoint/put-file",
            tip: "只能上传jpg/png用户头像，且不超过500kb",
            span: 10,
            row: false,
            prop: "avatar",
            disabled: true // 初始禁用
          },
          {
            label: "营业执照",
            type: "upload",
            listType: "picture-img",
            propsHttp: {
              res: "data",
              url: "link",
            },
            canvasOption: {
              text: " ",
              ratio: 0.1,
            },
            action: "/blade-resource/oss/endpoint/put-file",
            span: 10,
            row: false,
            prop: "license",
            disabled: true // 初始禁用
          },
          {
            label: "登录账号",
            span: 20,
            row: true,
            prop: "account",
            disabled: true // 始终禁用
          },
          {
            label: "联系人名称",
            span: 20,
            row: true,
            prop: "realName",
            disabled: true // 初始禁用
          },
          {
            label: "手机号",
            span: 20,
            row: true,
            prop: "phone",
            disabled: true // 初始禁用
          },
          {
            label: "邮箱",
            prop: "email",
            span: 20,
            row: true,
            disabled: true // 初始禁用
          },
          {
            label: "昵称",
            prop: "name",
            span: 20,
            row: true,
            disabled: true // 初始禁用
          },
        ],
      },
    };
  },
  mounted() {
    this.getUserInfo();
  },
  methods: {
    resetForm(){
      this.isEdit = false
      this.option.column.forEach((item, index) => {
        item.disabled = true
      });
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await getUserInfo();
        const user = res.data.data;
        this.form = {
          id: user.id,
          avatar: user.avatar,
          realName: user.realName,
          account: user.account,
          phone: user.phone,
          email: user.email,
          license: user.license,
          name: user.name
        };
      } catch (error) {
        console.error("获取用户信息失败:", error);
        this.$message.error("获取用户信息失败");
      }
    },

    // 切换编辑状态
    toggleEdit() {
      if (this.loading) return;

      if (this.isEdit) {
        // 保存修改
        this.handleSubmit(this.form);
      } else {
        // 进入编辑状态
        this.isEdit = true;
        this.updateFormDisabledStatus();
      }
    },

    // 更新表单禁用状态（跳过第一个字段）
    updateFormDisabledStatus() {
      this.option.column.forEach((item, index) => {
        // 跳过第一个字段（登录账号）
        if (item.prop !== 'account' && item.prop !== 'license' ) { // 假设第一个字段的 prop 是 account
          item.disabled = !this.isEdit;
        }
      });
    },

    // 提交表单
    async handleSubmit(form) {
      if (this.loading) return;

      this.loading = true;
      try {
        const res = await updateInfo(form);
        if (res.data.success) {
          this.$message.success("修改信息成功!");
          this.isEdit = false;
          this.updateFormDisabledStatus();
          this.$emit("close"); // 关闭弹窗（如果有）
        } else {
          this.$message.error(res.data.msg || "修改信息失败");
        }
      } catch (error) {
        console.error("提交表单失败:", error);
        this.$message.error("提交表单失败");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.disable {
  color: #c4c6cc;
  pointer-events: none;
  user-select: none;
}

.userInfo {
  position: relative;
}

.recharge {
  height: 40px;
  margin-left: 9%;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: white;
}

:deep(.el-input__inner) {
  color: rgb(0, 0, 0);
}

:deep(.avue-upload__avatar){
  color: transparent;
}

/* 添加编辑按钮样式 */
.mt-4 {
  margin-top: 1rem;
}

.flex {
  display: flex;
}

.justify-end {
  justify-content: flex-end;
}
</style>
