<template>
  <div class="order-container">
    <el-tabs :tab-position="tabPosition" style="height: 100%; margin-left: 10px">
      <el-tab-pane label="全部订单" lazy="false">
        <order-all ref="orderAll"></order-all>
      </el-tab-pane>
      <el-tab-pane v-if="userInfo.dept_id === '1123598813738675201'">
        <template #label>
          <!-- 文字始终显示，badge仅在数值>0时显示 -->
          <span>待操作订单</span>
          <el-badge :value="operateOrderCount" class="badge-item" v-if="operateOrderCount > 0" />
        </template>
        <operate-order ref="operateOrder" @queryData="handleQueryData"></operate-order>
      </el-tab-pane>
      <el-tab-pane label="设备续费定价" v-if="userInfo.dept_id === '1123598813738675201'">
        <device-price ref="devicePrice"></device-price>
      </el-tab-pane>
      <el-tab-pane label="设备续费到账详情">
        <renew-record ref="renewRecord"></renew-record>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import orderAll from "./order/orderAll.vue";
import operateOrder from "./order/operateOrder.vue";
import {mapGetters} from "vuex";
import devicePrice from "./order/devicePrice.vue";
import renewRecord from "./order/renewRecord.vue";

export default {
  name: "order",
  components: {
    orderAll,
    operateOrder,
    devicePrice,
    renewRecord
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  data() {
    return {
      tabPosition: 'left',
      operateOrderCount: 0,
    }
  },
  methods: {
    handleQueryData(data) {
      this.operateOrderCount = data
    }
  }
}
</script>

<style lang="scss" scoped>
.order-container {
  width: 100%;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 5px;
  height: 100%;
  border: 1px solid lightblue;
}

/* Badge样式调整 */
.badge-item {
  display: inline-flex;
  align-items: center;
  position: absolute;
  top: 7px;
  right: 7px;
}
</style>

<style>
.el-tabs__item {
  color: #ffffff;
}
</style>
