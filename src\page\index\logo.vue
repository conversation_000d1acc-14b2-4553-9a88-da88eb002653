<template>
  <div class="avue-logo" >
    <transition name="fade">
      <span v-if="getScreen(isCollapse)" class="avue-logo_subtitle" key="0">
        {{ website.logo }}
      </span>
    </transition>
    <transition-group name="fade" >
      <template v-if="getScreen(!isCollapse)">
        <img class="avue-logo-img" src="/img/logo.png" />
        <span class="avue-logo-text">杰途创新</span>
      </template>
    </transition-group>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "logo",
  data() {
    return {};
  },
  created() {},
  computed: {
    ...mapGetters(["isCollapse"]),
  },
  methods: {},
};
</script>

<style scoped>
.avue-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

}

.avue-logo-img {
  height: 30px;       /* 设置图片高度 */
  transform: scale(3.0);    /* 保持图片原始比例 */
  vertical-align: middle; /* 确保垂直对齐 */
  margin-right: 0;   /* 移除右边距 */
}

.avue-logo-text {
  display: inline-block;
  height: 40px;      /* 与图片高度一致 */
  line-height: 45px; /* 行高等于高度实现垂直居中 */
  font-size:25px;   /* 调整字体大小与图片匹配 */
  vertical-align: middle; /* 确保垂直对齐 */
  margin-left: 0;    /* 移除左边距 */
  padding-left: 4px; /* 文字与图片的间距，可根据需要调整 */
}
</style>
