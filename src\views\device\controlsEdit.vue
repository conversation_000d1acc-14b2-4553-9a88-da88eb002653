<template>
  <div class="container">
    <el-dialog
        class="controls-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="dialogVisible"
        width="450px"
        title="新增"
        @close="closeDialog"
        >
        <el-form ref="form" :model="form" label-width="120px" :rules="rule" :inline="false">
            <el-form-item label="项目名称" prop="projectId" required>
                <el-select v-model="form.projectId" placeholder="请选择项目名称">
                    <el-option
                        v-for="item in projectData"
                        :key="item.id"
                        :label="item.projectName"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="产品名称" prop="productId" required>
                <el-select v-model="form.productId" placeholder="请选择产品名称">
                    <el-option
                        v-for="item in productData"
                        :key="item.id"
                        :label="item.productName"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item> 
            <el-form-item label="设备名称" prop="equIds" required>
                <el-select
                    v-model="form.equIds"
                    multiple
                    placeholder="请选择设备"
                    >
                    <el-option
                        v-for="item in deviceData"
                        :key="item.id"
                        :label="item.deviceName"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item> 
            <el-form-item label="工作模式" prop="workType" required>
                <el-select v-model="form.workType" placeholder="请选择工作模式">
                    <el-option
                        v-for="item in workTypeData"
                        :key="item.dictKey"
                        :label="item.dictValue"
                        :value="item.dictKey"
                    />
                </el-select>
            </el-form-item> 
            <el-form-item v-if="form.workType == 7" label="开始时间" prop="startTime">
                <el-date-picker
                    v-model="form.startTime"
                    type="datetime"
                    placeholder="请选择开始时间"
                />
            </el-form-item> 
            <el-form-item v-if="form.workType == 7" label="结束时间" prop="endTime">
                <el-date-picker
                    v-model="form.endTime"
                    type="datetime"
                    placeholder="请选择结束时间"
                />
            </el-form-item>
            <el-form-item v-if="form.workType == 3" label="高压包" prop="hv">
                <el-switch v-model="form.hv" active-value="1" inactive-value="0" />
            </el-form-item> 
            <el-form-item v-if="form.workType == 3" label="1#灯管" prop="uvOne">
                <el-switch v-model="form.uvOne" active-value="1" inactive-value="0" />
            </el-form-item> 
            <el-form-item v-if="form.workType == 3" label="2#灯管" prop="uvTwo">
                <el-switch v-model="form.uvTwo" active-value="1" inactive-value="0" />
            </el-form-item> 
            <el-form-item label="心跳间隔" prop="heartStep" required>
                <el-select v-model="form.heartStep" placeholder="请选择心跳间隔时间">
                    <el-option
                        v-for="item in heartStepData"
                        :key="item.dictKey"
                        :label="item.dictValue"
                        :value="item.dictKey"
                    />
                </el-select>
            </el-form-item> 
                <el-form-item label=" ">
                <el-button type="primary" @click="handleSubmit">确认</el-button>
                <el-button @click="closeDialog">取消</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { add } from "@/api/device/controls";
import { getTreeData } from "@/api/project/list";
import { getList as productList } from "@/api/device/product";
import { getList as deviceList } from "@/api/device/list";
import { getDictionary } from "@/api/system/dictbiz";
import { mapGetters } from "vuex";
export default {
    data() {
        return {
            dialogVisible: false,
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0,
            },
            selectionList: [],
            projectData: [],
            productData: [],
            deviceData: [],
            workTypeData: [],
            heartStepData: [],
            rule: {
                projectId: [
                    { required: true, message: '请选择项目', trigger: 'blur' }
                ],
                productId: [
                    { required: true, message: '请选择产品', trigger: 'blur' }
                ],
                equIds: [
                    { required: true, message: '请选择设备', trigger: 'blur' }
                ],
                workType: [
                    { required: true, message: '请选择工作模式', trigger: 'blur' }
                ],
                heartStep: [
                    { required: true, message: '请选择心跳间隔', trigger: 'blur' }
                ],
                startTime: [
                    { required: true, message: '请选择开始时间', trigger: 'blur' }
                ],
                endTime: [
                    { required: true, message: '请选择结束时间', trigger: 'blur' }
                ]
            }
        }
    },
    async created() {
        this.getProjectData(this.page);
        this.getProductData(this.page);
        this.getDeviceData(this.page);
        this.workTypeData = await this.getDict("deviceWorkMode");
        this.heartStepData = await this.getDict("timeFrame");
    },
    methods: {
        init(id = null) {
            this.form = {};
            if (id !== null) {
            getDetail(id).then(res => {
                const arr = res.data.data;
                console.log(arr);
            })
            }
            this.dialogVisible = true;
        },
        handleSubmit() {
            this.form.equIds = JSON.stringify(this.form.equIds).substring(0,JSON.stringify(this.form.equIds).length - 1).substring(1);
            this.$refs.form.validate((valid, fields) => {
                if (valid) {
                    add(this.form).then(res => {
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                    })
                } else {
                    return false;
                }
                this.$emit('result');
                this.dialogVisible = false;
            })
        },
        getProjectData(page, params = {}) {
            this.loading = true;
            getTreeData(
                page.currentPage,
                page.pageSize,
                Object.assign(params, this.query)
            ).then((res) => {
                const data = res.data.data;
                this.projectData = data;

            })
        },
        getProductData(page, params = {}) {
            productList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
                const data = res.data.data.records;
                this.productData = data;
            })
        },
        getDeviceData(page, params = {}) {
            deviceList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
                const data = res.data.data.records;
                this.deviceData = data;
            })
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        async getDict(code) {
            let result = [];
            const query = {
                code: code
            }
            result = await getDictionary(query).then(res => {
                const data = res.data.data;
                return data;
            })
            return result;
        },
    }
}
</script>

<style>

</style>