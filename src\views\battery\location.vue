<template>
  <div class="location-container">
    <div class="location-container-right battery-border" style="height: auto;min-height:740px;">
      <!--
            <el-form :inline="true" :model="queryForm" class="location-container-haeder">
              &lt;!&ndash;        <el-form-item label="设备号码" prop="devicesn" style="width: 100%">
                        <avue-select
                            v-model="queryForm.devicesn"
                            placeholder="请选择内容"
                            type="tree"
                            @change="changeDevice"
                            :filterable="true"
                            style="width: 100%"
                            :dic="devEquList"
                        ></avue-select>
                      </el-form-item>&ndash;&gt;
              &lt;!&ndash;
                      <el-form-item label="开始时间" prop="startTime" style="width: 100%">
                        <avue-date
                            type="datetime"
                            format="YYYY年MM月DD日 HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            v-model="queryForm.startTime"
                            placeholder="请输入开始时间"
                            style="width: 100%"
                        />
                      </el-form-item>
                      <el-form-item label="结束时间" prop="endTime" style="width: 100%">
                        <avue-date
                            type="datetime"
                            format="YYYY年MM月DD日 HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            v-model="queryForm.endTime"
                            placeholder="请输入结束时间"
                            style="width: 100%"
                        />
                      </el-form-item>
              &ndash;&gt;

      &lt;!&ndash;        <el-form-item label="点位间隔时间" style="width: 100%">
                <el-select v-model="intervalTime" placeholder="请选择" style="width: 100%">
                  <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>&ndash;&gt;
              <el-row>
                <el-form-item>
                  &lt;!&ndash;            <el-button type="primary" :loading="loading" @click="handleQuery">加载轨迹</el-button>&ndash;&gt;
                  &lt;!&ndash;            <el-button type="warning" :loading="loading1" @click="exportAlarmMessage">导出数据</el-button>&ndash;&gt;
                </el-form-item>
                &lt;!&ndash; <el-form-item>
                  <el-button type="primary" @click="handleStart">播放</el-button>
                </el-form-item> &ndash;&gt;

              </el-row>
            </el-form>
      -->

      <div class="location-container-below">
        <div style="height: 80px;padding: 10px">
          <el-form-item label="点位间隔时间" style="width: 100%;margin-bottom: 5px">
            <el-select v-model="intervalTime" placeholder="请选择" style="width: 100%;color: #000">
              <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <div class="warningFont" v-if="isShowWarningFont">暂时无上报定位数据</div>
        </div>
        <el-table
            :data="tableData"
            style="width: 100%;height: 320px" border>
          <el-table-column
              type="index"
              align="center"
              label="编号"
              width="60">
          </el-table-column>
          <el-table-column
              label="坐标信息"
              width="284">
            <template #default="{ row }">
              <div style="display: grid; grid-template-columns: auto auto; gap: 5px;">
                <span class="label">时间：</span>
                <span>{{ row.time }}</span>
                <span class="label">地址：</span>
                <span>{{ row.placeName }}</span>
              </div>
            </template>
          </el-table-column>

        </el-table>
      </div>
      <div class="AlarmapContainer" id="AlarmapContainer"></div>
      <!--      <div style="position: absolute;right: 1%;bottom: 15%;background-color: #ffffff;width: 40px;height: 40px;display: flex;align-items: center;justify-content: center;"
                 @click="openAlarmap">
              <img src="/img/Download.png" alt="导出数据" title="导出数据" width="30" height="30 ">
            </div>-->
      <div
          style="position: absolute;right: 1%;bottom: 48%;background-color: #ffffff;width: 40px;height: 40px;display: flex;align-items: center;justify-content: center;"
          @click="showPreciseLocation">
        <img src="/img/address1.png" alt="精准定位" title="精准定位" width="30" height="30 ">
      </div>
      <div
          style="position: absolute;right: 1%;bottom:55%;background-color: #ffffff;width: 40px;height: 40px;display: flex;align-items: center;justify-content: center;"
          @click="fastreport" v-if="userInfo.dept_id ==='1123598813738675201'">
        <img id="alarmImg" :class="{ flashing: isFlashing }" src="/img/alarm.png" alt="报警" title="报警" width="30"
             height="30">
      </div>
    </div>

    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        append-to-body
        v-model="openDialogVisible"
        width="400"
        title="数据导出">

      <el-form :inline="true" :model="AlarmForm">
        <el-form-item label="设备号码" prop="devicesn" style="width: 100%">
          <avue-select
              v-model="AlarmForm.devicesn"
              placeholder="请选择内容"
              type="tree"
              @change="changeDevice"
              :filterable="true"
              style="width: 100%"
              :dic="devEquList"
          ></avue-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" style="width: 100%">
          <avue-date
              type="datetime"
              format="YYYY年MM月DD日 HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="AlarmForm.startTime"
              placeholder="请输入开始时间"
              style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime" style="width: 100%">
          <avue-date
              type="datetime"
              format="YYYY年MM月DD日 HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="AlarmForm.endTime"
              placeholder="请输入结束时间"
              style="width: 100%"
          />
        </el-form-item>

      </el-form>
      <!--      <el-button type="warning" @click="fastreport">快速上报数据</el-button>-->
      <el-button type="primary" @click="exportAlarmMessage" style="float: right">导出位置信息</el-button>
      <br>
      <br>
    </el-dialog>


  </div>
</template>

<script>
import {AMapLoaderInitMap, AMapGPS} from "@/utils/amap";
import {exportGPSData, getGPSData, getIotdbGpsPointData, getTop5PointData, PcIssuedData} from "@/api/device/card";
import {getBladeSystemDeptThree, getBatterList} from "@/api/battery/equipment.js";
import {uuid} from "avue-plugin-ueditor/packages/ueditor/src/upload/util";
import dayjs from 'dayjs';
import {mapGetters} from "vuex";

// 定义非响应式地图实例变量
let mapInstance = null;
let markerInstance = null;
let polylineInstance = null;
let passedPolylineInstance = null;
let startMarkerInstance = null;
let endMarkerInstance = null;
let infoWindowInstance = null;
// 局部地图元素引用
let preciseMarker = null;
let breakMarkers = [];
let breakInfoWindows = [];

export default {
  data() {
    return {
      isFlashing: false,
      preciseLocation: null,
      AlarmForm: {},
      openDialogVisible: false,
      tableData: [],
      intervalTime: '30',
      options: [
        {
          value: '10',
          label: '间隔10分钟'
        }, {
          value: '30',
          label: '间隔30分钟'
        }, {
          value: '60',
          label: '间隔60分钟'
        }, {
          value: '120',
          label: '间隔120分钟'
        }],
      lineArr: [],
      checkboxGroup1: ["全部"],
      cities: ["全部", "到期设备", "离线设备"],
      treeData: [],
      treeForm: {},
      option: {
        column: [
          {
            label: "设备名称",
            prop: "deviceName",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入设备名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "设备状态",
            prop: "deviceSwitch",
            type: "select",
            dicUrl: "/blade-system/dict-biz/getDictBizData?code=currentState",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择设备状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "电池编码",
            prop: "deviceCode",
            display: false,
            rules: [
              {
                required: true,
                message: "请输入设备编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用时长",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入使用时长",
                trigger: "blur",
              },
            ],
          },
          {
            label: "物联网卡号",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入物联网卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "所在位置",
            prop: "simCode",
            rules: [
              {
                required: false,
                message: "请输入所在位置",
                trigger: "blur",
              },
            ],
          },
          {
            label: "状态",
            prop: "deviceDesc",
            hide: true,
            type: "textarea",
            rules: [
              {
                required: false,
                message: "请输入状态",
                trigger: "blur",
              },
            ],
          }
        ],
      },
      isShowWarningFont: false,
      isHavePathData: false,
      queryForm: {
        devicesn: this.$route.query.devicesn,
      },
      devEquList: [],
      loading: false,
      loading1: false,
      timer: null,
      timer1: null,
      mountedTimer: null,
      // 组件销毁标志
      isDestroyed: false
    };
  },

  props: {
    dataTime: {
      type: Object,
      default: {},
    },
    softVer: {
      type: String,
      default: {},
    },
    status: {
      type: Number,
      default: {},
    },
  },
  watch: {
    dataTime(data) {
      this.queryForm.startTime = data.startTime;
      this.queryForm.endTime = data.endTime;
      this.queryForm.devicesn = data.devicesn;
      this.handleQuery()
    }
  },
  mounted() {
    this.init();
    this.initAMapLoaderInitMap();
    // 使用 setTimeout 延迟执行
    this.mountedTimer = setTimeout(() => {
      const objectElement = document.querySelector('object');
      if (objectElement) {
        objectElement.style.position = 'static';
      }
    }, 1000);
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  methods: {
    async showPreciseLocation() {
      if (!this.queryForm.devicesn) {
        this.$message.warning("请先选择设备");
        return;
      }
      this.loading = true;
      try {
        const res = await getTop5PointData(this.queryForm.devicesn);
        if (res.data.data && res.data.data.length > 0) {
          this.preciseLocation = res.data.data[0];
          this.displayPreciseLocation();
        } else {
          this.$message.warning("该设备暂无定位数据");
        }
      } catch (error) {
        console.error("获取精准定位失败:", error);
        this.$message.error("获取精准定位失败");
      } finally {
        this.loading = false;
      }
    },

    displayPreciseLocation() {
      if (!mapInstance || !this.preciseLocation) return;

      // 清除现有地图元素
      this.clearMapElements();

      const position = [this.preciseLocation.position[0], this.preciseLocation.position[1]];

      // 创建精准定位标记
      preciseMarker = new AMap.Marker({
        map: mapInstance,
        position: position,
        icon: new AMap.Icon({
          image: '/img/bms.png',
          size: new AMap.Size(50, 50),
          imageSize: new AMap.Size(50, 50)
        }),
        offset: new AMap.Pixel(-10, -30)
      });

      // 创建信息窗口
      infoWindowInstance = new AMap.InfoWindow({
        content: `
          <div style="padding: 12px; min-width: 220px; max-width: 280px; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.15); background: white; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">
            <div style="margin-bottom: 6px;">
              <span style="display: inline-block; width: 60px; color: #666;">时间：</span>
              <span style="color: #333;">${this.preciseLocation.time || '未知'}</span>
            </div>
            <div style="margin-bottom: 0;">
              <span style="display: inline-block; width: 60px; color: #666;">地址：</span>
              <span style="color: #333; word-break: break-all;">${this.preciseLocation.placeName || '未知'}</span>
            </div>
            <div style="margin-top: 10px; padding-top: 8px; border-top: 1px dashed #eee; text-align: right;">
              <span style="font-size: 12px; color: #999;">高德地图</span>
            </div>
          </div>
        `,
        offset: new AMap.Pixel(0, -40),
        border: 'none',
        closeWhenClickMap: true
      });

      // 打开信息窗口
      infoWindowInstance.open(mapInstance, position);
      mapInstance.setCenter(position);
      mapInstance.setZoom(19);

      // 点击标记也显示信息窗口
      preciseMarker.on('click', () => {
        infoWindowInstance.open(mapInstance, position);
      });
    },

    init() {
      // this.getTreeData();
      /* getBatterList().then((res) => {
         var data = res.data.data;
         data.forEach((item) => {
           this.devEquList.push({
             label: item.equNo,
             value: item.equNo,
           });
         });


       });*/

      this.queryForm.startTime = new Date().toISOString().split("T")[0] + " 00:00:00";
      this.queryForm.endTime = new Date().toISOString().split("T")[0] + " 23:59:59";

      var deviceSn = localStorage.getItem('devicesn');
      if (deviceSn) {
        this.queryForm.devicesn = deviceSn;
        this.handleQuery();
      }
    },

    openAlarmap() {
      this.openDialogVisible = true;
      this.AlarmForm.startTime = new Date().toISOString().split("T")[0] + " 00:00:00";
      this.AlarmForm.endTime = new Date().toISOString().split("T")[0] + " 23:59:59";
      this.AlarmForm.devicesn = this.queryForm.devicesn;
    },

    fastreport() {
      if (this.status === 2) {
        this.$message.warning("该设备离线，无法使用该功能！")
        return;
      }
      if (this.softVer < 1014) {
        this.$message.warning("该设备不支持报警功能！")
        return;
      }
      this.isFlashing = !this.isFlashing;
      if (this.isFlashing === false) {
        clearInterval(this.timer1);
        this.$notify({
          title: '报警数据快速上报',
          message: h('i', {style: 'color: teal'}, '指令正停止下发中....')
        });
        return;
      }

      this.$notify({
        title: '报警数据快速上报',
        message: h('i', {style: 'color: teal'}, '指令正在下发中....')
      });

      let deviceNo = this.queryForm.devicesn;
      let uuid1 = uuid();
      let firstExecutionTime = null;
      let isMapInitialized = false;
      let taskDuration = 10 * 60 * 1000;
      let startTime = Date.now();

      const executeTask = async () => {
        if (!firstExecutionTime) {
          firstExecutionTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
        }

        if (Date.now() - startTime >= taskDuration) {
          this.stopTask();
          return;
        }

        try {
          const res = await PcIssuedData({dataType: "C9", filedVal: "A9", batNo: deviceNo, uuid: uuid1});
          const data = res.data.data;

          if (data.uuid === uuid1 && !data.error) {
            const parse = JSON.parse(data.data);
            if (parse.Data === 1) {
              const gpsData = await getGPSData(
                  this.queryForm.devicesn,
                  firstExecutionTime,
                  this.queryForm.endTime
              );

              if (gpsData.data.data.length === 0) {
                this.isHavePathData = false;
                this.isShowWarningFont = true;
                this.loading = false;
                return;
              }

              this.isHavePathData = true;
              this.isShowWarningFont = false;
              const lineArr = gpsData.data.data.map((items) => [items.lon, items.lat]);
              this.lineArr = lineArr;

              if (!isMapInitialized) {
                this.initMap(lineArr);
                isMapInitialized = true;
              } else {
                this.updateMapTrajectory(lineArr);
              }

              this.loading = false;
            }
          }
        } catch (error) {
          console.error('请求出错:', error);
          this.$message({type: 'error', message: '网络请求失败，请稍后重试！'});
        }
      };

      executeTask();
      this.timer1 = setInterval(executeTask, 8000);
    },

    stopTask() {
      clearInterval(this.timer1);
      this.isFlashing = false; // 停止闪烁动画
      this.$notify({
        title: '报警数据快速上报',
        message: h('i', {style: 'color: teal'}, '指令下发已停止')
      });
    },

    initMap(lineArr) {
      if (!mapInstance) return;

      this.clearMapElements();

      // 添加起点标记
      startMarkerInstance = new AMap.Marker({
        map: mapInstance,
        position: lineArr[0],
        icon: "https://webapi.amap.com/theme/v1.3/markers/n/start.png",
        offset: new AMap.Pixel(-13, -50),
      });

      // 添加终点标记
      endMarkerInstance = new AMap.Marker({
        map: mapInstance,
        position: lineArr[lineArr.length - 1],
        icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png",
        offset: new AMap.Pixel(-13, -30),
      });

      // 添加车辆标记
      const startIcon = new AMap.Icon({
        image: "/img/bms.png",
        size: new AMap.Size(40.125, 40),
        imageSize: new AMap.Size(40.125, 40),
      });

      markerInstance = new AMap.Marker({
        map: mapInstance,
        position: lineArr[0],
        icon: startIcon,
        autoRotation: true,
        offset: new AMap.Pixel(-26, -21),
        //   angle: -90,
      });

      // 绘制完整轨迹
      polylineInstance = new AMap.Polyline({
        map: mapInstance,
        path: lineArr,
        showDir: true,
        strokeColor: "#28F",
        strokeWeight: 6,
      });

      // 绘制已通过轨迹
      passedPolylineInstance = new AMap.Polyline({
        map: mapInstance,
        strokeColor: "#AF5",
        strokeWeight: 6,
      });

      // 添加移动事件监听
      markerInstance.on("moving", (e) => {
        passedPolylineInstance.setPath(e.passedPath);
      });

      setTimeout(() => {
        mapInstance.setFitView();
      }, 500);
    },

    updateMapTrajectory(newLineArr) {
      if (!mapInstance || !polylineInstance) return;

      polylineInstance.setPath(newLineArr);

      if (endMarkerInstance) {
        endMarkerInstance.setPosition(newLineArr[newLineArr.length - 1]);
      }

      if (markerInstance && newLineArr.length > 0) {
        markerInstance.setPosition(newLineArr[newLineArr.length - 1]);
      }
    },

    exportAlarmMessage() {
      this.loading1 = true;
      let blobUrl = null;

      this.$confirm(`此操作将导出电池编号为："${this.queryForm.devicesn}" 行驶路径,且单次导出时间不能超过4小时, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 将字符串格式的开始时间和结束时间转换为Date对象
        const startTime = dayjs(this.queryForm.startTime);
        const endTime = dayjs(this.queryForm.endTime);
        const hoursDiff = endTime.diff(startTime, 'hour', true); // true表示返回小数

        if (hoursDiff > 4) {
          console.log('超过4小时');
          this.$message.warning("导出行驶路径单次时间范围不能超过4小时,请重新选择时间！")
          this.loading1 = false;
          return;
        }
        this.$message({
          type: 'info',
          message: '数据正在导出请稍等！'
        });
        exportGPSData(
            this.queryForm.devicesn,
            this.queryForm.startTime,
            this.queryForm.endTime,
            {responseType: 'blob'}
        ).then(res => {
          this.openDialogVisible = false;
          const blob = new Blob([res.data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
          blobUrl = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = blobUrl;

          const contentDisposition = res.headers['content-disposition'];
          let fileName = 'gps_data.xlsx';
          if (contentDisposition) {
            const matches = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (matches != null && matches[1]) {
              fileName = matches[1].replace(/['"]/g, '');
            }
          }

          link.download = fileName;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();

          // 确保DOM元素被正确移除
          setTimeout(() => {
            if (document.body.contains(link)) {
              document.body.removeChild(link);
            }
          }, 100);

          this.$message({
            type: 'success',
            message: '数据导出成功',
          });
        }).catch((error) => {
          console.error('导出数据失败:', error);
          this.$message({
            type: 'error',
            message: '数据导出失败，请稍后重试',
          });
        }).finally(() => {
          this.loading1 = false;
          // 清理Blob URL
          if (blobUrl) {
            URL.revokeObjectURL(blobUrl);
          }
        });
      }).catch(() => {
        /*this.$message({
          type: 'info',
          message: '已取消'
        });*/
        this.loading1 = false;
      });
    },

    getTreeData() {
      getBladeSystemDeptThree().then((res) => {
        this.treeData = res.data.data;
      });
    },

    processAddress(address) {
      return address.replace(/[\u3000\s]/g, '');
    },

    handleStart() {
      if (markerInstance) {
        markerInstance.moveAlong(this.lineArr, 1500);
      }
    },

    changeDevice(val) {
      localStorage.setItem('devicesn', val.value);
      this.queryForm.startTime = this.dataTime.startTime;
      this.queryForm.endTime = this.dataTime.endTime;
    },

    async handleQuery() {
      if (!this.queryForm.devicesn) {
        this.$message.warning("请先选择设备");
        return;
      }

      if (this.queryForm.startTime > this.queryForm.endTime) {
        this.$message.warning("选择开始时间不能大于结束时间");
        return;
      }

      this.clearMapElements();
      this.lineArr = [];
      this.loading = true;

      try {
        const gpsData = await getGPSData(
            this.queryForm.devicesn,
            this.queryForm.startTime,
            this.queryForm.endTime
        );

        const data = gpsData.data.data;
        this.isHavePathData = data.length > 0;

        if (!this.isHavePathData) {
          this.isShowWarningFont = true;
          // this.$message.warning("暂时无上报定位数据")
          this.tableData = []
          this.loading = false;
          return;
        }

        this.isShowWarningFont = false;
        const lineArr = data.map((items) => [items.lon, items.lat]);
        this.lineArr = lineArr;

        if (mapInstance) {
          mapInstance.clearMap();

          // 添加终点标记
          endMarkerInstance = new AMap.Marker({
            map: mapInstance,
            position: lineArr[lineArr.length - 1],
            icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png",
            offset: new AMap.Pixel(-13, -30),
          });
          // 获取断点数据
          const breakPointsRes = await getIotdbGpsPointData(
              this.queryForm.devicesn,
              this.queryForm.startTime,
              this.queryForm.endTime,
              this.intervalTime
          );

          const breakPoints = breakPointsRes.data.data;
          this.tableData = breakPoints.slice(-5);

          // 添加断点标记和信息窗口
          breakPoints.forEach((point) => {
            const breakMarker = new AMap.Marker({
              map: mapInstance,
              position: point.position,
              icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mid.png',
              offset: new AMap.Pixel(-13, -30),
            });

            const breakInfoWindow = new AMap.InfoWindow({
              content: `<div style="padding: 5px;color:red;">地址：${point.placeName}<br>时间：${point.time}</div>`,
              offset: new AMap.Pixel(0, -30),
            });

            // 保存引用以便清理
            breakMarkers.push(breakMarker);
            breakInfoWindows.push(breakInfoWindow);

            breakMarker.on('mouseover', () => {
              breakInfoWindow.open(mapInstance, breakMarker.getPosition());
            });

            breakMarker.on('mouseout', () => {
              breakInfoWindow.close();
            });
          });

          const startIcon = new AMap.Icon({
            image: "/img/bms.png",
            imageSize: new AMap.Size(40.125, 40),
          });

          markerInstance = new AMap.Marker({
            map: mapInstance,
            position: lineArr[0],
            icon: startIcon,
            autoRotation: false,
            offset: new AMap.Pixel(-26, -33),
            zIndex: 9999
          });

          // 绘制轨迹
          polylineInstance = new AMap.Polyline({
            map: mapInstance,
            path: lineArr,
            showDir: true,
            strokeColor: "#28F",
            strokeWeight: 6,
          });

          passedPolylineInstance = new AMap.Polyline({
            map: mapInstance,
            strokeColor: "#AF5",
            strokeWeight: 6,
          });

          markerInstance.on("moving", (e) => {
            passedPolylineInstance.setPath(e.passedPath);
          });

          setTimeout(() => {
            mapInstance.setFitView();
          }, 500);

          this.timer = setTimeout(() => {
            this.handleStart();
          }, 1000);
        }
      } catch (error) {
        //  console.error("获取轨迹数据失败:", error);
        this.$message.error("获取轨迹数据失败");
      } finally {
        this.loading = false;
      }
    },

    initAMapLoaderInitMap() {
      AMapLoaderInitMap({
        afterRequest: (AMap) => {
          mapInstance = new AMap.Map("AlarmapContainer", {
            zoom: 4,
            zooms: [3, 21],
            center: [105.397428, 34.90923],
          });
        },
      });
    },

    clearMapElements() {
      if (mapInstance) {
        mapInstance.clearMap();
      }

      // 清除所有事件监听
      if (markerInstance) {
        markerInstance.off("moving");
        markerInstance.off("click");
        markerInstance.off("mouseover");
        markerInstance.off("mouseout");
        markerInstance = null;
      }

      if (polylineInstance) {
        polylineInstance = null;
      }
      if (passedPolylineInstance) {
        passedPolylineInstance = null;
      }
      if (startMarkerInstance) {
        startMarkerInstance = null;
      }
      if (endMarkerInstance) {
        endMarkerInstance = null;
      }

      if (infoWindowInstance) {
        infoWindowInstance.close();
        infoWindowInstance = null;
      }

      // 清理局部地图元素
      preciseMarker = null;
      breakMarkers = [];
      breakInfoWindows = [];
    },
    /**
     * 清理所有定时器
     */
    clearAllTimers() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      if (this.timer1) {
        clearInterval(this.timer1);
        this.timer1 = null;
      }

      if (this.mountedTimer) {
        clearTimeout(this.mountedTimer);
        this.mountedTimer = null;
      }
    },

    /**
     * 清理地图相关资源
     */
    cleanupMapResources() {
      // 清理所有地图实例
      if (mapInstance) {
        try {
          // 移除所有覆盖物
          mapInstance.clearMap();

          // 销毁地图实例
          mapInstance.destroy();
        } catch (error) {
          console.error('销毁地图实例出错:', error);
        } finally {
          mapInstance = null;
        }
      }

      // 清理所有标记和覆盖物
      const mapElements = [
        markerInstance,
        polylineInstance,
        passedPolylineInstance,
        startMarkerInstance,
        endMarkerInstance,
        infoWindowInstance
      ];

      mapElements.forEach((element, index) => {
        if (element) {
          try {
            // 移除事件监听器
            if (element.off) {
              element.off('moving');
              element.off('click');
              element.off('mouseover');
              element.off('mouseout');
            }

            // 移除覆盖物
            if (element.setMap) {
              element.setMap(null);
            }

            // 关闭信息窗口
            if (element.close) {
              element.close();
            }
          } catch (error) {
            console.error('清理地图元素出错:', error);
          }
        }
      });

      // 重置所有地图元素引用
      markerInstance = null;
      polylineInstance = null;
      passedPolylineInstance = null;
      startMarkerInstance = null;
      endMarkerInstance = null;
      infoWindowInstance = null;

      // 清理局部地图元素
      preciseMarker = null;
      breakMarkers = [];
      breakInfoWindows = [];
    },

    /**
     * 清理事件监听和数据引用
     */
    cleanupEventAndData() {
      // 停止闪烁动画
      this.isFlashing = false;

      // 清理数据引用
      this.lineArr = [];
      this.tableData = [];
      this.preciseLocation = null;
      this.devEquList = [];
      this.treeData = [];
      this.breakPoints = [];

      // 清理表单数据
      this.queryForm = {
        devicesn: '',
        startTime: '',
        endTime: ''
      };

      this.AlarmForm = {};

      // 清理状态变量
      this.isShowWarningFont = false;
      this.isHavePathData = false;
      this.loading = false;
      this.loading1 = false;
      this.openDialogVisible = false;
    }
  },
  beforeUnmount() {
    // 设置组件销毁标志
    this.isDestroyed = true;

    // 清理所有定时器
    this.clearAllTimers();

    // 清理所有地图相关资源
    this.cleanupMapResources();

    // 清理所有事件监听和数据引用
    this.cleanupEventAndData();
  }
};
</script>
<style lang="scss" scoped>
:root {
  --el-text-color-regular: #000000;
}

:deep(.el-table thead.is-group th.el-table__cell) {
  background-color: white;
  text-align: center;
}

.warningFont {
  text-align: center;
  color: red;
  line-height: 30px;
}

.location-container {
  width: 100%;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 5px;

  &-below {
    width: 345px;
    position: absolute;
    border: 15px;
    left: 15px;
    z-index: 2;
    background-color: #ffffff;

    :deep(.el-table__cell) {
      background-color: #ffffff;
    }

    .el-table__cell {
      background-color: #ffffff;
    }
  }

  &-haeder {
    width: 345px;
    position: absolute;
    top: 20px;
    left: 15px;
    z-index: 2;
    background-color: #ffffff;
    padding: 20px 20px;

    :deep(.el-form-item__label) {
      color: #000;
    }

    :deep(.el-input__inner) {
      color: #000;
    }
  }

  &-left {
    width: 20%;
    height: 100%;
    padding: 5px;

    &-title {
      display: flex;
      justify-content: space-between;
      padding: 10px;
    }
  }

  &-right {
    width: 100%;
    height: 100%;
    min-height: 680px;
    position: relative;
    padding: 10px;
  }
}

.AlarmapContainer {
  width: 100%;
  height: 100%;
}
</style>
<style>
.el-table__header {
  width: 0;
}

.label {
  color: #747373; /* 标签颜色设置为灰色 */
  font-weight: bold; /* 标签字体加粗 */
}

@keyframes pulseFlash {
  0% {
    transform: scale(1);
    filter: brightness(1);
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(0.8) hue-rotate(-50deg);
    box-shadow: 0 0 15px 8px rgba(255, 0, 0, 0.2);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
  }
}

.flashing {
  animation: pulseFlash 1.2s infinite;
}

.el-input__inner {
  color: #000;
}
</style>
