.login-container {
  display: flex;
  align-items: center;
  justify-content: end;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  // background-image: url("/img/bg/bg.png");
  background: linear-gradient(180deg, #000000 0%, #020202 100%);
  background-size: 100% 100%;
  overflow: hidden;
  box-sizing: border-box;

  // padding-right: 200px;
  .el-input__inner {
    color: #ffffff;
  }

  div {
    box-sizing: border-box;
  }

  &-title {
    font-size: 88px;
    font-weight: bold;
    position: absolute;
    left: -160px;
    top: 35%;
    transform: translate(0px, -50%);
    color: #ffffff;

    img {
      width: 800px;
    }
  }

  &-bagUrl {
    width: 100%;
    position: absolute;
    bottom: 0;
    animation: logo-imag-animation 3s linear 0s 1 alternate forwards;
    z-index: 0;
  }


  @-webkit-keyframes logo-imag-animation {
    0% {
      -webkit-transform: scale(1.2, 1.2);
      transform: scale(1.2, 1.2);
    }

    100% {
      -webkit-transform: scale(1, 1);
      transform: scale(1, 1);
    }
  }

  @keyframes logo-imag-animation {
    0% {
      -webkit-transform: scale(1.2, 1.2);
      transform: scale(1.2, 1.2);
    }

    100% {
      -webkit-transform: scale(1, 1);
      transform: scale(1, 1);
    }
  }

  &-block {
    width: 1178npx;
    height: 650px;
    background: #2c2c2c;
    box-shadow: 0px 4px 8px 2px rgba(0, 0, 0, 0.04), 0px 2px 6px 0px rgba(0, 0, 0, 0.06), 0px 0px 4px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px 8px 8px 8px;
    display: flex;

    &-left {
      min-width: 378npx;
      height: 100%;
      // background: #000000;
      background: url(https://static.sinoclick.com/website/prod/_next/static/media/<EMAIL>);
      position: relative;
      padding-top: 41npx;
      color: #FFFFFF;

      &-logo {
        width: 146npx;
        height: 38.8npx;
        background-image: url("/img/logo_bg.png");
        background-size: 100% 100%;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0px);
        z-index: 2;
        top: 46npx;
      }

      &-footer {
        position: absolute;
        bottom: 46npx;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18npx;
      }

      &-crice {
        width: 110npx;
        height: 110npx;
        background-color: #39C487;
        border-radius: 50%;
        position: absolute;
        left: 56npx;
        top: 15npx;
        z-index: 1;
      }

      &-bg {
        width: 80%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    &-right {
      min-width: 800npx;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}

.login-weaper {
  margin: 0 auto;
  width: 1000px;
  box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);

  .el-input-group__append {
    border: none;
  }
}

.login-left,
.login-border {
  position: relative;
  min-height: 500px;
  align-items: center;
  display: flex;
}

.login-left {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  justify-content: center;
  flex-direction: column;
  background-color: #8b9aac;
  color: #fff;
  float: left;
  width: 50%;
  position: relative;
}

.login-left .img {
  width: 140px;
}

.login-time {
  position: absolute;
  left: 25px;
  top: 25px;
  width: 100%;
  color: #fff;
  font-weight: 200;
  opacity: 0.9;
  font-size: 18px;
  overflow: hidden;
}

.login-left .title {
  text-align: center;
  color: #fff;
  font-weight: bold;
  font-size: 30px;
  letter-spacing: 2px;
}

.login-border {
  border-left: none;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  color: #fff;
  background-color: #fff;
  width: 50%;
  float: left;
  box-sizing: border-box;
}

.login-main {
  margin: 0 auto;
  width: 65%;
  box-sizing: border-box;
}

.login-main>h3 {
  margin-bottom: 20px;
}

.login-main>p {
  color: #76838f;
}

.login-title {
  color: #333;
  margin-bottom: 40px;
  font-weight: 500;
  font-size: 22px;
  text-align: center;
  letter-spacing: 4px;
}

.login-menu {
  margin-top: 40px;
  width: 100%;
  text-align: center;

  a {
    color: #999;
    font-size: 12px;
    margin: 0px 8px;
  }
}

.login-submit {
  width: 100%;
  height: 45px;
  // // border: 1px solid #409EFF;
  // background: none;
  // font-size: 18px;
  // letter-spacing: 2px;
  // font-weight: 300;
  // // color: #409EFF;
  // cursor: pointer;
  margin-top: 30px;
  // font-family: "neo";
  transition: 0.25s;
}

.login-form {
  margin: 10px 0;
  width: 400npx;
  background-color: #ffffff17;
  padding: 20px;
  padding-bottom: 10px;
  border-radius: 15px;
  position: fixed;
  right: 200px;
  top: 50%;
  z-index: 2;
  transform: translate(0px, -50%);

  i {
    color: #333;
  }

  .el-form-item__content {
    width: 100%;
  }

  .el-form-item__label {
    font-size: 20npx;
    margin-bottom: 15npx;
    font-weight: 700;
    color: #ffffff;
  }

  .el-form-item {
    margin-bottom: 12px;
    display: block;
  }

  .el-input {
    .el-input__wrapper {
      padding: 10npx 15npx;
      text-indent: 5px;
      background: transparent;
      box-shadow: none;
      border-radius: 0;
      background: #000;
      border-radius: 5px;
      color: #fff;
      //  border: 1px solid rgb(235, 237, 242);
    }

    .el-input__prefix {
      i {
        padding: 0 5px;
        font-size: 16px !important;
      }
    }
  }
}

.login-code {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: 0 0 0 10px;
  width: 100%;
}

.login-code-img {
  width: 100%;
  height: 52npx;
  background-color: #fdfdfd;
  border: 1px solid #f0f0f0;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 5px;
  line-height: 38px;
  text-indent: 5px;
  text-align: center;
  cursor: pointer !important;
  border-radius: 5px;
}
