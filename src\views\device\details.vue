<template>
  <basic-container>
    <el-row :gutter="24" style="padding: 10px">
      <el-form :inline="true" :model="queryForm" class="queryForm">
        <el-form-item label="设备名称" prop="devicesn">
          <el-input v-model="queryForm.devicesn" placeholder="请输入设备名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList">搜索</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-card shadow="hover">
          <div class="details-flex details-prod">
            <div>剩余电量</div>
            <div style="width: 260px">
              <el-progress :percentage="form.resiEle" :stroke-width="15" striped />
            </div>
          </div>
          <div class="details-flex details-prod">
            <div>设备状态</div>
            <div>
              {{ status[form.eleStatus] }}
            </div>
          </div>
          <div class="details-flex details-prod">
            <div>循环次数</div>
            <div>({{ form.sumCycEle }})次</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="16">
        <el-card shadow="hover">
          <div class="details-flex">
            <div class="details-ecahts" id="electric"></div>
            <div class="details-ecahts" id="voltage"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>基础数据：</span>
          </template>
          <div class="details-flex details-database" v-for="(items, index) in basisList">
            <div>{{ items.label }}</div>
            <div>{{ items.value }}</div>
          </div>
          <div class="details-flex details-database"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>设备状态：</span>
          </template>
          <div class="details-flex details-database" v-for="(items, index) in statusList">
            <div>{{ items.label }}</div>
            <div>{{ items.value }}</div>
          </div>
          <div class="details-flex details-database"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>设备温度：</span>
          </template>
          <div class="details-flex details-database" v-for="(items, index) in battery">
            <div>{{ items.label }}</div>
            <div>{{ items.value }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-card shadow="hover">
      <template #header>
        <span>电芯单体电压</span>
      </template>
      <el-row :gutter="48">
        <el-col :span="6" v-for="(items, index) in voltageList">
          <el-card shadow="hover">
            <div class="details-flex">
              <div>{{ index }}节电芯：</div>
              <div>{{ items }}V</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </basic-container>
</template>

<script>
import * as echarts from "echarts";
import deviceView from "./deviceView.vue";
import { mapGetters } from "vuex";
import { initEchartsPie } from "./ecahts";
import { validatenull } from "utils/validate";
import { getlastDevPage as getList } from "@/api/device/card";
export default {
  components: { deviceView },
  data() {
    return {
      loading: false,
      dataList: [],
      projectList: [],
      queryForm: {
        devicesn: this.$route.query.devicesn,
      },
      form: {
        resiEle: 0,
      },
      loading: false,
      page: {
        pageSize: 12,
        currentPage: 1,
        total: 0,
      },
      basisList: [
        {
          label: "电池总电压(V)",
          value: `0`,
        },
        {
          label: "电流(A)",
          value: `0`,
        },
        {
          label: "剩余电量(%)",
          value: `0`,
        },
        {
          label: "累积循环放电次数",
          value: `0`,
        },
        {
          label: "预计可用时间(h)",
          value: "0",
        },
        {
          label: "系统软件版本",
          value: "0",
        },
      ],
      voltageList: [0, 0, 0, 0, 0, 0, 0, 0],
      statusList: [
        {
          label: "电池状态",
          value: "正常",
        },
        {
          label: "充电 MOS 状态",
          value: "开",
        },
        {
          label: "放电 MOS 状态",
          value: "开",
        },
        {
          label: "强启状态",
          value: "开",
        },
        {
          label: "智能加热状态",
          value: "开",
        },
        {
          label: "加热功能状态",
          value: "开",
        },
      ],
      battery: [
        {
          label: "电芯1温度(℃)",
          value: `0`,
        },
        {
          label: "电芯2温度(℃)",
          value: `0`,
        },
        {
          label: "电芯3温度(℃)",
          value: `0`,
        },
        {
          label: "电芯4温度(℃)",
          value: `0`,
        },
        {
          label: "充放电MOS温度(℃)",
          value: `0`,
        },
        {
          label: "加热片温度(℃)",
          value: `0`,
        },
        {
          label: "MCU温度(℃)",
          value: `0`,
        },
      ],
      status: {
        0: "待机",
        1: "预充",
        2: "放电",
        3: "充电",
        4: "待机",
        5: "短路保护",
        6: "AFE异常",
        7: "预充次数超限",
        8: "关机",
        9: "NTC异常",
        10: "电池断线",
        11: "放电过流1",
        12: "电池低电",
        13: "充电过流",
        14: "充电完成",
      },
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      if (validatenull(this.queryForm.devicesn)) {
        return null;
      }
      this.$nextTick(() => {
        getList(this.queryForm.devicesn).then((res) => {
          const data = res.data.data;
          this.form = data[0];
          // 电压
          this.initEchartsPie(this.form.batterVol1, this.form.batterVol2);
          // 基础数据
          this.basisList[0].value = this.form.batterVol1;
          this.basisList[1].value = this.form.eleFlow;
          this.basisList[2].value = this.form.resiEle;
          this.basisList[3].value = this.form.sumCycEle;
          this.basisList[4].value = this.form.estTime;
          this.basisList[5].value = this.form.softVer;
          // 设备状态
          this.statusList[0].value = this.status[this.form.eleStatus];
          this.statusList[1].value = this.form.devStatus[4] == 1 ? "开" : "关";
          this.statusList[2].value = this.form.devStatus[3] == 1 ? "开" : "关";
          this.statusList[3].value = this.form.devStatus[2] == 1 ? "开" : "关";
          this.statusList[4].value = this.form.devStatus[1] == 1 ? "开" : "关";
          this.statusList[5].value = this.form.devStatus[0] == 1 ? "开" : "关";
          // 电压
          this.voltageList[0] = this.form.batterVol1;
          this.voltageList[1] = this.form.batterVol2;
          this.voltageList[2] = this.form.batterVol3;
          this.voltageList[3] = this.form.batterVol4;
          this.voltageList[4] = this.form.batterVol5;
          this.voltageList[5] = this.form.batterVol6;
          this.voltageList[6] = this.form.batterVol7;
          this.voltageList[7] = this.form.batterVol8;
          // 温度
          this.battery[0].value = this.form.temperature1;
          this.battery[1].value = this.form.temperature2;
          this.battery[2].value = this.form.temperature3;
          this.battery[3].value = this.form.temperature4;
          this.battery[4].value = this.form.temperature5;
          this.battery[5].value = this.form.temperature6;
          this.battery[6].value = this.form.temperature7;
          this.battery[7].value = this.form.temperature8;
        });
      });
    },
    initEchartsPie(val1, val2) {
      // 基于准备好的dom，初始化echarts实例
      let electricEchats = echarts.init(document.getElementById("electric"));
      // 绘制图表
      electricEchats.setOption(
        initEchartsPie({ title: "电压", unit: "V", pointerData: val1 ? val1 : 35.2 }),
        true
      );

      let voltageEchats = echarts.init(document.getElementById("voltage"));
      // 绘制图表
      voltageEchats.setOption(
        initEchartsPie({ title: "电流", unit: "A", pointerData: val2 ? val2 : 33.2 }),
        true
      );
      window.addEventListener("resize", () => {
        electricEchats.resize();
        voltageEchats.resize();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
  .details {
    &-prod {
      height: 80px;
    }
    &-ecahts {
      width: 49%;
      height: 240px;
    }
    &-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    &-database {
      height: 30px;
      line-height: 30px;
      margin-top: 10px;
      div:nth-child(2) {
        background-color: rgb(238, 238, 238);
        width: 200px;
        text-align: center;
      }
    }
  }
</style>
