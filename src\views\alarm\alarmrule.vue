<template>
    <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
            :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave"
            @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
            @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
            <template #menu-left>
                <el-button type="danger" icon="el-icon-delete" plain v-if="permission.user_delete" @click="handleDelete">删 除
                </el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>
  
<script>
import { getList, getDetail, add, update, remove } from "@/api/alarm/alarmrule";
import { mapGetters } from "vuex";

export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                searchShow: true,
                span: 24,
                dialogWidth: '40%',
                searchMenuSpan: 12,
                border: true,
                index: true,
                viewBtn: true,
                selection: true,
                dialogClickModal: false,
                labelWidth: 180,
                searchLabelWidth: 110,
                column: [
                    {
                        label: "告警规则名称",
                        prop: "alarmRuleName",
                        rules: [{
                            required: true,
                            message: "请输入告警规则名称",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "告警等级",
                        prop: "alarmLevel",
                        type: 'select',
                        search: true,
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=alarmRuleLevel',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        dataType: 'number',
                        rules: [{
                            required: true,
                            message: "请输入选择告警等级",
                            trigger: "blur",
                        }]
                    },
                    {
                        label: "场景规则名称",
                        prop: "sceneId",
                        type: 'select',
                        search: true,
                        dicUrl: '/base/scenelinkage/page',
                        dicQuery: {
                            current: 1,
                            size: 999999
                        },
                        props: {
                            label: 'sceneName',
                            value: 'id',
                        },
                        dicFormatter: (res) => {
                            return res.data.records
                        },
                        rules: [{
                            required: true,
                            message: "请输入场景ID",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "状态",
                        prop: "alarmRuleStatus",
                        type: 'select',
                        dicData: [{
                            label: '运行中',
                            value: 1
                        }, {
                            label: '未启用',
                            value: 0
                        }],
                        rules: [{
                            required: true,
                            message: "请选择状态",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "告警规则描述",
                        prop: "alarmRuleDesc",
                        rules: [{
                            required: true,
                            message: "请输入告警规则描述",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "修改时间",
                        prop: "updateTime",
                        addDisplay: false,
                        editDisplay: false,
                    },
                ]
            },
            data: []
        };
    },
    computed: {
        ...mapGetters(['userInfo', 'permission']),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.user_add, false),
                viewBtn: this.validData(this.permission.user_view, false),
                delBtn: this.validData(this.permission.user_delete, false),
                editBtn: this.validData(this.permission.user_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        rowSave(row, done, loading) {
            add(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        }
    }
};
</script>
  
<style></style>
  