<template>
  <template v-for="item in menu">
    <el-menu-item
        v-if="validatenull(item[childrenKey]) && validRoles(item)"
        :index="getPath(item)"
        @click="open(item)"
        :key="item[labelKey]"
    >
      <template #title>
        <el-badge :value="badgeValue || 0" v-if="shouldShowBadge(item) && flag">
          <operate-order style="display: none" ref="operateOrder" @queryData="handleQueryData"></operate-order>
          <span :alt="item[pathKey]">{{ getTitle(item) }}</span>
        </el-badge>
        <span :alt="item[pathKey]" v-else>{{ getTitle(item) }}</span>
      </template>
    </el-menu-item>
    <el-sub-menu
        v-else-if="!validatenull(item[childrenKey]) && validRoles(item)"
        :index="getPath(item)"
        :key="item[labelKey]"
    >
      <template #title>
        <el-badge :value="item.badgeValue || 0" v-if="shouldShowBadge(item)">
          <span>{{ getTitle(item) }}</span>
        </el-badge>
        <span v-else>{{ getTitle(item) }}</span>
      </template>
      <template v-for="(child, cindex) in item[childrenKey]" :key="child[labelKey]">
        <el-menu-item
            :index="getPath(child)"
            @click="open(child)"
            v-if="validatenull(child[childrenKey])"
        >
          <template #title>
            <el-badge :value="child.badgeValue || 0" v-if="shouldShowBadge(child)">
              <span>{{ getTitle(child) }}</span>
            </el-badge>
            <span v-else>{{ getTitle(child) }}</span>
          </template>
        </el-menu-item>
        <sidebar-item v-else :menu="[child]" :key="cindex"></sidebar-item>
      </template>
    </el-sub-menu>
  </template>
</template>

<script>
import {mapGetters} from "vuex";
import {validatenull} from "utils/validate";
import website from "@/config/website";
import operateOrder from "@/views/battery/order/operateOrder.vue";
import eventBus from "@/utils/eventBus";

export default {
  name: "sidebarItem",
  components: {
    operateOrder,
  },
  data() {
    return {
      props: website.menu,
      badgeValue: 0,
      flag: true,
    };
  },
  props: {
    menu: Array,
  },
  computed: {
    ...mapGetters(["roles", "userInfo"]),
    labelKey() {
      return this.props.label;
    },
    pathKey() {
      return this.props.path;
    },
    queryKey() {
      return this.props.query;
    },
    iconKey() {
      return this.props.icon;
    },
    childrenKey() {
      return this.props.children;
    },
  },
  created() {
    eventBus.$on('dataChanged', this.handleQueryData);
  },
  beforeUnmount() {
    eventBus.$off('dataChanged', this.handleQueryData);
  },
  methods: {
    handleQueryData(data) {
      this.badgeValue = data
      this.flag = data > 0
    },
    validatenull,
    getPath(item) {
      return item[this.pathKey];
    },
    getTitle(item) {
      return this.$router.$avueRouter.generateTitle(item, this.props);
    },
    validRoles(item) {
      item.meta = item.meta || {};
      return item.meta.roles ? item.meta.roles.includes(this.roles) : true;
    },
    open(item) {
      if (item.path === "/preview") {
        window.location.href = "/cockpit";
      } else {
        this.$router.push({
          path: item[this.pathKey],
          query: item[this.queryKey],
        });
      }
    },
    shouldShowBadge(item) {
      return item.alias === "订单管理" && this.userInfo.dept_id === '1123598813738675201' ;
    },
  },
};
</script>
<style lang="scss" scoped>

:deep(.el-badge__content.is-fixed) {
  position: absolute;
  top: 9px;
  right: 5px;
  transform: translateY(-50%) translateX(100%);
  z-index: var(--el-index-normal);
}

</style>
