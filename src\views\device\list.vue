<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      style="width: 100%;"
    >
      <template #menu="{ size, row, index }">
        <el-button
          @click.stop="handleView(row, index)"
          icon="el-icon-view"
          text
          type="primary"
          :size="size"
          >查看</el-button
        >
      </template>
      <template #selectRange-form="{ disabled, size }">
        <div class="device-selectRange-map">
          <el-input
            style="margin-right: 2px"
            :disabled="true"
            :size="size"
            v-model="form.selectRange"
          ></el-input>
          <el-button icon="el-icon-map-location" @click="handleSelectRange"
            >地图
          </el-button>
        </div>
      </template>
    </avue-crud>

    <!-- tag弹窗 -->
    <el-dialog
      class="Tag-viewForm"
      :fullscreen="false"
      :show-close="true"
      align-center
      append-to-body
      v-model="tagModel"
      width="400px"
      title="设置标签"
    >
      <el-form label-width="60px">
        <el-form-item
          v-for="(item, index) in addTagValue"
          :key="item"
          :rules="{
            required: true,
            message: '必填',
            trigger: 'blur',
          }"
        >
          <el-select ref="addTagRef" v-model="item.label" clearable>
            <el-option
              v-for="item in tagOptions"
              :key="item.id"
              :label="item.markName"
              :value="item.markName"
            />
          </el-select>
          <!-- <el-button class="ml-5" @click.prevent="removeTag(item)">删除</el-button> -->
        </el-form-item>
        <!-- <el-form-item>
                    <span><el-link @click="addTag"><el-icon><CirclePlusFilled /></el-icon>新增标签</el-link></span>
                </el-form-item> -->
        <el-form-item>
          <span class="ml-10"
            ><el-button type="primary" @click="confirmAddTag">确定</el-button></span
          >
          <span class="ml-5"
            ><el-button type="info" @click="tagModel = false">取消</el-button></span
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 查看弹窗 -->
    <!-- <el-dialog
        class="Device-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="viewModel"
        width="1000px"
        title="查看设备信息"
        @close="closeViewDialog"
        >
            <el-form :model="form" label-width="120px">
            <el-row :gutter="20">
                <el-col :span="12">
                <el-form-item label="设备名称">
                    {{ viewData.deviceName }}
                </el-form-item>
                </el-col>
                <el-col :span="12">
                <el-form-item label="设备状态">
                    {{ viewData.deviceStatusN }}
                </el-form-item>
                </el-col>
            </el-row>
            <Div class="tabsContent">
                <el-tabs
                    v-model="activeName"
                    type="card"
                    class="device-tabs"
                >
                    <el-tab-pane label="设备信息" name="first">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="设备编码">
                                    {{ viewData.deviceCode }}
                                </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                <el-form-item label="设备名称">
                                    {{ viewData.deviceName }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="创建时间">
                                    {{ viewData.createTime }}
                                </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                <el-form-item label="sn码">
                                    {{ viewData.deviceSn }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="sim卡号">
                                    {{ viewData.simCode }}
                                </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                <el-form-item label="工作模式">
                                    {{ viewData.workModeName }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="当前状态">
                                    {{ viewData.deviceStatusN }}
                                </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                <el-form-item label="最后上线时间">
                                    {{ viewData.lastTime }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="经纬度">
                                    {{ viewData.longitude }}:{{ viewData.latitude }}
                                    <el-link type="primary"><el-icon><LocationFilled /></el-icon></el-link>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-divider />
                        <div class="rowTitle">标签信息<Span class="ml-10"><el-link type="primary" @click="tagModel = true"><el-icon><Edit /></el-icon><span class="ml-5">编辑</span></el-link></Span></div>
                        <el-row :gutter="20">
                            <el-col :span="24">
                            <el-form-item label="设置标签">
                                <div class="flex gap-2">
                                <el-tag
                                    v-for="item in viewData.labelLists"
                                    :key="item.id"
                                    type="info"
                                    class="ml-5"
                                    effect="light"
                                    round
                                    closable
                                    @close="removeTag(item)"
                                >
                                    {{ item.name }}
                                </el-tag>
                                </div>
                            </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="设备数据" name="second">
                        <device-data ref="deviceData" />
                    </el-tab-pane>
                </el-tabs>
            </Div>
            </el-form>
        </el-dialog> -->
    <!-- 地图弹窗 -->
    <el-dialog
      class="Device-MapModel"
      :fullscreen="true"
      header="null"
      :show-close="true"
      align-center
      append-to-body
      v-model="mapModel"
      width="100%"
      :destroy-on-close="true"
    >
      <MapModel :info="form" @change="confirmMap" />
    </el-dialog>
    <device-view ref="deviceView" />
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  saveMark,
  removeMark,
} from "@/api/device/list";
import { getList as getTagList } from "@/api/identifying/list";
import { getDictionary } from "@/api/system/dictbiz";
import MapModel from "./mapModel.vue";
import deviceView from "./deviceView.vue";
import { mapGetters } from "vuex";
export default {
  components: { deviceView, MapModel },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      tagModel: false,
      mapModel: false,
      addTagValue: [
        {
          id: "",
          name: "",
          type: "info",
        },
      ],
      addTagVisible: false,
      tagOptions: [],
      activeName: "first",
      viewModel: false,
      viewData: {},
      productList: [],
      deviceData: [],

      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
      
        column: [
          {
            label: "设备名称",
            prop: "deviceName",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入设备名称",
                trigger: "blur",
              },
            ],
          },

          {
            label: "设备状态",
            prop: "deviceSwitch",
            type: "select",

            dicUrl: "/blade-system/dict-biz/getDictBizData?code=currentState",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择设备状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "电池编码",
            prop: "deviceCode",

            display: false,
            rules: [
              {
                required: true,
                message: "请输入设备编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用时长",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入使用时长",
                trigger: "blur",
              },
            ],
          },
          {
            label: "物联网卡号",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入物联网卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "所在位置",
            prop: "simCode",
            rules: [
              {
                required: false,
                message: "请输入所在位置",
                trigger: "blur",
              },
            ],
          },
          {
            label: "状态",
            prop: "deviceDesc",
            hide: true,
            type: "textarea",
            rules: [
              {
                required: false,
                message: "请输入状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "设备名称",
            prop: "deviceName",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入设备名称",
                trigger: "blur",
              },
            ],
          },

          {
            label: "设备状态",
            prop: "deviceSwitch",
            type: "select",

            dicUrl: "/blade-system/dict-biz/getDictBizData?code=currentState",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择设备状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "电池编码",
            prop: "deviceCode",

            display: false,
            rules: [
              {
                required: true,
                message: "请输入设备编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用时长",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入使用时长",
                trigger: "blur",
              },
            ],
          },
          {
            label: "物联网卡号",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入物联网卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "所在位置",
            prop: "simCode",
            rules: [
              {
                required: false,
                message: "请输入所在位置",
                trigger: "blur",
              },
            ],
          },
          {
            label: "状态",
            prop: "deviceDesc",
            hide: true,
            type: "textarea",
            rules: [
              {
                required: false,
                message: "请输入状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "设备名称",
            prop: "deviceName",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入设备名称",
                trigger: "blur",
              },
            ],
          },

          {
            label: "设备状态",
            prop: "deviceSwitch",
            type: "select",

            dicUrl: "/blade-system/dict-biz/getDictBizData?code=currentState",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            slot: true,
            rules: [
              {
                required: true,
                message: "请选择设备状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "电池编码",
            prop: "deviceCode",

            display: false,
            rules: [
              {
                required: true,
                message: "请输入设备编码",
                trigger: "blur",
              },
            ],
          },
          {
            label: "使用时长",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入使用时长",
                trigger: "blur",
              },
            ],
          },
          {
            label: "物联网卡号",
            prop: "selectRange",
            rules: [
              {
                required: false,
                message: "请输入物联网卡号",
                trigger: "blur",
              },
            ],
          },
          {
            label: "所在位置",
            prop: "simCode",
            rules: [
              {
                required: false,
                message: "请输入所在位置",
                trigger: "blur",
              },
            ],
          },
          {
            label: "状态",
            prop: "deviceDesc",
            hide: true,
            type: "textarea",
            rules: [
              {
                required: false,
                message: "请输入状态",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.dept_add, false),
        viewBtn: this.validData(this.permission.dept_view, false),
        delBtn: this.validData(this.permission.dept_delete, false),
        editBtn: this.validData(this.permission.dept_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    confirmMap(e) {
      this.mapModel = false;
      const values = e.selectRange;
      this.form.latitude = values.latitude;
      this.form.longitude = values.longitude;
      this.form.selectRange = `${values.longitude},${values.latitude}`;
    },
    handleControls() {
      this.$router.push("/device/controls");
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      delete row.lastTime;
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    async handleView(row, index) {
      const workModeList = await this.getDict("deviceWorkMode");
      this.loadTagList(3);
      if (row !== null) {
        await getDetail(row.id).then((res) => {
          this.viewData = res.data.data;
          this.viewData.deviceStatusN = row.$deviceSwitch;
          this.viewData.workModeName = workModeList.filter(
            (_item) => _item.dictKey == row.deviceStatus
          )[0].dictValue;
          this.viewModel = true;
        });
      }
      this.$refs.deviceView.handleView(this.viewData);
      // this.$refs.deviceData.loadDeviceData({ deviceId: this.viewData.deviceSn, deviceName: this.viewData.deviceName });
    },
    removeTag(e) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeMark(e.id);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.viewData.labelLists.splice(this.viewData.labelLists.indexOf(e), 1);
        });
    },
    confirmAddTag() {
      // this.viewData.labelLists = [];
      // this.addTagValue.forEach(_item => {
      //     if (_item.label !== '') {
      //         this.viewData.labelLists.push(_item);
      //     }
      // })
      const arr = {
        markName: this.addTagValue[0].label,
        deviceId: this.viewData.id,
      };
      saveMark(arr).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.handleView(this.viewData);
        this.tagModel = false;
      });
    },
    closeViewDialog() {
      this.activeName = "first";
    },
    handleSelectRange() {
      this.mapModel = true;
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          const values = res.data.data;
          if (values.longitude) {
            values["selectRange"] = `${values.longitude},${values.latitude}`;
          }
          this.form = values;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(
        (res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        }
      );
    },
    async getDict(code) {
      let result = [];
      const query = {
        code: code,
      };
      result = await getDictionary(query).then((res) => {
        const data = res.data.data;
        return data;
      });
      return result;
    },
    loadTagList(type) {
      const query = {
        markType: type,
      };
      getTagList(1, 100, query).then((res) => {
        const data = res.data.data;
        this.tagOptions = data.records;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.ml-5 {
  margin-left: 5px;
}
.ml-10 {
  margin-left: 10px;
}
.Device-viewForm {
  .rowTitle {
    font-size: 18px;
    font-weight: bold;
    padding-left: 35px;
    padding-bottom: 20px;
  }
  .tabsContent {
    padding: 20px;
  }
  .el-divider--horizontal {
    margin-top: 0;
  }
}
.Device-MapModel {
  :global(.el-dialog__header) {
  }
  :global(.el-dialog__body) {
    height: 100%;
    padding: 0px;
  }
}

.device-selectRange-map {
  display: flex;
}
</style>
