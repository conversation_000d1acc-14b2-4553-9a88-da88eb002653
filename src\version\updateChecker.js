import { APP_VERSION, RELEASE_DATE, UPDATE_CONTENT } from './version';
import { createApp } from 'vue';
import UpdateModal from './UpdateModal.vue';

class UpdateChecker {
    constructor() {
        this.currentVersion = APP_VERSION;
        this.releaseDate = RELEASE_DATE;
        this.updateContent = UPDATE_CONTENT;

        // 存储键名包含版本号，确保版本隔离
        this.storageKey = `app_${APP_VERSION}_updated`;
        this.sessionKey = `app_${APP_VERSION}_session`;
        this.cookieKey = `app_${APP_VERSION}_cookie`;

        // 设置cookie有效期（默认365天）
        this.cookieExpires = 365;
    }

    // 三重检查：sessionStorage + localStorage + Cookie
    getUpdateStatus() {
        try {
            // 1. 检查sessionStorage（会话级存储）
            if (sessionStorage.getItem(this.sessionKey) === 'true') return true;

            // 2. 检查localStorage（持久化存储）
            if (localStorage.getItem(this.storageKey) === 'true') return true;

            // 3. 检查Cookie（最持久化方案）
            if (this.getCookie(this.cookieKey) === 'true') return true;

            return false;
        } catch (e) {
            console.error('更新状态检查失败:', e);
            return false;
        }
    }

    // 三重存储：确保更新状态可靠持久化
    markAsUpdated() {
        try {
            // 1. 存入sessionStorage（当前会话有效）
            sessionStorage.setItem(this.sessionKey, 'true');

            // 2. 存入localStorage（持久存储）
            localStorage.setItem(this.storageKey, 'true');

            // 3. 存入Cookie（跨会话持久存储）
            this.setCookie(this.cookieKey, 'true', this.cookieExpires);

            // 存储当前版本信息
            localStorage.setItem('app_current_version', this.currentVersion);
        } catch (e) {
            console.error('更新状态存储失败:', e);
        }
    }

    // 检查更新（主入口）
    // updateChecker.js
    checkUpdate() {
        try {
            if (!this.getUpdateStatus()) {
                this.showUpdateModal();
            }
        } catch (error) {
            console.error('检查更新时出现错误:', error);
        }
    }

    // 显示更新弹窗
    showUpdateModal() {
        const mountNode = document.createElement('div');
        document.body.appendChild(mountNode);

        const app = createApp(UpdateModal, {
            newVersion: this.currentVersion,
            updateContent: this.updateContent,
            updateTime: this.releaseDate,
            onUpdate: () => {
                this.clearCacheAndReload();
            }
        });

        app.mount(mountNode);
    }

    // 清除缓存并刷新页面
    clearCacheAndReload() {
        this.markAsUpdated();
        if (caches) {
            caches.keys().then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        return caches.delete(cacheName);
                    })
                );
            }).then(() => {
                window.location.reload(true);
            }).catch((error) => {
                console.error('清除缓存失败:', error);
                window.location.reload(true);
            });
        } else {
            window.location.reload(true);
        }
    }

    // Cookie操作方法
    setCookie(name, value, days) {
        const date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${date.toUTCString()};path=/`;
    }

    getCookie(name) {
        const cookieArr = document.cookie.split(';');
        for (let i = 0; i < cookieArr.length; i++) {
            const cookiePair = cookieArr[i].split('=');
            if (name === cookiePair[0].trim()) {
                return decodeURIComponent(cookiePair[1]);
            }
        }
        return null;
    }
}

export default new UpdateChecker();
