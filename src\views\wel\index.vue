<template>
  <basic-container>
    <div class="wel-container">
      <div class="welMap" id="welMap"></div>
      <div class="wel-container-left">
        <div
          class="wel-container-left-top animate__animated animate__bounceIn"
          v-if="isContainer"
        >
          <div class="wel-container-left-top-items" v-for="items in deviceArr">
            <img src="/img/home/<USER>" />
            <div class="wel-container-left-top-items-block">
              <div>{{ items.label }}</div>
              <div>{{ DeviceSumData[items.key] }}</div>
            </div>
          </div>
        </div>
        <div
          class="wel-container-left-center animate__animated animate__bounceIn"
          v-if="isContainer"
        >
          <div class="wel-container-title" style="margin-bottom: 0px">设备列表</div>
          <TableView :dataSource="deviceDataSource" :column="deviceColumn">
            <template #action>
              <div class="cockpit-container-block-deviceColumn2">
                <span style="color: #2a357a">查看</span>
              </div>
            </template>
          </TableView>
        </div>
      </div>

      <div class="wel-container-right">
        <div
          class="wel-container-right-top animate__animated animate__bounceIn"
          v-if="isContainer"
        >
          <div class="wel-container-title" style="margin-bottom: 0px">各省份数据统计</div>
          <div class="wel-container-right-total" id="totalEchats"></div>
        </div>
        <div
          class="wel-container-right-bottom animate__animated animate__bounceIn"
          v-if="isContainer"
        >
          <div class="wel-container-title">每月上报情况</div>
          <div class="wel-container-right-total" id="densityEchats"></div>
        </div>
      </div>
    </div>
  </basic-container>
</template>

<script>
import { mapGetters } from "vuex";
import * as echarts from "echarts";
import enableDrag from "easy-drag";
import mapCode from "./data-1594956060000-wzSYdj4lt.json";
echarts.registerMap("china", mapCode);
import { getList as getDeviceList, getTreeData } from "@/api/project/list";
import { initEchartsTotal, initDensityEchats } from "./ecahts";
import { initEchartsMap } from "./map";
import TabView from "./tab.vue";
import TableView from "./table.vue";
import { formatTime } from "@/utils/util";
import getBaseData from "./data";
import func from '@/utils/func';
import {getGPSData, getDevData, getDevCnt, getMapLocationCnt, getMonthList, getlastDevList} from "@/api/device/card"

let myDensityEchats = null;
let myEchartsTotal = null;
export default {
  components: {
    TabView,
    TableView,
  },
  data() {
    return {
      map: null,
      activeName: "first",
      isContainer: true,
      deviceDataSource: [],
      dateType1: 1,
      dateType2: 1,
      deviceColumn: [
        { title: "设备名称", dataIndex: "devicesn", width: 80, align: "left" },
        { title: "电池编号", dataIndex: "batterNo", width: 80, align: "left" },
        { title: "使用时长", dataIndex: "estTime", width: 80, align: "left" },
        { title: "状态", dataIndex: "eleStatus", width: 60, align: "left" },
        // { title: "剩余电量", dataIndex: "resiEle", width: 80, align: "left", slot: true },
      ],
      deviceDataSource1: [],
      deviceDataSource2: [],
      proposeColumn: [
        { title: "序号", dataIndex: "index", width: 50 },
        { title: "项目名称", dataIndex: "projectName", width: 100, align: "left" },
        { title: "建议内容", dataIndex: "cmf1", width: 100, align: "left" },
        { title: "位置", dataIndex: "projectName", width: 100, align: "left" },
      ],
      proposeColumn1: [
        { title: "序号", dataIndex: "index", width: 50 },
        { title: "设备名称", dataIndex: "alarmDevice", width: 120, align: "left" },
        { title: "告警内容", dataIndex: "alarmName", width: 100, align: "left" },
        { title: "位置导航", dataIndex: "action", width: 60, slot: true, align: "left" },
      ],
      proposeValue: 1,
      proposeData: [
        { label: "消杀建议", value: 1 },
        { label: "告警通知", value: 2 },
      ],
      totalData: [
        { label: "日", value: 1 },
        { label: "月", value: 2 },
        { label: "年", value: 3 },
      ],
      totalValue: 1,
      densityValue: 1,
      DeviceSumData: {
        deviceTotal: 0,
        onlineTotal: 0,
        faultsTotal: 0,
        offlineTotal: 0,
      },
      deviceArr: [
        {
          label: "设备总量",
          key: "deviceTotal",
        },
        {
          label: "在线总量",
          key: "onlineTotal",
        },
        {
          label: "故障设备",
          key: "faultsTotal",
        },
        {
          label: "离线总量",
          key: "offlineTotal",
        },
      ],
      status: {
        '0': '待机',
        '1': '预充',
        '2': '放电',
        '3': '充电',
        '4': '待机',
        '5': '短路保护',
        '6': 'AFE异常',
        '7': '预充次数超限',
        '8': '关机',
        '9': 'NTC异常',
        '10': '电池断线',
        '11': '放电过流1',
        '12': '电池低电',
        '13': '充电过流',
        '14': '充电完成',
      }
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {
    isContainer: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            myDensityEchats = null;
            myEchartsTotal = null;
            this.initEchartsTotal();
            this.initDensityEchats();
          });
        }
      },
      immediate: true,
    },
    totalValue: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.initEchartsTotal();
          });
        }
      },
      immediate: true,
    },
    densityValue: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.initDensityEchats();
          });
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * 初始化
     **/
    init () {
      this.$nextTick(() => {
        // 累计设备数量
        getDevCnt().then(res => {
          var data = res.data.data;
          this.DeviceSumData.deviceTotal = data.devCnt
          this.DeviceSumData.onlineTotal = data.onlieCnt
          this.DeviceSumData.offlineTotal = data.offlineCnt
          this.DeviceSumData.faultsTotal = data.alarmCnt
        })
        // 获取各个设备最新数据
        getlastDevList().then(res => {
          var data = res.data.data;
          data.forEach(dt => {
            dt.eleStatus = this.status[dt.eleStatus]
          })
          this.deviceDataSource = data;
        })
        // 地图
        getMapLocationCnt().then(res => {
          var data = res.data.data;
          data.map(param => {
            param.name = param.province.replace("省", ""),
            param.value = param.num ? param.num : 0,
            param.code = param.adcode
          })
          let welMapEchats = echarts.init(document.getElementById("welMap"));
          // 绘制图表
          welMapEchats.setOption(
            initEchartsMap(data),
            true
          );
          window.addEventListener("resize", () => {
            welMapEchats.resize();
          });

          // 省份统计
          if (!myEchartsTotal) {
            myEchartsTotal = echarts.init(document.getElementById("totalEchats"));
          }
          // 绘制图表
          myEchartsTotal.setOption(initEchartsTotal(data), true);
          window.addEventListener("resize", () => {
            myEchartsTotal.resize();
          });
        })
        // 获取上报情况
        getMonthList().then(res => {
          var data = res.data.data;

          // 基于准备好的dom，初始化echarts实例
          if (!myDensityEchats) {
            myDensityEchats = echarts.init(document.getElementById("densityEchats"));
          }

          var keys = Object.keys(data);
          var values = Object.values(data);
          var resData = [];
          for (var i = 0; i < keys.length; i++) {
            keys[i] = keys[i].split("-")[keys[i].split("-").length - 1];
          }
          const relust1 = keys;
          const relust2 = values;

          // 绘制图表
          myDensityEchats.setOption(
            initDensityEchats(relust2, relust1, this.densityValue),
            true
          );
          window.addEventListener("resize", () => {
            myDensityEchats.resize();
          });
        })
      })
    }
  },
  created() {
    this.init()
  },
  mounted() {
    this.initEchartsTotal();
    this.initDensityEchats();
    this.initEchartsMap()
  },
};
</script>

<style lang="scss" scoped>
  .wel-container-economize {
    width: 40npx !important;
    font-size: 20npx;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center !important;
    text-align: center !important;
    justify-content: center;
    box-shadow: none !important;
    border-radius: 0px !important;
    background-color: #79d1a9 !important;
    padding: 0px !important;
    flex-direction: column;
    div {
      padding-bottom: 10npx;
      width: 100%;
    }
  }

  :deep(.basic-container__card) {
    height: 100%;
  }

  :deep(.el-card__body) {
    height: 100%;
    padding: 0px;
  }

  #totalEchats {
    width: 100%;
    flex: 1;
    padding-top: 10px;
  }

  #densityEchats {
    width: 100%;
    flex: 1;
    padding-top: 10px;
  }
  .cockpit-container-block-deviceColumn2 {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #ff3939;
    img {
      width: 16npx;
      height: 16npx;
      margin-left: 4npx;
    }
  }
  .basic-container {
    padding: 0px !important;
    margin: 0px !important;
  }

  :global(#avue-view) {
    margin-bottom: 0px;
  }

  @mixin normalStyle() {
    width: 486npx;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.08);
    border-radius: 6npx 6npx 6npx 6npx;
    background-color: #ffffff;
    padding: 0px 15npx;
  }

  div {
    box-sizing: border-box;
  }

  .wel-container {
    height: 100%;
    width: 100%;
    position: relative;
    &-homeShow {
      width: 80npx;
      height: 80npx;
      position: fixed;
      right: 10npx;
      top: 130npx;
      cursor: pointer;
      z-index: 2;
    }
    &-tabs-postion {
      position: absolute;
      top: 40npx;
      left: 15npx;
      z-index: 1;
    }

    &-title {
      font-size: 16px;
      font-weight: 500;
      color: #2a357a;
      margin: 15px 0px;
      position: relative;
      padding-left: 12npx;
    }

    &-title::after {
      content: " ";
      width: 5npx;
      height: 21npx;
      display: block;
      background: #07b667;
      border-radius: 3px 3px 3px 3px;
      position: absolute;
      top: 2npx;
      left: 0px;
    }

    .welMap {
      width: 100%;
      height: 100%;
    }

    &-left,
    &-right {
      position: absolute;
      top: 10npx;
      height: 100%;
    }

    &-left {
      left: 10npx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 96%;
      top: 2%;

      &-top {
        height: 24%;
        @include normalStyle();
        display: flex;
        flex-wrap: wrap;

        &-items:nth-child(1),
        &-items:nth-child(2) {
          border-bottom: 1px solid #e5e6eb;
        }

        &-items {
          display: flex;
          width: 50%;
          height: 50%;
          align-items: center;
          justify-content: left;

          img {
            width: 40npx;
            height: 40npx;
            margin-right: 20npx;
          }

          &-block {
            div:nth-child(1) {
              font-size: 14npx;
              color: #333333;
              margin-bottom: 2npx;
            }

            div:nth-child(2) {
              font-size: 24npx;
              color: #333333;
            }
          }
        }
      }

      &-center {
        height: 73%;
        @include normalStyle();
        display: flex;
        flex-direction: column;
      }

      &-bottom {
        height: 36%;
        @include normalStyle();
        display: flex;
        flex-direction: column;
        position: relative;
        :deep(.wel-tabs) {
          margin-bottom: 0px;
        }
      }
    }

    &-right {
      right: 10npx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 96%;
      top: 2%;

      &-top {
        height: 49%;
        @include normalStyle();
        display: flex;
        flex-direction: column;
        position: relative;
        &-img {
        }
      }

      &-bottom {
        height: 49%;

        display: flex;
        flex-direction: column;
        @include normalStyle();
      }
    }
  }
</style>
