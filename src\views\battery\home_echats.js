// 各省份的地图json文件
const provinces = {
    上海: "/asset/data-1482909900836-H1BC_1WHg.json",
    河北: "/asset/data-1482909799572-Hkgu_yWSg.json",
    山西: "/asset/data-1482909909703-SyCA_JbSg.json",
    内蒙古: "/asset/data-1482909841923-rkqqdyZSe.json",
    辽宁: "/asset/data-1482909836074-rJV9O1-Hg.json",
    吉林: "/asset/data-1482909832739-rJ-cdy-Hx.json",
    黑龙江: "/asset/data-1482909803892-Hy4__J-Sx.json",
    江苏: "/asset/data-1482909823260-HkDtOJZBx.json",
    浙江: "/asset/data-1482909960637-rkZMYkZBx.json",
    安徽: "/asset/data-1482909768458-HJlU_yWBe.json",
    福建: "/asset/data-1478782908884-B1H6yezWe.json",
    江西: "/asset/data-1482909827542-r12YOJWHe.json",
    山东: "/asset/data-1482909892121-BJ3auk-Se.json",
    河南: "/asset/data-1482909807135-SJPudkWre.json",
    湖北: "/asset/data-1482909813213-Hy6u_kbrl.json",
    湖南: "/asset/data-1482909818685-H17FOkZSl.json",
    广东: "/asset/data-1482909784051-BJgwuy-Sl.json",
    广西: "/asset/data-1482909787648-SyEPuJbSg.json",
    海南: "/asset/data-1482909796480-H12P_J-Bg.json",
    四川: "/asset/data-1482909931094-H17eKk-rg.json",
    贵州: "/asset/data-1482909791334-Bkwvd1bBe.json",
    云南: "/asset/data-1482909957601-HkA-FyWSx.json",
    西藏: "/asset/data-1482927407942-SkOV6Qbrl.json",
    陕西: "/asset/data-1482909918961-BJw1FyZHg.json",
    甘肃: "/asset/data-1482909780863-r1aIdyWHl.json",
    青海: "/asset/data-1482909853618-B1IiOyZSl.json",
    宁夏: "/asset/data-1482909848690-HJWiuy-Bg.json",
    新疆: "/asset/data-1482909952731-B1YZKkbBx.json",
    北京: "/asset/data-1482818963027-Hko9SKJrg.json",
    天津: "/asset/data-1482909944620-r1-WKyWHg.json",
    重庆: "/asset/data-1482909775470-HJDIdk-Se.json",
    香港: "/asset/data-1461584707906-r1hSmtsx.json",
    澳门: "/asset/data-1482909771696-ByVIdJWBx.json",
};

// 各省份的数据
const mapAllData = [
    {name: "北京"},
    {name: "天津"},
    {name: "上海"},
    {name: "重庆"},
    {name: "河北"},
    {name: "河南"},
    {name: "云南"},
    {name: "辽宁"},
    {name: "黑龙江"},
    {name: "湖南"},
    {name: "安徽"},
    {name: "山东"},
    {name: "新疆"},
    {name: "江苏"},
    {name: "浙江"},
    {name: "江西"},
    {name: "湖北"},
    {name: "广西"},
    {name: "甘肃"},
    {name: "山西"},
    {name: "内蒙古"},
    {name: "陕西"},
    {name: "吉林"},
    {name: "福建"},
    {name: "贵州"},
    {name: "广东"},
    {name: "青海"},
    {name: "西藏"},
    {name: "四川"},
    {name: "宁夏"},
    {name: "海南"},
    {name: "台湾"},
    {name: "香港"},
    {name: "澳门"},
];

// 计算饼图百分比的函数
const calculatePiePercent = (echartData, params) => {
    try {
        let total = 0;
        let percent = 0;
        Object.keys(echartData).forEach((dt) => {
            if (typeof echartData[dt].value === 'number') {
                total += echartData[dt].value;
            }
        });
        if (total !== 0 && typeof params.value === 'number') {
            percent = ((params.value / total) * 100).toFixed(1);
        } else {
            percent = '0';
        }
        return percent;
    } catch (error) {
        console.error('Error calculating pie chart label:', error);
        return '0';
    }
};

// 地图配置项生成函数
const mapOptions = (name, mapObj) => {
    const option = {
        tooltip: {
            show: true,
            formatter: (params) => {
                if (params.data) return params.name + "：" + params.data["value"];
            },
        },
        title: {
            text: '设备分布图',
            left: 'center',
            top: '2%',
            textStyle: {
                fontSize: 20,
                color: "#ffffff",
            },
        },
        visualMap: {
            min: 0,
            max: 2,
            left: '1%',
            bottom: '5%',
            orient: 'vertical',
            textStyle: {
                color: "#ffffff",
            },
            inRange: {
                color: ['#ffffff', '#ffcccc', '#ff0000']
            },
            pieces: [
                {
                    gte: 10,
                    label: '> 10',
                    color: '#ff0000'
                },
                {
                    gte: 6,
                    lte: 9,
                    label: '6 - 9',
                    color: '#f57373'
                },
                {
                    gte: 1,
                    lte: 5,
                    label: '1 - 5',
                    color: '#ffcccc'
                },
                {
                    gte: 0,
                    lt: 0,
                    label: '0 ',
                    color: '#ffffff'
                }
            ],
            show: true,
        },
        toolbox: {
            show: true,
            feature: {
                saveAsImage: {
                    pixelRatio: 4,
                },
            },
        },
        series: [
            {
                name: "MAP",
                type: "map",
                mapType: name,
                selectedMode: "false",
                label: {
                    normal: {
                        show: true,
                        formatter: (params) => {
                            const count = mapObj[params.name] || 0;
                            return `${params.name}\n${count}台`;
                        },
                        color: '#000',
                        fontSize: 12,
                        lineHeight: 16
                    },
                    emphasis: {
                        show: true,
                        color: '#000'
                    },
                },
                roam: true, // 启用鼠标缩放和平移
                zoom: 1.2, // 初始缩放比例
                scaleLimit: {
                    min: 0.8, // 最小缩放比例
                    max: 5    // 最大缩放比例
                },
                data: mapAllData.map((item) => {
                    return {
                        ...item,
                        value: mapObj[`${item.name}`] || 0
                    };
                }),
                itemStyle: {
                    areaColor: '#FFFFFF', // 默认白色
                    borderColor: '#4D4D4D',
                },
            },
        ],
    };

    return option;
};
// 饼图配置项生成函数
const pieOptions = (echartData) => {
    const calculatePiePercent = (data, params) => {
        const total = data.reduce((sum, item) => sum + item.value, 0);
        return total > 0 ? ((params.value / total) * 100).toFixed(1) : '0.0';
    };

    const colors = [
        '#36BFFA', '#60A5FA', '#93C5FD', '#BFDBFE',
        '#FBBF24', '#FCD34D', '#FDBA74', '#FB923C',
        '#F87171', '#EF4444', '#DC2626', '#B91C1C'
    ];

    return {
        backgroundColor: 'rgba(0, 0, 0, 0)',
        title: {
            text: '当日各类型故障比例',
            left: 'center',
            top: '3%',
            textStyle: {
                fontSize: 16,
                color: '#FFFFFF',
                fontWeight: 'normal'
            }
        },
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(17, 24, 39, 0.9)',
            textStyle: {color: '#FFFFFF'},
            formatter: (params) => {
                const percent = calculatePiePercent(echartData, params);
                return `${params.name}: ${params.value} (${percent}%)`;
            }
        },
        legend: {
            type: 'scroll',
            orient: 'horizontal',
            bottom: '2%',
            left: 'center',
            width: '95%',
            pageButtonItemGap: 8,
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 12,
            textStyle: {
                fontSize: 11,
                color: '#FFFFFF'
            },
            data: echartData.map(item => item.name)
        },
        series: [
            {
                name: '故障比例',
                type: 'pie',
                radius: ['30%', '50%'], // 缩小饼图尺寸
                center: ['50%', '50%'], // 调整位置
                avoidLabelOverlap: true,
                label: {
                    show: true,
                    position: 'outside',
                    color: '#FFFFFF', // 文字白色
                    fontSize: 12,
                    formatter: (params) => {
                        const percent = calculatePiePercent(echartData, params);
                        return `${params.name}\n${percent}%`;
                    }
                },
                labelLine: {
                    lineStyle: {color: '#4B5563'}
                },
                emphasis: {
                    itemStyle: {shadowBlur: 5, shadowColor: 'rgba(0, 0, 0, 0.3)'}
                },
                data: echartData.map((item, index) => ({
                    ...item,
                    itemStyle: {color: colors[index % colors.length]}
                }))
            }
        ]
    };
};
// 自定义设备平台到期概况饼图配置项生成函数
const CustomByEqusOptions = (echartData) => {
    // 计算百分比函数（保持原有逻辑）
    const calculatePiePercent = (data, params) => {
        const total = data.reduce((sum, item) => sum + item.value, 0);
        return total > 0 ? ((params.value / total) * 100).toFixed(1) : '0.0';
    };

    // 自定义颜色方案（保持与饼图一致的色系）
    const colors = [
        '#6610f2', '#28a745', '#ffc107',
        '#17a2b8', '#dc3545', '#343a40'
    ];

    return {
        backgroundColor: 'rgba(0, 0, 0, 0)', // 透明背景
        title: {
            text: '设备平台到期概况',
            left: 'center',
            top: '5%',
            textStyle: {
                fontSize: 18,
                color: '#FFFFFF',
                fontWeight: 'normal',
                fontFamily: 'Arial, sans-serif'
            }
        },
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(17, 24, 39, 0.9)', // 深色背景
            borderColor: '#374151',
            borderWidth: 1,
            padding: 12,
            textStyle: {
                fontSize: 14,
                color: '#FFFFFF'
            },
            formatter: (params) => {
                const percent = calculatePiePercent(echartData, params);
                return `
          <div style="font-weight: bold;">${params.name}</div>
          <div>数量: ${params.value}</div>
          <div>占比: ${percent}%</div>
        `;
            }
        },
        legend: {
            type: 'scroll',
            orient: 'horizontal',
            bottom: '5%',
            left: 'center',
            width: '90%',
            pageButtonItemGap: 10,
            pageButtonColor: '#9CA3AF',
            pageButtonHoverColor: '#FFFFFF',
            pageTextStyle: {
                color: '#9CA3AF'
            },
            itemWidth: 12,
            itemHeight: 12,
            itemGap: 15,
            textStyle: {
                fontSize: 12,
                color: '#E5E7EB',
                padding: [0, 5]
            },
            data: echartData.map(item => item.name)
        },
        series: [
            {
                name: '设备平台到期概况',
                type: 'pie',
                radius: ['30%', '50%'], // 缩小饼图尺寸
                center: ['50%', '50%'], // 调整位置
                roseType: false, // 取消玫瑰图
                avoidLabelOverlap: true,
                label: {
                    show: true,
                    position: 'outside',
                    formatter: (params) => {
                        const percent = calculatePiePercent(echartData, params);
                        return `${params.name}\n${percent}%`;
                    },
                    backgroundColor: 'rgba(0, 0, 0, 0)',
                    borderWidth: 0,
                    padding: [0, 0],
                    color: '#FFFFFF', // 确保文字为白色
                    rich: {
                        name: {
                            fontSize: 12,
                            color: '#E5E7EB',
                            lineHeight: 18
                        },
                        value: {
                            fontSize: 14,
                            color: '#FFFFFF',
                            fontWeight: 'bold'
                        }
                    }
                },
                labelLine: {
                    show: true,
                    length: 10,
                    length2: 15,
                    lineStyle: {
                        color: '#4B5563'
                    }
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.3)',
                        shadowOffsetX: 0,
                        shadowOffsetY: 0,
                        opacity: 0.8
                    },
                    label: {
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                },
                data: echartData.map((item, index) => ({
                    ...item,
                    itemStyle: {
                        color: colors[index % colors.length]
                    }
                }))
            }
        ]
    };
};
// 柱状图配置项生成函数
const barOptions = (yAxis, seriesData) => {
    const option = {
        title: {
            text: '客户设备统计(单位:个)',
            left: 'center',
            top: '2%',
            textStyle: {
                fontSize: 16,
                color: "#ffffff",
            },
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "shadow",
            },
        },
        grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
        },
        dataZoom: [
            {
                type: 'inside',
                show: true,
                yAxisIndex: [0],
                start: 0,
                end: 100
            },
            {
                type: 'slider',
                show: true,
                yAxisIndex: [0],
                left: '0.5%',
                start: 0,
                end: 100,
                width: 15
            }
        ],
        xAxis: {
            type: "value",
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#ffffff",
                },
            },
        },
        yAxis: {
            type: "category",
            data: yAxis,
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#ffffff",
                },
            },
            axisLabel: {
                formatter: (value) => {
                    if (value.length > 4) {
                        return value.substring(0, 4) + '...';
                    }
                    return value;
                }
            }
        },
        series: [
            {
                type: "bar",
                label: {
                    normal: {
                        show: false,
                        position: "insideRight",
                    },
                },
                data: seriesData,
            },
        ],
        brush: {
            toolbox: ['zoom'],
            xAxisIndex: "all",
            yAxisIndex: "all",
            brushStyle: {
                borderColor: "#fff",
                borderWidth: 1,
                color: "rgba(255, 255, 255, 0.1)",
            },
        },
    };
    return option;
};

// 最后设备分布统计柱状图配置项生成函数
const lastDevOptions = (xAxis, seriesData) => {
    const option = {
        xAxis: {
            type: 'category',
            data: xAxis,
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#ffffff",
                },
            },
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "shadow",
            },
        },
        title: {
            text: '设备分布统计(单位:个)',
            left: 'center',
            top: '2%',
            textStyle: {
                fontSize: 16,
                color: "#ffffff",
            },
        },
        dataZoom: [
            {
                type: 'slider',
                show: true,
                xAxisIndex: [0],
                bottom: 10,
                start: 0,
                end: 100,
                height: 13
            }
        ],
        grid: {
            top: '15%',
            left: "3%",
            right: "4%",
            bottom: "10%",
            containLabel: true,
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#ffffff",
                },
            },
        },
        series: [
            {
                data: seriesData,
                type: 'bar'
            }
        ]
    };
    return option;
};

export {
    provinces,
    mapAllData,
    mapOptions,
    pieOptions,
    barOptions,
    CustomByEqusOptions,
    lastDevOptions
};
