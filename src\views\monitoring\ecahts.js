import * as echarts from 'echarts';
const labelStyle = {
    color: "#333333",
    fontSize: '14npx',
}
const initDensityEchats = (data) => {
    // const colorList = ["#0EB96C", "#FFA71E"];
    return {
        backgroundColor: "#fff",
        legend: {
            icon: "circle",
            top: "0%",
            right: "5%",
            itemWidth: 6,
            itemGap: 20,
            textStyle: labelStyle,
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                label: {
                    show: true,
                    backgroundColor: "#fff",
                    color: "#556677",
                    borderColor: "rgba(0,0,0,0)",
                    shadowColor: "rgba(0,0,0,0)",
                    shadowOffsetY: 0,
                },
                lineStyle: {
                    width: 0,
                },
            },
            backgroundColor: "#fff",
            textStyle: {
                color: "#5c6c7c",
            },
            padding: [10, 10],
            extraCssText: "box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)",
        },
        grid: {
            top: "18%",
            left: "7.5%",
            right: "0%",
            bottom: "10%",
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: true,
                axisLine: {
                    //坐标轴轴线相关设置。数学上的x轴
                    show: true,
                    lineStyle: {
                        color: "#E5E5E5",
                    },
                },
                axisLabel: {
                    //坐标轴刻度标签的相关设置
                    textStyle: {
                        ...labelStyle,
                        margin: 20
                    },
                },
                axisTick: {
                    show: true,
                },
                data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10'],
            },
        ],
        yAxis: [
            {
                type: "value",
                nameTextStyle: {
                    color: "#fff",
                },
                name: "数量/只",
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: "dashed",
                        color: "#E5E5E5",
                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#E5E5E5",
                    },
                },
                axisLabel: {
                    margin: 10,
                    textStyle: {
                        ...labelStyle,
                    },
                },
                axisTick: {
                    show: false,
                },
            },
        ],
        series: data,
    };
}

export {
    initDensityEchats
}