<template>
  <div class="orderAll-container">
    <basic-container>
      <avue-crud
          style="width: 100%;height:100%"
          v-model:search="search"
          :data="tableData"
          :option="option"
          v-model:page="page"
          @current-change="currentChange"
          @search-change="searchChange"
          >
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {getOrderPages} from "@/api/pay/order";

export default {
  name: "orderAll",
  data() {
    return {
      tableData: [],
      orderNoDic:[],
      query:{},
      search:{},
      //日志功能
      option: {
        menu: false,
        calcHeight: 30,
        height:500,
        tip: false,
        searchShow: true,
        searchMenuSpan: 2,
        emptyBtn: false,
        addBtn: false,
        indexLabel: "序号",
        indexWidth: 60,
        index:true,
        columnBtn: true,
        align: "center",
        column: [
          {
            label: '订单编号',
            prop: 'orderNo',
            search: true,
            searchSpan: 4,
            change:(res => {
              this.query.orderNo = res.value;
            }),
            width:200,
          }, {
            label: '订单状态',
            prop: 'orderStatus',
            type:"select",
            search: true,
            searchSpan: 4,
            width:120,
            change:(res => {
              this.query.orderStatus = res.value;
              this.getOrderAll(this.page,this.query);
            }),
            dicData:[
              {
                label:"预下单",
                value:'0'
              },
              {
                label:"续费中",
                value:'1'
              },
              {
                label:"续费完成",
                value:'2'
              },
              {
                label:"取消续费",
                value:'3'
              },
            ]
          }, {
            label: '支付方式',
            prop: 'paymentMethod',
            formatter: function (row, value, column) {
              return "微信"
            },
            width:120,
          }, {
            label: '订单创建时间',
            prop: 'createTime',
            width:200,
          }, {
            label: '续费设备数量',
            prop: 'deviceNum',
            formatter: function (row, value, column) {
              return value + "台";
            },
            width:150,
          }, {
            label: '支付金额',
            prop: 'paymentAmount',
            formatter: function (row, value, column) {
              return value + "元";
            },
            width:120,
          }, {
            label: '设备编号',
            prop: 'equNo',
            width:200,
          }, {
            label: '续费年限',
            prop: 'renewTime',
            formatter: function (row, value, column) {
              return value + "年";
            },
          }, {
            label: '支付状态',
            prop: 'payStatus',
            search: true,
            searchSpan: 4,
            type:"select",
            change:(res => {
              this.query.payStatus = res.value;
              this.getOrderAll(this.page,this.query);
            }),
            dicData:[
              {
                label:"未支付",
                value:'1'
              },
              {
                label:"已支付",
                value:'2'
              },
              {
                label:"已取消",
                value:'3'
              }
            ]
          }
        ]
      },
      page: {
        total: 0,
        pageNum: 2,
        pageSize: 10
      },
      form: {},
      isDestroyed: false
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  mounted() {
    this.isDestroyed = false;
    this.init();
  },
  beforeUnmount() {
    this.isDestroyed = true;
    this.tableData = [];
    this.orderNoDic = [];
  },
  methods: {
    init() {
      this.query.userId = this.userInfo.user_id;
      this.getOrderAll(this.page, this.query);
      this.getOrderMax();
    },
    //加载函数
    getOrderAll(page,params) {
      if(params === undefined){
        params = {};
      }
      getOrderPages({pageNum: page.currentPage, pageSize: page.pageSize, ...params}).then(res => {
        if (this.isDestroyed) return;
        this.page.total = res.data.data.total;
        this.tableData = res.data.data.data
        this.tableData.forEach(element => {
          if(element.orderStatus === 0){
            element.orderStatus = "预下单"
          }else if (element.orderStatus === 1) {
            element.orderStatus = "续费中"
          }else if (element.orderStatus === 2) {
            element.orderStatus = "续费完成"
          }else{
            element.orderStatus = "取消续费"
          }
        })
        this.tableData.forEach(element => {
          if(element.payStatus === 1){
            element.payStatus = "未支付"
          }else if (element.payStatus === 2) {
            element.payStatus = "已支付"
          }else{
            element.payStatus = "已取消"
          }
        })
      })
    },
    getOrderMax(){
      getOrderPages({pageNum: 1, pageSize: 1000000 }).then((res) => {
        if (this.isDestroyed) return;
        res.data.data.data.forEach(item => {
          this.orderNoDic.push({label: item.orderNo,value: item.orderNo})
        })
      })
    },
    changeOrderNo(res){
      this.query.orderNo = res.value;
      this.getOrderAll(this.page,this.query);
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.getOrderAll(this.page,this.query);
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.getOrderAll(this.page,this.query);
    },
    searchChange(val,done){
      this.getOrderAll(this.page,this.query);
      done();
    }
  }
}
</script>

<style lang="scss" scoped>
.orderAll-container {
  width: 100%;
  color: #ffffff;
  background-color: #000;
  height: 82vh;
  border: 1px solid white;
  overflow-y: auto;
  overflow-x: hidden;
  &-right {
    width: 100%;
    height: 100%;
  }
}
</style>


