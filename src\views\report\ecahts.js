import * as echarts from 'echarts';
const labelStyle = {
    color: "#333333",
    fontSize: '14px',
}
const initEchartsTotal = (data, xAxis) => {

    return {
        title: {
            left: 13,
            top: 0,
            text: '单位/只',
            textStyle: labelStyle,
        },
        grid: {
            top: "15%",
            left: "5%",
            right: "5%",
            bottom: "5%",
            containLabel: true,
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                label: {
                    show: true,
                    backgroundColor: "#333",
                    color: "#556677",
                    borderColor: "rgba(0,0,0,0)",
                    shadowColor: "rgba(0,0,0,0)",
                    shadowOffsetY: 0,
                },
                lineStyle: {
                    width: 0,
                },
            }
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: true,
                axisLine: {
                    //坐标轴轴线相关设置。数学上的x轴
                    show: true,
                    lineStyle: {
                        color: "#1b1c21",
                    },
                },
                axisLabel: {
                    //坐标轴刻度标签的相关设置
                    textStyle: {
                        ...labelStyle,
                        margin: 20
                    },
                },
                axisTick: {
                    show: false,
                },
                data: xAxis,
            },
            {
                type: "category",
                axisLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                axisLabel: {
                    show: false,
                },
                splitArea: {
                    show: false,
                },
                splitLine: {
                    show: false,
                },
                data: [],
            },
        ],
        yAxis: [
            {
                type: "value",
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: "dashed",
                        color: "#2B2E35",
                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#C0CEE7",
                    },
                },
                axisLabel: {
                    margin: 10,
                    textStyle: {
                        ...labelStyle,
                    },
                },
                axisTick: {
                    show: false,
                },
            },
        ],
        series: [
            {
                name: "灭蚊数据总量变化", //这个是Bar图
                type: "bar",
                barWidth: "28%",
                barGap: "220%",
                itemStyle: {
                    normal: {
                        barBorderRadius: [30, 30, 0, 0],
                        color: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 1,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: "#07B667", // 0% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: "#6BD8A9", // 100% 处的颜色
                                },
                            ],
                        },
                    },
                },
                data: data,
            }
        ],
    };
}
const initDensityEchats = (data, xAxis) => {
    const colorList = ["#0EB96C", "#FFA71E"];
    return {
        // legend: {
        //     icon: "circle",
        //     top: "0%",
        //     right: "5%",
        //     itemWidth: 6,
        //     itemGap: 20,
        //     textStyle: labelStyle,
        // },
        title: {
            left: 13,
            top: 0,
            text: '单位/只',
            textStyle: labelStyle,
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                label: {
                    show: true,
                    backgroundColor: "#333",
                    color: "#556677",
                    borderColor: "rgba(0,0,0,0)",
                    shadowColor: "rgba(0,0,0,0)",
                    shadowOffsetY: 0,
                },
                lineStyle: {
                    width: 0,
                },
            }
        },
        grid: {
            top: "15%",
            left: "5%",
            right: "5%",
            bottom: "5%",
            containLabel: true,
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: true,
                axisLine: {
                    //坐标轴轴线相关设置。数学上的x轴
                    show: true,
                    lineStyle: {
                        color: "#1b1c21",
                    },
                },
                axisLabel: {
                    //坐标轴刻度标签的相关设置
                    textStyle: {
                        ...labelStyle,
                        margin: 20
                    },
                },
                axisTick: {
                    show: true,
                },
                data: xAxis,
            },
        ],
        yAxis: [
            {
                type: "value",
                nameTextStyle: {
                    color: "#333",
                    padding: [0, 10, 0, 0],
                    fontSize: '14px'
                },

                splitLine: {
                    show: true,
                    lineStyle: {
                        type: "dashed",
                        color: "#2B2E35",

                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#E5E5E5",
                    },
                },
                axisLabel: {
                    margin: 10,
                    textStyle: {
                        ...labelStyle,
                    },
                },
                axisTick: {
                    show: false,
                },
            },
        ],
        series: [
            // {
            //     name: "类目一",
            //     type: "line",
            //     data: [10, 10, 30, 12, 15, 3, 7, 8, 11, 22, 11, 22],
            //     symbolSize: 1,
            //     symbol: "circle",
            //     smooth: true,
            //     yAxisIndex: 0,
            //     showSymbol: false,
            //     lineStyle: {
            //         width: 3,
            //         color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            //             {
            //                 offset: 0,
            //                 color: "#0EB96C",
            //             },
            //             {
            //                 offset: 1,
            //                 color: "#0EB96C",
            //             },
            //         ]),
            //         shadowColor: "rgba(158,135,255, 0.3)",
            //         shadowBlur: 10,
            //         shadowOffsetY: 20,
            //     },
            //     itemStyle: {
            //         normal: {
            //             color: colorList[0],
            //             borderColor: colorList[0],
            //         },
            //     },
            //     color: {
            //         type: 'linear',
            //         x: 0,
            //         y: 0,
            //         x2: 0,
            //         y2: 1,
            //         colorStops: [{
            //             offset: 0, color: 'rgba(14,185,108, 0.5)' // 0% 处的颜色
            //         }, {
            //             offset: 1, color: 'rgba(14,185,108, 0.1)' // 100% 处的颜色
            //         }],
            //         global: false // 缺省为 false
            //     }
            // },
            {
                name: "灭蚊数",
                type: "line",
                data: data,
                symbolSize: 1,
                symbol: "circle",
                smooth: true,
                showSymbol: false,
                lineStyle: {
                    width: 3,
                },
                itemStyle: {
                    normal: {
                        color: colorList[1],
                        borderColor: colorList[1],
                    },

                },
                areaStyle: {
                    //color: '#94C9EC'
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(255,167,30, 0.5)' // 0% 处的颜色
                        }, {
                            offset: 1, color: 'rgba(255,167,30, 0.1)' // 100% 处的颜色
                        }],
                        global: false // 缺省为 false
                    }
                }
            },
        ],
    };
}

export {
    initEchartsTotal,
    initDensityEchats
}