<template>
  <div class="update-modal-overlay">
    <div class="update-modal">
      <div class="modal-header">
        <div class="version-badge">
          <svg class="version-icon" viewBox="0 0 24 24">
            <path d="M12,2L4,5V11.09C4,16.14 7.41,20.85 12,22C16.59,20.85 20,16.14 20,11.09V5L12,2M11,15H13V17H11V15M11,7H13V13H11V7Z" />
          </svg>
          <h3>发现新版本 {{ newVersion }}</h3>
        </div>
        <div class="update-time">
          <svg class="time-icon" viewBox="0 0 24 24">
            <path d="M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
          </svg>
          {{ updateTime }}
        </div>
      </div>

      <div class="update-tip">
        <svg class="sparkle-icon" viewBox="0 0 24 24">
          <path d="M9,22A1,1 0 0,1 8,21V18H5A1,1 0 0,1 4,17V15A1,1 0 0,1 5,14H19A1,1 0 0,1 20,15V17A1,1 0 0,1 19,18H16V21A1,1 0 0,1 15,22H9M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10M17,10A2,2 0 0,1 19,12A2,2 0 0,1 17,14A2,2 0 0,1 15,12A2,2 0 0,1 17,10Z" />
        </svg>
        更新后体验更佳哦~
      </div>

      <div class="update-content">
        <p class="content-title">
          <svg class="list-icon" viewBox="0 0 24 24">
            <path d="M3 16H10V14H3M3 19H10V17H3M3 13H10V11H3M3 10H10V8H3M14 16H21V14H14M14 19H21V17H14M14 13H21V11H14M14 8V10H21V8H14Z" />
          </svg>
          更新内容：
        </p>
        <ul class="content-list">
          <li v-for="(item, index) in updateContent" :key="index">
            <span class="item-index">{{ index + 1 }}、</span>
            <span class="item-text">{{ item }}</span>
            <svg class="check-icon" viewBox="0 0 24 24">
              <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
            </svg>
          </li>
        </ul>
      </div>

      <div class="modal-footer">
        <button @click="handleUpdate" class="update-button">
          <svg class="download-icon" viewBox="0 0 24 24">
            <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" />
          </svg>
          <span>立即更新</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    newVersion: String,
    updateContent: Array,
    updateTime: String
  },
  emits: ['update'],
  methods: {
    handleUpdate() {
      this.$emit('update');
    }
  }
}
</script>

<style scoped>
.update-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.update-modal {
  background: white;
  padding: 28px;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  animation: modalFadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.version-badge {
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-icon {
  width: 24px;
  height: 24px;
  fill: #1890ff;
}

h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
  font-weight: 600;
}

.update-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #666;
}

.time-icon {
  width: 16px;
  height: 16px;
  fill: #888;
}

.update-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 0 16px;
  font-size: 14px;
  color: #666;
  padding: 8px 12px;
  background: #f8f8f8;
  border-radius: 6px;
}

.sparkle-icon {
  width: 18px;
  height: 18px;
  fill: #ffc107;
}

.content-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px;
  font-weight: 500;
  color: #444;
  font-size: 16px;
}

.list-icon {
  width: 20px;
  height: 20px;
  fill: #1890ff;
}

.update-content {
  margin: 16px 0;
  max-height: 50vh;
  overflow-y: auto;
  padding-right: 8px;
}

.content-list {
  padding-left: 4px;
  margin: 0;
  list-style: none;
}

.content-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  line-height: 1.5;
  color: #555;
  position: relative;
  padding-right: 24px;
}

.item-index {
  font-weight: 500;
  color: #1890ff;
  min-width: 20px;
}

.item-text {
  flex: 1;
}

.check-icon {
  width: 18px;
  height: 18px;
  fill: #52c41a;
  position: absolute;
  right: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.update-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  border: none;
  padding: 10px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.25s;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.download-icon {
  width: 18px;
  height: 18px;
  fill: white;
}

.update-button:hover {
  background: linear-gradient(135deg, #40a9ff, #1890ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.update-button:active {
  transform: translateY(0);
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.update-content::-webkit-scrollbar {
  width: 6px;
}

.update-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.update-content::-webkit-scrollbar-track {
  background: transparent;
}

@media (max-width: 480px) {
  .update-modal {
    padding: 20px;
  }

  h3 {
    font-size: 18px;
  }

  .content-title {
    font-size: 15px;
  }

  .content-list li {
    font-size: 14px;
  }

  .update-button {
    padding: 8px 16px;
    font-size: 14px;
  }
}
</style>
