<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #subproject="{ row }">
        <el-button type="primary" text @click.stop="handleAdd(row)">子项目</el-button>
      </template>
      <template #selectRange="{ row }">
        <el-button type="primary" text @click.stop="handleSelectRange(row)"
          >框选</el-button
        >
      </template>
      <template #menu="{ size, row, index }">
        <el-button
          @click.stop="handleView(row, index)"
          icon="el-icon-view"
          text
          type="primary"
          :size="size"
          >查看</el-button
        >
        <el-button
          @click.stop="showDeviceList(row, index)"
          icon="el-icon-platform"
          text
          type="primary"
          :size="size"
          >设备管理</el-button
        >
      </template>
      <template #selectRange-form="{ disabled, size }">
        <div class="project-selectRange-map">
          <el-input
            style="margin-right: 2px"
            :disabled="true"
            :size="size"
            v-model="form.selectRange"
          ></el-input>
          <el-button icon="el-icon-map-location" @click="handleSelectRange"
            >地图
          </el-button>
        </div>
      </template>
    </avue-crud>

    <!-- tag弹窗 -->
    <el-dialog
      class="Tag-viewForm"
      :fullscreen="false"
      :show-close="true"
      align-center
      append-to-body
      v-model="tagModel"
      width="400px"
      title="设置标签"
    >
      <el-form label-width="60px">
        <el-form-item
          v-for="(item, index) in addTagValue"
          :key="item"
          :rules="{
            required: true,
            message: '必填',
            trigger: 'blur',
          }"
        >
          <el-select ref="addTagRef" v-model="item.label" clearable>
            <el-option
              v-for="item in tagOptions"
              :key="item.id"
              :label="item.markName"
              :value="item.markName"
            />
          </el-select>
          <!-- <el-button class="ml-5" @click.prevent="removeTag(item)">删除</el-button> -->
        </el-form-item>
        <!-- <el-form-item>
                    <span><el-link @click="addTag"><el-icon><CirclePlusFilled /></el-icon>新增标签</el-link></span>
                </el-form-item> -->
        <el-form-item>
          <span class="ml-10"
            ><el-button size="small" type="primary" @click="confirmAddTag"
              >确定</el-button
            ></span
          >
          <span class="ml-5"
            ><el-button size="small" type="info" @click="tagModel = false"
              >取消</el-button
            ></span
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 查看弹窗 -->
    <el-dialog
      class="Project-viewForm"
      :fullscreen="false"
      :show-close="true"
      align-center
      append-to-body
      v-model="viewModel"
      width="1000px"
    >
      <el-form :model="form" label-width="120px">
        <div class="rowTitle">项目信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目编码">
              {{ viewData.projectCode }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称">
              {{ viewData.projectName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态">
              {{ viewData.projectStatusN }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="租户名称">
              {{ viewData.tenantName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目区域">
              {{ viewData.region }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="框选位置">
              <el-button type="primary" text @click.stop="handleSelectRange(viewData)"
                >框选</el-button
              >
              <!-- {{ viewData.selectRange }}
              <el-link type="primary"
                ><el-icon><LocationFilled /></el-icon
              ></el-link> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="勘察">
              <el-link type="primary">查看</el-link>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否高危">
              {{ viewData.highRisk }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider />
        <div class="rowTitle">
          标签信息<Span class="ml-10"
            ><el-link type="primary" @click="tagModel = true"
              ><el-icon><Edit /></el-icon><span class="ml-5">编辑</span></el-link
            ></Span
          >
        </div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="设置标签">
              <div class="flex gap-2">
                <el-tag
                  v-for="item in viewData.labelLists"
                  :key="item.id"
                  type="info"
                  class="ml-5"
                  effect="light"
                  round
                  closable
                  @close="removeTag(item)"
                >
                  {{ item.name }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <!-- 地图弹窗 -->
    <el-dialog
      class="Project-MapModel"
      :fullscreen="true"
      title="点击框选后在地图上进行打点框选"
      :show-close="true"
      align-center
      append-to-body
      v-model="mapModel"
      width="100%"
      :destroy-on-close="true"
    >
      <MapModel :info="selectRangeInfo" @rowUpdate="rowMapUpdate" />
    </el-dialog>
  </basic-container>
</template>

<script>
import { reactive, ref } from "vue";
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  getTreeData,
  saveMark,
  removeMark,
} from "@/api/project/list";
import { getList as getTagList } from "@/api/identifying/list";
import { getDetail as getTenantDetail } from "@/api/system/tenant";
import { getLazyTree } from "@/api/base/region";
import { mapGetters } from "vuex";
import MapModel from "./mapModel.vue";
export default {
  components: {
    MapModel,
  },
  data() {
    return {
      tagModel: false,
      addTagValue: [
        {
          id: "",
          name: "",
          type: "info",
        },
      ],
      addTagVisible: false,
      selectRangeInfo: {},
      form: {
        parentId: 0,
      },
      query: {
        parentId: 0,
      },
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      viewData: {},
      searchForm: {},
      option: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        span: 24,
        dialogWidth: "50%",
        searchMenuSpan: 8,
        border: true,
        index: true,
        addBtn: true,
        viewBtn: false,
        selection: false,
        dialogClickModal: false,
        menuWidth: "350px",
        column: [
          {
            label: "父项目名称",
            prop: "parentId",
            dicUrl: "/base/projectinfo/page",
            type: "select",
            hide: true,
            rules: [
              {
                required: false,
                message: "父项目id",
                trigger: "blur",
              },
            ],
            dicFormatter: (res) => {
              return res.data.records;
            },
            props: {
              label: "projectName",
              value: "id",
            },
          },
          {
            label: "项目编码",
            prop: "projectCode",
            disabled: true,
            width: "200px",
          },
          {
            label: "项目名称",
            prop: "projectName",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入项目名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "租户名称",
            prop: "tenantId",
            dicUrl: "/blade-system/tenant/list",
            type: "select",
            search: true,
            dicQuery: {
              current: 1,
              size: 99999,
            },
            dicFormatter: (res) => {
              return res.data.records;
            },
            props: {
              label: "tenantName",
              value: "tenantId",
            },
            rules: [
              {
                required: true,
                message: "请选择租户",
                trigger: "blur",
              },
            ],
          },
          {
            label: "项目状态",
            prop: "projectStatus",
            type: "select",
            dicUrl: "/blade-system/dict-biz/getDictBizData?code=projectInfoStatus",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请输入项目状态",
                trigger: "blur",
              },
            ],
          },
          {
            label: "勘察项目",
            prop: "surveyId",
            dicUrl: "/base/surveyinfo/list",
            type: "select",
            dicQuery: {
              current: 1,
              size: 99999,
            },
            dicFormatter: (res) => {
              return res.data.records;
            },
            props: {
              label: "surveyName",
              value: "id",
            },
            rules: [
              {
                required: false,
                message: "请输入勘察项目",
                trigger: "blur",
              },
            ],
          },
          {
            label: "框选范围",
            prop: "selectRange",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "子项目",
            prop: "subproject",
            viewDisplay: false,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: "区域",
            prop: "region",
            type: "cascader",
            dicUrl: "/blade-system/region/lazy-tree",
            hide: true,
            dicQuery: {
              topCode: "00",
            },
            props: {
              label: "title",
              value: "title",
            },
            rules: [
              {
                required: false,
                message: "请输入区域",
                trigger: "blur",
              },
            ],
            dicFormatter: (res) => {
              return res.data.filter((item) => +item.id !== 0);
            },
          },
          {
            label: "描述",
            prop: "projectDesc",
            span: 24,
            minRows: 3,
            hide: true,
            type: "textarea",
            rules: [
              {
                required: false,
                message: "请输入描述",
                trigger: "blur",
              },
            ],
          },
          // {
          //     label: "标识id",
          //     prop: "markId",
          //     rules: [{
          //         required: true,
          //         message: "请输入标识id",
          //         trigger: "blur"
          //     }]
          // },
          // {
          //     label: "修改时间",
          //     prop: "updateTime",
          //     addDisplay: false,
          //     editDisplay: false,
          //     rules: [{
          //         required: true,
          //         message: "请输入修改时间",
          //         trigger: "blur"
          //     }]
          // }
        ],
      },
      data: [],
      mapModel: false,
      viewModel: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.dept_add, false),
        viewBtn: this.validData(this.permission.dept_view, false),
        delBtn: this.validData(this.permission.dept_delete, false),
        editBtn: this.validData(this.permission.dept_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      row.region = row.region[0] + "," + row.region[1] + "," + row.region[2];
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowMapUpdate(row) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.mapModel = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    confirmAddTag() {
      const arr = {
        markName: this.addTagValue[0].label,
        projectId: this.viewData.id,
      };
      saveMark(arr).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.handleView(this.viewData);
        this.tagModel = false;
      });
    },
    removeTag(e) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeMark(e.id);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.viewData.labelLists.splice(this.viewData.labelLists.indexOf(e), 1);
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleAdd(row) {
      this.form.parentId = row.id || 0;
      this.$refs.crud.rowAdd();
    },
    handleView(row, index) {
      this.loadTagList(5);
      if (row !== null) {
        getDetail(row.id).then((res) => {
          this.viewData = res.data.data;
          this.viewData.tenantName = row.tenantName;
          this.viewData.projectStatusN = row.$projectStatus;
        });
        this.viewModel = true;
      }
    },
    showDeviceList(row, index) {
    },
    async beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        await getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      const column = this.findObject(this.option.column, "parentId");
      this.form.id || this.form.parentId
        ? (column.display = true)
        : (column.display = false);
      done();
    },
    beforeClose(done, type) {
      this.parentId = 0;
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getTreeData(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleSelectRange(row) {
      this.selectRangeInfo = row;
      this.$nextTick(() => {
        this.mapModel = true;
      });
    },
    loadTagList(type) {
      const query = {
        markType: type,
      };
      getTagList(1, 100, query).then((res) => {
        const data = res.data.data;
        this.tagOptions = data.records;
      });
    },
    async getDict(code) {
      let result = [];
      const query = {
        code: code,
      };
      result = await getDictionary(query).then((res) => {
        const data = res.data.data;
        return data;
      });
      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
.ml-5 {
  margin-left: 5px;
}
.ml-10 {
  margin-left: 10px;
}

.Project-MapModel {
  :global(.el-dialog__header) {
  }
  :global(.el-dialog__body) {
    height: 100%;
    padding: 0px;
  }
}

.project-selectRange-map {
  display: flex;
}

.Project-viewForm {
  .rowTitle {
    font-size: 18px;
    font-weight: bold;
    padding-left: 35px;
    padding-bottom: 20px;
  }
  .tabsContent {
    padding: 20px;
  }
  .el-divider--horizontal {
    margin-top: 0;
  }
  .el-divider--horizontal {
    margin-top: 0;
  }
}
</style>
