<template>
  <div>
    <div v-if="paymentTab === 1">
      <div class="title">运营商:江苏无锡孙财</div>
      <avue-form :option="option" @submit="handleSubmit" v-model="form"></avue-form>
    </div>
    <div class="payment-submit" v-else>
      <div>微信充值:30 元</div>
      <div>已支付,点击 <a @click="handlePay">完成支付</a></div>
      <img src="" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      paymentTab: 1,
      form: {
        input: 1,
      },
      option: {
        emptyBtn: false,
        column: [
          {
            label: "微信充值",
            prop: "input",
            type: "radio",
            span: 24,
            dicData: [
              {
                label: "30",
                value: 1,
              },
              {
                label: "100",
                value: 2,
              },
              {
                label: "1000",
                value: 3,
              },
              {
                label: "5000",
                value: 4,
              },
              {
                label: "其它",
                value: 5,
              },
            ],
            change: (res) => {
              if (res.value === 5) {
                this.option.column.push({
                  label: "金额",
                  prop: "realName",
                  rules: [
                    {
                      required: true,
                      message: "请输入金额",
                      trigger: "blur",
                    },
                  ],
                });
              } else {
                this.option.column = this.option.column.filter(
                  (item) => item.prop !== "realName"
                );
              }
            },
          },
        ],
      },
    };
  },

  computed: {},
  methods: {
    handleSubmit() {
      this.paymentTab = 2;
    },
    handlePay() {
      console.log(this);
      alert("支付成功 谢谢您呢！");
      this.$parent.paymentModel = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 20px;
}
.payment-submit {
  display: flex;
  flex-direction: column;
  align-items: center;
  div {
    margin-bottom: 10px;
  }
  div:nth-child(1) {
    font-size: 18px;
  }
  div:nth-child(2) {
    font-size: 16px;
  }
  a {
    color: red;
    cursor: pointer;
  }
  img {
    width: 300px;
    height: 300px;
    background-color: aqua;
  }
}
</style>
