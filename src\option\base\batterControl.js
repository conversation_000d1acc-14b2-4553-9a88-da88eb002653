export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "下发数据类型：C8 功能命令，B9 电池数据设置",
      prop: "dataType",
      type: "input",
    },
    {
      label: "排序",
      prop: "sort",
      type: "input",
    },
    {
      label: "设备项名称",
      prop: "filedName",
      type: "input",
    },
    {
      label: "设备项值",
      prop: "filedVal",
      type: "input",
    },
    {
      label: "单位",
      prop: "unit",
      type: "input",
    },
    {
      label: "上限",
      prop: "upLimit",
      type: "input",
    },
    {
      label: "下限",
      prop: "downLimit",
      type: "input",
    },
    {
      label: "组数据（根据这个数组可以找到高/低数据进行相减）",
      prop: "group",
      type: "input",
    },
    {
      label: "高低类型：1 高，2 低",
      prop: "hightLowType",
      type: "input",
    },
    {
      label: "高低位间隔",
      prop: "valLimit",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
