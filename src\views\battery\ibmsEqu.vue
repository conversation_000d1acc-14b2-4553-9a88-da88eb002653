<template>
  <div class="ibms-container">
    <div class="ibms-container-content">
      <div class="battery-control-grid">
        <!-- BMS配置 -->
        <div class="section-container bms-config" v-if="canAccessOther || canAccessSpecial">
          <!-- 控制按钮 -->
          <div class="control-buttons" style="width: 100%; justify-content: space-between;">
            <div><span class="icon-batter"></span>&nbsp;电池参数</div>
            <div class="button-group">
              <el-button type="primary" @click="readyInfo" v-if="hasNoneOfPermissions">读取</el-button>
              <el-button type="primary" @click="downCom" v-if="hasNoneOfPermissions"
                         v-loading.fullscreen.lock="loadingDownCom">下发
              </el-button>
            </div>
          </div>

          <!-- 核心参数部分 -->
          <div class="param-section" v-if="canAccessOther">
            <div class="section-title">核心参数：</div>
            <div class="config-grid">
              <div class="config-column" v-for="(column, index) in coreParamsColumns" :key="index">
                <div v-for="item in column" :key="item.id">
                  <div class="config-item" v-if="item.visible && !isSpecialParameter(item)">

                    <div class="config-label">
                      <span>{{ item.filedName }}: ({{ item.unit }})</span>
                    </div>
                    <div class="config-input">
                      <el-select
                          v-if="item.interFlag == 1"
                          v-model="form[item.filed]"
                          @change="changeHandle(item)"
                          filterable
                          placeholder="请选择">
                        <el-option
                            v-for="opt in item.dicData"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value"
                        ></el-option>
                      </el-select>
                      <el-input
                          v-else
                          class="no_number"
                          v-model="form[item.filed]"
                          :controls="false"
                      ></el-input>
                    </div>
                    <div class="config-range">范围： ({{ item.upLimit }} ~ {{ item.downLimit }})</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 常用参数部分 -->
          <div class="param-section" v-if="canAccessSpecial">
            <div class="section-title">常用参数：</div>
            <div class="config-grid">
              <div class="config-column" v-for="(column, index) in commonParamsColumns" :key="index">
                <div v-for="item in column" :key="item.id">
                  <div class="config-item" v-if="item.visible">
                    <div class="config-label">
                      <span v-if="item.filedName === '输出开关'">{{ item.filedName }}: {{ item.unit }}</span>
                      <span v-else>{{ item.filedName }}: ({{ item.unit }})</span>
                    </div>
                    <div class="config-input">
                      <el-select
                          v-if="item.interFlag == 1"
                          v-model="form[item.filed]"
                          @change="changeHandle(item)"
                          filterable
                          placeholder="请选择"
                      >
                        <el-option
                            v-for="opt in item.dicData"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value"
                        ></el-option>
                      </el-select>
                      <el-input
                          v-else
                          class="no_number"
                          v-model="form[item.filed]"
                          :controls="false"
                      ></el-input>
                    </div>
                    <div class="config-range" v-if="item.filedName === '输出开关'">范围： (开 ~ 关)</div>
                    <div class="config-range" v-else>范围： ({{ item.upLimit }} ~ {{ item.downLimit }})</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- BMS控制 -->
        <div class="section-container bms-control" v-if="canAccessnFrom25To33">
          <!-- 使用Flexbox布局的标题行 -->
          <div class="control-header"
               style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <div class="control-title" style="display: flex; align-items: center;">
              <span class="icon-control"></span>
              <span>操作指令</span>
            </div>
            <el-button
                type="primary"
                @click="batchDownHandle"
                v-if="IssueBtn"
                class="batch-down-btn">
              下发
            </el-button>
          </div>

          <div style="border-bottom: 1px solid rgba(46, 199, 201, 0.3);"></div>

          <div class="control-grid">
            <div class="control-column" v-for="(column, index) in columnC8" :key="index">
              <div v-for="item in column" :key="item.id">
                <div class="control-item" v-if="item.visible">
                  <div class="control-label">
                    <span class="label-decoration">{{ item.filedName }}</span>
                  </div>
                  <el-select
                      v-if="item.interFlag === 1"
                      v-model="form[item.filed]"
                      @change="instructionsChangeHandle(item,form[item.filed])"
                      @click="resetSelectValue(item.filed)"
                      filterable
                      style="width:32%"
                      placeholder="请选择">
                    <el-option
                        v-for="opt in item.dicData"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                    ></el-option>
                  </el-select>
                  <el-input
                      style="width:50%"
                      v-if="item.interFlag === 2"
                      class="no_number"
                      v-model="form[item.filed]"
                      @input="instructionsChangeHandle(item,form[item.filed])"
                      :controls="false"
                      :placeholder="'范围：'+Math.floor(item.upLimit)+' ~ '+Math.floor(item.downLimit)">
                  </el-input>
                  <span style="font-size: 13px"> &nbsp;&nbsp;{{ item.unit }}</span>
                  <el-button v-if="item.interFlag !==1 && item.interFlag !==2" type="primary"
                             @click="downC8Handle(item)"
                             v-loading.fullscreen.lock="loadingDownCom"
                             class="control-button">下发
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作记录 -->
        <div class="section-container operation-log" v-if="perms.logPanel">
          <div class="log-header">
            <span class="icon-logs"></span> 操作记录
          </div>
          <avue-crud
              class="avue-crud-width"
              :option="option"
              v-model:search="search"
              :table-loading="loading"
              :data="data"
              :page.sync="page"
              :before-open="beforeOpen"
              v-model="form"
              ref="crud"
              @search-change="searchChange"
              @search-reset="searchReset"
              @selection-change="selectionChange"
              @current-change="currentChange"
              @size-change="sizeChange"
              @refresh-change="refreshChange"
              @on-load="onLoad">
            <template #sendMessage="scope">
              <el-tooltip
                  effect="dark"
                  :content="scope.row.sendMessage"
                  placement="top"
              >
                <div class="message-tooltip">{{ scope.row.sendMessage }}</div>
              </el-tooltip>
            </template>
          </avue-crud>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {MQTT_SERVICE, MQTT_USERNAME, MQTT_PASSWORD, MQTT_CLIENTID} from "./mqtt";
import * as mqtt from 'mqtt/dist/mqtt'
import {getListByType, sendComC8} from "@/api/base/batterControl";
import {getList, getDetail} from "@/api/base/batterControlLog";
import {ElLoading} from 'element-plus'
import {mapGetters} from "vuex";
import {
  hasAnyOfSpecifiedPermissions,
  hasBatteryOrSwitchPermission,
  hasOtherPermission,
  hasPermissionFrom25To33
} from "@/views/battery/permBtn";

export default {
  data() {
    return {
      IssueBtn: false,
      loadingDownCom: false,
      search: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      count: 0,
      timer: 0,
      loadingInstance: {},
      loadingAnimate: true,
      selectionList: [],
      data: [],
      option: {
        menu: false,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: false,
        index: true,
        searchBtn: true,
        viewBtn: false,
        emptyBtn: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        selection: false,
        dialogClickModal: false,
        labelWidth: 140,
        indexLabel: "序号",
        indexWidth: 50,
        height: 330,
        expandFixed: false,
        align: "left",
        refreshBtn: false,
        columnBtn: false,
        size: "small",
        searchSize: "small",
        column: [
          {
            label: "指令类型",
            prop: "sendType",
            minWidth: 60,
          },
          {
            label: "指令内容",
            overHidden:true,
            prop: "sendMessage",
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            minWidth: 50,
            dicData: [
              {
                label: "已发送",
                value: "1"
              }, {
                label: "下发成功",
                value: "2"
              }, {
                label: "小程序下发",
                value: "3"
              },
            ],
          },
          {
            label: "设备编号",
            prop: "equNo",
            minWidth: 100,
          },
          {
            label: "发送时间",
            prop: "createTime",
            minWidth: 120,
          },
          {
            label: "操作人",
            prop: "operatorName",
            minWidth: 80,
          },
        ],
      },
      filedData: [],
      client: null,
      timeout: null,
      hasProcessedMessage: false,
      timeoutId: null,
      perms: {},
      hasBatteryOrSwitchPermission: false,
      itemIssue: {},
      originalMessageHandler: null,
      subscribedTopic: null,
      clearInt: null,
    };
  },
  props: {
    devicesn: {
      type: String,
      default: {},
    },
    softVer: {
      type: String,
      default: {},
    },
    status: {
      type: Number,
      default: {},
    },
  },
  computed: {
    ...mapGetters(["permission", 'userInfo']),
    // 是否拥有电池容量或开关权限（18或20）
    canAccessSpecial() {
      return hasBatteryOrSwitchPermission(this.perms);
    },

    // 是否拥有其他任意权限（排除18和20）
    canAccessOther() {
      return hasOtherPermission(this.perms);
    },
    //是否拥有其他任意权限(25到31)s
    canAccessnFrom25To33() {
      return hasPermissionFrom25To33(this.perms)
    },
    // 是否同时拥有特殊权限和其他权限
    canAccessAll() {
      return this.canAccessSpecial && this.canAccessOther;
    },

    //是否具有读取数据权限
    hasNoneOfPermissions() {
      return hasAnyOfSpecifiedPermissions(this.perms);
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    columnC8() {
      const length = this.filedData.length;
      var filedList = this.filedData.filter(item => item.dataType === 'C8' || item.dataType === 'C9' || item.dataType === 'CB' || item.dataType === 'CC')
      const leftColumn = filedList.filter((_, index) => index % 2 === 0);
      const rightColumn = filedList.filter((_, index) => index % 2 !== 0);
      return [leftColumn, rightColumn];
    },
    coreParamsColumns() {
      const coreParams = this.filedData.filter(item =>
          item.dataType == 'B9' &&
          item.filedName !== '输出开关' &&
          item.filedName !== '电池容量'
      );
      const leftColumn = coreParams.filter((_, index) => index % 2 === 0);
      const rightColumn = coreParams.filter((_, index) => index % 2 !== 0);
      return [leftColumn, rightColumn];
    },
    commonParamsColumns() {

      const commonParams = this.filedData.filter(item =>
          item.dataType == 'B9' &&
          (item.filedName === '输出开关' || item.filedName === '电池容量')
      );
      const leftColumn = commonParams.filter((_, index) => index % 2 === 0);
      const rightColumn = commonParams.filter((_, index) => index % 2 !== 0);
      return [leftColumn, rightColumn];
    }
  },
  mounted() {
    this.connectToMqtt();

  },
  created() {
    this.init()
    // 安全检查userInfo.premSign是否存在
    const premSign = this.userInfo?.premSign || '';
    const permissions = premSign.split(',');

    this.perms.Hargingprotectionvoltage = permissions.includes('Hargingprotectionvoltage')
    this.perms.Chargingrecoveryvoltage = permissions.includes('Chargingrecoveryvoltage')
    this.perms.Dischargeprotectionvoltage = permissions.includes('Dischargeprotectionvoltage')
    this.perms.Dischargerecoveryvoltage = permissions.includes('Dischargerecoveryvoltage')
    this.perms.Dischargeshutdownvoltage = permissions.includes('Dischargeshutdownvoltage')
    this.perms.MOStemperatureprotection = permissions.includes('MOStemperatureprotection')
    this.perms.MOStemperaturerecovery = permissions.includes('MOStemperaturerecovery')
    this.perms.Batterytemperatureprotection = permissions.includes('Batterytemperatureprotection')
    this.perms.Batterytemperaturerecovery = permissions.includes('Batterytemperaturerecovery')
    this.perms.Lowtemperatureprotectionforcharging = permissions.includes('Lowtemperatureprotectionforcharging')
    this.perms.Lowtemperaturechargingrecovery = permissions.includes('Lowtemperaturechargingrecovery')
    this.perms.Lowtemperatureprotectionfordischarge = permissions.includes('Lowtemperatureprotectionfordischarge')
    this.perms.Lowtemperaturedischargerecovery = permissions.includes('Lowtemperaturedischargerecovery')
    this.perms.Heatingactivationtemperature = permissions.includes('Heatingactivationtemperature')
    this.perms.Heatingstoptemperature = permissions.includes('Heatingstoptemperature')
    this.perms.Reportingintervaltime = permissions.includes('Reportingintervaltime')


    //常用参数
    this.perms.Batterycapacity = permissions.includes('Batterycapacity')
    this.perms.switch = permissions.includes('switch')

    //指令下发
    this.perms.shutdown = permissions.includes('shutdown')
    this.perms.strongStart = permissions.includes('strongStart')
    this.perms.autoHeatOn = permissions.includes('autoHeatOn')
    this.perms.autoHeatOff = permissions.includes('autoHeatOff')
    this.perms.reportData = permissions.includes('reportData')
    this.perms.reportCCID = permissions.includes('reportCCID')
    this.perms.quickReport = permissions.includes('quickReport')
    this.perms.logPanel = permissions.includes('logPanel')

    this.perms.dontallProtect = permissions.includes('dontallProtect')
    this.perms.donttemperatureProtect = permissions.includes('donttemperatureProtect')
  },
  beforeDestroy() {
    this.cleanup();
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {
    // 统一的清理方法
    cleanup() {
      // 清理MQTT连接
      if (this.client) {
        // 取消订阅
        if (this.subscribedTopic && this.client.connected) {
          this.client.unsubscribe(this.subscribedTopic, (error) => {
            if (error) {
              console.error('取消订阅失败:', error);
            } else {
              console.log('成功取消订阅:', this.subscribedTopic);
            }
          });
        }

        // 移除所有事件监听器
        this.client.removeAllListeners('connect');
        this.client.removeAllListeners('message');
        this.client.removeAllListeners('error');
        this.client.removeAllListeners('close');

        if (this.client.connected) {
          this.client.end();
        }
        this.client = null;
      }

      // 清理定时器
      if (this.timeout) {
        clearTimeout(this.timeout);
        this.timeout = null;
      }

      // 清理超时定时器
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
        this.timeoutId = null;
      }

      // 清理clearInt定时器
      if (this.clearInt) {
        clearInterval(this.clearInt);
        this.clearInt = null;
      }

      // 清理loading实例
      if (this.loadingInstance) {
        this.loadingInstance.close();
        this.loadingInstance = null;
      }

      // 清理原始消息处理器
      this.originalMessageHandler = null;

      // 重置状态
      this.loadingAnimate = true;
      this.timer = 0;
      this.hasProcessedMessage = false;
      this.loadingDownCom = false;
      this.subscribedTopic = null;
    },
    init() {
      getListByType("").then(res => {
        var data = res.data.data;
        if (!Array.isArray(data)) {
          console.error('返回的数据不是数组:', data);
          this.$message.error('数据格式错误，请刷新页面重试');
          return;
        }

        data.forEach(dt => {
          dt.filed = 'filed' + dt.sort
          if (dt.interFlag === 1) {
            try {
              // 安全检查数值
              const upLimit = Number(dt.upLimit) || 0;
              const downLimit = Number(dt.downLimit) || 0;
              const interVal = Number(dt.interVal) || 1;

              if (interVal <= 0) {
                console.warn('间隔值无效:', dt);
                dt.dicData = [];
                return;
              }

              var gapVal = Math.abs(downLimit - upLimit);
              var num = Math.floor(gapVal / interVal);
              var dicData = []

              for (var i = 0; i <= num; i++) {
                var dicVal = (upLimit + (i * interVal)).toFixed(2);
                dicVal = this.convertValueToText(dicVal);
                dicData.push({
                  label: dicVal,
                  value: dicVal
                })
              }
              dt.dicData = dicData;
            } catch (error) {
              console.error('处理数据项失败:', error, dt);
              dt.dicData = [];
            }
          }
        })

        const permissionIdMap = {
          1: 'Hargingprotectionvoltage',
          2: 'Chargingrecoveryvoltage',
          3: 'Dischargeprotectionvoltage',
          4: 'Dischargerecoveryvoltage',
          5: 'Dischargeshutdownvoltage',
          6: 'MOStemperatureprotection',
          7: 'MOStemperaturerecovery',
          8: 'Batterytemperatureprotection',
          9: 'Batterytemperaturerecovery',
          10: 'Lowtemperatureprotectionforcharging',
          11: 'Lowtemperaturechargingrecovery',
          12: 'Lowtemperatureprotectionfordischarge',
          13: 'Lowtemperaturedischargerecovery',
          14: 'Heatingactivationtemperature',
          15: 'Heatingstoptemperature',
          18: "Batterycapacity",
          19: 'Reportingintervaltime',
          20: 'switch',
          25: 'shutdown', //
          26: 'strongStart', //
          27: 'autoHeatOn', //
          28: 'autoHeatOff', //
          29: 'reportData', //
          30: 'reportCCID', //
          31: 'quickReport', //
          32: 'dontallProtect', //
          33: 'donttemperatureProtect', //
        };

        this.filedData = data.map(item => {
          const permissionKey = permissionIdMap[item.id];
          const hasPermission = !permissionKey || this.perms[permissionKey];
          // 返回原始数据，并添加 visible 字段
          return {
            ...item,
            visible: hasPermission
          };
        });
      }).catch(error => {
        console.error('初始化数据失败:', error);
        this.$message.error('初始化数据失败，请刷新页面重试');
      });
    },

    //屏蔽所有保护和屏蔽温度保护下发
    batchDownHandle() {
      if (this.status === 2) {
        this.$message.warning("设备离线，无法读取！");
        return;
      }
      if (this.softVer < 1014) {
        this.$message({
          type: 'warning',
          message: '当前版本低于1014，无法下发！',
        });
        this.loadingDownCom = false
        return;
      }
      this.downC8Handle(this.itemIssue)
    },
    isSpecialParameter(item) {

      // 两个条件同时为 true 时返回 true（隐藏）
      return item.id === 17 || item.id === 16;
    },
    convertValueToText(value) {
      if (value === "101.00") {
        return "开";
      } else if (value === "100.00") {
        return "关";
      }
      return value;
    },
    connectToMqtt() {
      if (!this.canAccessOther && !this.canAccessSpecial && !this.canAccessnFrom25To33) {
        this.$message.warning("请联系管理员配置该页面权限！")
        return;
      }

      // 如果已经有连接，先清理
      if (this.client) {
        this.cleanup();
      }

      // 安全检查DOM元素是否存在
      const loadingTarget = document.querySelector(".battery-border") || document.body;
      this.loadingInstance = ElLoading.service({
        target: loadingTarget,
        text: '数据读取中....',
        background: "#20202078",
      })
      const brokerUrl = MQTT_SERVICE;
      this.client = mqtt.connect(brokerUrl);

      this.client.on('connect', () => {
        console.log('Connected to MQTT broker');
      });

      // 添加错误处理
      this.client.on('error', (error) => {
        console.error('MQTT连接错误:', error);
        // 清理所有资源
        if (this.loadingInstance) {
          this.loadingInstance.close();
          this.loadingInstance = null;
        }
        if (this.timeout) {
          clearTimeout(this.timeout);
          this.timeout = null;
        }
        if (this.client) {
          this.client.removeAllListeners();
          this.client.end();
          this.client = null;
        }
        this.$message.error('MQTT连接失败，请检查网络连接');
      });

      // 添加连接关闭处理
      this.client.on('close', () => {
        console.log('MQTT连接已关闭');
        if (this.loadingInstance) {
          this.loadingInstance.close();
          this.loadingInstance = null;
        }
        if (this.timeout) {
          clearTimeout(this.timeout);
          this.timeout = null;
        }
      });

      var topic = "JD/KDB/" + this.devicesn + "/Ack";
      var qos = 0;
      this.subscribedTopic = topic; // 保存订阅主题引用
      this.client.subscribe(topic, {qos}, (error) => {
        if (error) {
          console.log('订阅失败:', error)
          this.subscribedTopic = null; // 订阅失败时清除引用
          if (this.loadingInstance) {
            this.loadingInstance.close();
            this.loadingInstance = null;
          }
          if (this.timeout) {
            clearTimeout(this.timeout);
            this.timeout = null;
          }
          if (this.client) {
            this.client.removeAllListeners();
            this.client.end();
            this.client = null;
          }
          this.$message.error('MQTT订阅失败');
          return
        }
        console.log(`订阅成功： '${topic}'`)
      })

      let that = this
      // 保存原始的message处理函数引用，用于后续重新绑定
      this.originalMessageHandler = (topic, message, packet) => {
        let jsonStr = message.toString();
        if (!jsonStr.endsWith("}")) {
          jsonStr += '"}';
        }

        try {
          let params = JSON.parse(jsonStr);
          console.log(JSON.stringify(params) + "读取的数据")
          if (params.Cmd === "9B") {
            var resultData = that.hexStringToNumbers(params.Data);
            if (Array.isArray(resultData)) {
              resultData.forEach((result, index) => {
                var idx = index + 1;
                var filed = that.filedData.filter(filed => filed.sort == idx);
                if (filed.length > 0 && filed[0].binaryBit) {
                  try {
                    let calculatedValue = Number(Number(result) / filed[0].binaryBit).toFixed(2);
                    if (calculatedValue === "101.00") {
                      that.form["filed" + idx] = "开";
                    } else if (calculatedValue === "100.00") {
                      that.form["filed" + idx] = "关";
                    } else {
                      that.form["filed" + idx] = calculatedValue;
                    }
                  } catch (error) {
                    console.error('计算值失败:', error, result, filed[0]);
                    that.form["filed" + idx] = 'N/A';
                  }
                } else {
                  that.form["filed" + idx] = 'N/A';
                }
              });
            }
            if (that.loadingInstance) {
              that.loadingInstance.close();
              that.loadingInstance = null;
            }
            that.loadingAnimate = false;
          }
        } catch (error) {
          console.error('解析MQTT消息失败:', error, '原始消息:', jsonStr);
        }
      };

      this.client.on('message', this.originalMessageHandler);

      this.timeout = setTimeout(() => this.readyInfo(), 2000)
    },
    readyInfo() {
      // 安全检查DOM元素是否存在
      const loadingTarget = document.querySelector(".battery-border") || document.body;
      this.loadingInstance = ElLoading.service({
        target: loadingTarget,
        text: '数据读取中....',
        background: "#20202078",
      })

      // 检查设备状态
      if (this.status === 2) {
        this.$message.warning("设备离线，无法读取！");
        if (this.loadingInstance) {
          this.loadingInstance.close();
          this.loadingInstance = null;
        }
        return;
      }

      // 检查软件版本
      let softVer = this.softVer
      if (softVer === null) {
        if (this.loadingInstance) {
          this.loadingInstance.close();
          this.loadingInstance = null;
        }
        return;
      }

      // 检查MQTT连接状态
      if (!this.client || !this.client.connected) {
        this.$message.warning("设备未连接！");
        if (this.loadingInstance) {
          this.loadingInstance.close();
          this.loadingInstance = null;
        }
        return;
      }

      // 重置定时器状态
      this.timer = 0;
      this.loadingAnimate = true;

      this.clearInt = setInterval(() => {
        this.timer++;
        if (this.timer == 8 && this.loadingAnimate == true) {
          if (this.loadingInstance) {
            this.loadingInstance.close();
            this.loadingInstance = null;
          }
          this.$message.warning("读取超时，请重新读取！")
          this.timer = 0;
          if (this.clearInt) {
            clearInterval(this.clearInt);
            this.clearInt = null;
          }
        } else if (this.loadingAnimate == false && this.timer < 7) {
          if (this.clearInt) {
            clearInterval(this.clearInt);
            this.clearInt = null;
          }
          this.loadingAnimate = true;
          this.timer = 0;
          if (this.loadingInstance) {
            this.loadingInstance.close();
            this.loadingInstance = null;
          }
          this.$message.success("读取成功！")
        }
      }, 1000)

      if (softVer >= 1014) {
        var topic = "JD/KDB/" + this.devicesn + "/Send";
        let params = {}
        params.Cmd = "C8";
        params.Data = "AA";
        let jsonStr1 = "{\n\"Cmd\":\"" + params.Cmd + "\",\n\"Data\":\"" + params.Data + "\"\n}";
        console.log(jsonStr1)

        // 再次检查连接状态（双重保险）
        if (this.client && this.client.connected) {
          this.client.publish(topic, jsonStr1);
        } else {
          console.log("未连接");
          this.$message.warning("设备未连接！")
          // 连接失败时也要清理定时器
          if (this.clearInt) {
            clearInterval(this.clearInt);
            this.clearInt = null;
          }
          if (this.loadingInstance) {
            this.loadingInstance.close();
            this.loadingInstance = null;
          }
        }
      } else {
        this.$message({
          type: 'warning',
          message: '当前版本低于1014，无法读取!',
        });
        if (this.loadingInstance) {
          this.loadingInstance.close();
          this.loadingInstance = null;
        }
        if (this.clearInt) {
          clearInterval(this.clearInt);
          this.clearInt = null;
        }
      }
    },
    downCom() {
      this.hasProcessedMessage = false;
      let flag = true;
      this.loadingDownCom = true
      let softVer = this.softVer
      if (softVer < 1014) {
        this.$message({
          type: 'warning',
          message: '当前版本低于1014，无法下发！',
        });
        this.loadingDownCom = false
        return;
      }
      if (this.status === 2) {
        this.$message.warning("设备离线，无法下发！")
        this.loadingDownCom = false
        return;
      }
      var topic = "JD/KDB/" + this.devicesn + "/Send";
      var resParam = {}
      resParam.Cmd = "B9";
      var msgData = "";
      var that = this;
      var fldData = that.filedData.filter(fld => fld.dataType == "B9");
      fldData.forEach(fld => {
        var fd = that.form["filed" + fld.sort];

        if (fd === "开") {
          fd = "101.00"
        } else if (fd === "关") {
          fd = "100.00"
        }

        if (fld.id === 18) {
          if (fd < fld.upLimit || fd > fld.downLimit) {
            this.loadingDownCom = false
            flag = false
            this.$message.error("电池容量需要在范围之内！")
            return;
          }
        }
        var fdVal = Number(Number(fd) * Number(fld.binaryBit)).toFixed(2);
        var bitVal = 0;
        if (Number(fdVal) < 0) {
          let absVal = Math.abs(Number(fdVal));
          let hexAbsVal = absVal.toString(16);
          let paddedHexAbsVal = hexAbsVal.padStart(4, "0");
          let invertedHex = "";
          for (let i = 0; i < paddedHexAbsVal.length; i++) {
            let hexChar = paddedHexAbsVal[i];
            if (hexChar === "0") {
              invertedHex += "F";
            } else if (hexChar === "F") {
              invertedHex += "0";
            } else {
              let decimalValue = parseInt(hexChar, 16);
              invertedHex += (15 - decimalValue).toString(16);
            }
          }
          bitVal = (parseInt(invertedHex, 16) + 1).toString(16).padStart(4, "F");
        } else {
          bitVal = (Number(fdVal).toString(16)).padStart(4, "0");
        }

        msgData += bitVal;
      });
      if (flag) {
        resParam.Data = msgData.toUpperCase();
        var dataObj = JSON.stringify(resParam);
        resParam.batNo = this.devicesn;
        resParam.dataType = "B9";
        resParam.sendTopic = topic;
        resParam.filedVal = msgData.toUpperCase() + "0000000000000000";
        let params = {}
        params.Cmd = "B9";
        params.Data = msgData.toUpperCase() + "0000000000000000";
        let jsonStr1 = "{\n\"Cmd\":\"" + params.Cmd + "\",\n\"Data\":\"" + params.Data + "\"\n}";
        console.log(jsonStr1)
        resParam.operatorId = this.userInfo.user_id
        sendComC8(resParam).then(res => {

        })
        if (this.client && this.client.connected) {
          this.client.publish(topic, jsonStr1);

          // 创建临时的响应处理监听器
          const responseHandler = (topicAck, message, packet) => {
            if (this.timeoutId) {
              clearTimeout(this.timeoutId);
              this.timeoutId = null;
            }

            console.log("服务器响应")

            try {
              let jsonStr = message.toString();
              let params = JSON.parse(jsonStr);
              console.log(JSON.stringify(params) + "下发B9")
              if (!this.hasProcessedMessage) {
                if (params.Data === 1) {
                  this.loadingDownCom = false
                  this.$message.success("操作成功！");
                } else {
                  this.loadingDownCom = false
                  this.$message.error("操作失败！");
                }
                this.hasProcessedMessage = true;

                // 移除临时响应处理器
                this.client.removeListener('message', responseHandler);
              }
            } catch (error) {
              console.error('解析响应消息失败:', error);
              this.loadingDownCom = false;
              this.$message.error("响应解析失败");

              // 移除临时响应处理器
              this.client.removeListener('message', responseHandler);
            }
          };

          // 添加临时的响应处理监听器
          this.client.on('message', responseHandler);

          this.timeoutId = setTimeout(() => {
            this.loadingDownCom = false
            this.$message.error("操作超时，请重新下发！");
            this.timeoutId = null;

            // 超时时也要移除临时响应处理器
            this.client.removeListener('message', responseHandler);
          }, 8000);
        } else {
          console.log("未连接");
          this.loadingDownCom = false;
        }
      }
    },
    downC8Handle(item) {
      console.log(item)
      if (this.status === 2) {
        this.$message.warning("设备离线，无法下发！");
        return;
      }
      this.loadingDownCom = true
      this.hasProcessedMessage = false;
      var topic = "JD/KDB/" + this.devicesn + "/Send";
      let params = {}
      params.Cmd = item.dataType;
      params.Data = item.filedVal;
      let jsonStr1 = "{\n\"Cmd\":\"" + params.Cmd + "\",\n\"Data\":\"" + params.Data + "\"\n}";
      console.log(topic)
      console.log(jsonStr1)

      item.batNo = this.devicesn
      item.operatorId = this.userInfo.user_id
      // 先发送API请求
      sendComC8(item).then(res => {
        // API请求成功后，再发送MQTT消息
        if (this.client && this.client.connected) {
          this.client.publish(topic, jsonStr1);

          // 创建临时的响应处理监听器
          const responseHandler = (topicAck, message, packet) => {
            if (this.timeoutId) {
              clearTimeout(this.timeoutId);
              this.timeoutId = null;
            }
            console.log("服务器响应")

            try {
              let jsonStr = message.toString();
              let params = JSON.parse(jsonStr);
              console.log(JSON.stringify(params) + "下发C8")
              if (!this.hasProcessedMessage) {
                if (params.Data === 1) {
                  this.loadingDownCom = false
                  this.$message.success("操作成功！");
                } else {
                  this.loadingDownCom = false
                  this.$message.error("操作失败！");
                }
                this.hasProcessedMessage = true;

                // 移除临时响应处理器
                this.client.removeListener('message', responseHandler);
              }
            } catch (error) {
              console.error('解析响应消息失败:', error);
              this.loadingDownCom = false;
              this.$message.error("响应解析失败");

              // 移除临时响应处理器
              this.client.removeListener('message', responseHandler);
            }
          };

          // 添加临时的响应处理监听器
          this.client.on('message', responseHandler);

          this.timeoutId = setTimeout(() => {
            this.loadingDownCom = false
            this.$message.error("操作超时，请重新下发！");
            this.timeoutId = null;

            // 超时时也要移除临时响应处理器
            this.client.removeListener('message', responseHandler);
          }, 8000);
        } else {
          console.log("未连接");
          this.loadingDownCom = false;
          this.$message.error("设备未连接！");
        }
      }).catch(error => {
        console.error('发送指令失败:', error);
        this.loadingDownCom = false;
        this.$message.error('发送指令失败，请重试');
      });
    },
    changeHandle(item) {
    },
    resetSelectValue(field) {
      // 先将值设为null，这样即使选择相同的值也会触发change事件
      this.form[field] = null;
    },
    //下发
    instructionsChangeHandle(item, data) {
      if (data) {
        this.IssueBtn = true
      }else{
        this.IssueBtn = false
      }
      let minutesToHex1 = this.minutesToHex(data);
      item.filedVal = minutesToHex1
      this.itemIssue = item
      //this.downC8Handle(item)
    },

    minutesToHex(minutes) {
      // 直接转换为整数（丢弃小数部分）
      const integerMinutes = Math.floor(minutes);

      // 转换为16进制并补零至4位
      return integerMinutes.toString(16).toUpperCase().padStart(4, '0');
    },
    hexStringToNumbers(hexStr) {
      try {
        if (!hexStr || typeof hexStr !== 'string') {
          console.warn('无效的十六进制字符串:', hexStr);
          return [];
        }

        const hexGroups = hexStr.match(/.{1,4}/g);
        if (!hexGroups) {
          console.warn('无法解析十六进制字符串:', hexStr);
          return [];
        }

        const numbers = [];
        hexGroups.forEach(hexGroup => {
          try {
            let num = parseInt(hexGroup, 16);
            if (isNaN(num)) {
              console.warn('无效的十六进制组:', hexGroup);
              num = 0;
            }
            if (hexGroup.startsWith('F') && hexGroup !== 'FFFFFFFF') {
              num = -(0x10000 - num);
            }
            numbers.push(num);
          } catch (error) {
            console.error('解析十六进制组失败:', error, hexGroup);
            numbers.push(0);
          }
        });
        return numbers;
      } catch (error) {
        console.error('hexStringToNumbers方法失败:', error, hexStr);
        return [];
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud && this.$refs.crud.toggleSelection) {
        this.$refs.crud.toggleSelection();
      }
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        }).catch(error => {
          console.error('获取详情失败:', error);
          this.$message.error('获取详情失败');
        });
      }
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.equNo = this.devicesn
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      }).catch(error => {
        console.error('加载数据失败:', error);
        this.loading = false;
        this.$message.error('加载数据失败，请重试');
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.ibms-container {
  height: 100vh;
  padding: 15px;
  background-color: #1e1e1e;
  color: #e0e0e0;
  font-family: "Microsoft YaHei", sans-serif;
  box-sizing: border-box;
}

.battery-control-grid {
  display: grid;
  height: 100%;
  grid-template-columns: 60% 40%;
  grid-template-rows: auto 1fr;
  grid-template-areas:
    "config control"
    "config log";
  gap: 15px;
}

.bms-config {
  grid-area: config;
  background: #1e1e1e;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.bms-control {
  grid-area: control;
  background: #1e1e1e;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  padding: 10px 10px 0 10px;
}

.operation-log {
  grid-area: log;
  background: #1e1e1e;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.control-buttons,
.log-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #3a3a3a;

  h3 {
    color: #2ec7c9;
    margin: 0;
    font-size: 16px;
  }
}

.el-button {
  padding: 8px 15px;
  background: rgba(46, 199, 201, 0.3);
  border-color: #2ec7c9;
  color: #fff;
  font-size: 15px;

  &:hover {
    background: rgba(46, 199, 201, 0.5);
  }
}

.param-section {
  margin-bottom: 20px;
  border: 1px solid rgba(46, 199, 201, 0.3);
  border-radius: 4px;
  padding: 10px;
  background: rgba(37, 37, 37, 0.5);

  .section-title {
    color: #2ec7c9;
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(46, 199, 201, 0.3);
  }
}

.config-grid {
  display: flex;
  height: calc(100% - 45px);
  overflow-y: auto;
  gap: 10px;
}

.config-column {
  flex: 1;
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px;
  background: #252525;
  border: 1px solid #3a3a3a;
  border-radius: 4px;

  &:hover {
    background: rgba(46, 199, 201, 0.15);
  }
}

.config-label {
  width: 30%;
  font-size: 13px;
  color: #b8c2cc;
}

.config-input {
  width: 30%;

  .el-select, .el-input {
    width: 100%;
  }
}

.config-range {
  width: 37%;
  font-size: 12px;
  margin-left: 2%;
}

.control-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 12px;
  height: calc(100% - 35px);
  overflow-y: auto;
}

.control-item {
  display: flex;
  align-items: center;
  padding: 10px 10px;
  background: #252525;
  border: 1px solid #3a3a3a;
  border-radius: 6px;
  margin-bottom: 8px;
  min-height: 56px;

  &:hover {
    background: rgba(46, 199, 201, 0.15);
  }
}

.control-label {
  flex: 1;
  font-size: 14px;
  color: #b8c2cc;
  padding-right: 12px;

  .label-decoration {
    display: inline-block;
    padding-left: 10px;
    border-left: 2px solid #2ec7c9;
    line-height: 1.4;
  }
}

.control-button {
  width: 90px;
  padding: 10px 0;
  font-size: 13px;
  background-color: #252525;
  border: 1px solid #ffffff;
}

@media (max-width: 1200px) {
  .control-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .control-item {
    min-height: 52px;
  }
}

.avue-crud {
  height: calc(100% - 50px);

  :deep(.el-table) {
    background: transparent;

    th, td {
      background: transparent;
      border-bottom: 1px solid rgba(46, 199, 201, 0.2);
      color: #e0e0e0;
    }

    th {
      background: rgba(11, 25, 41, 0.8);
    }

    tr:hover td {
      background: rgba(46, 199, 201, 0.1) !important;
    }
  }
}

:deep(.el-input__inner) {
  border-color: rgba(46, 199, 201, 0.3);
  color: #e0e0e0;
  height: 32px;
  line-height: 32px;

  &:hover, &:focus {
    border-color: #2ec7c9;
  }
}

:deep(.el-select-dropdown) {
  background-color: #1a2a3a;
  border-color: rgba(46, 199, 201, 0.3);

  .el-select-dropdown__item {
    color: #e0e0e0;

    &:hover, &.selected {
      background-color: rgba(46, 199, 201, 0.2);
    }
  }
}

@media (max-width: 1200px) {
  .battery-control-grid {
    grid-template-columns: 100%;
    grid-template-rows: auto auto auto;
    grid-template-areas:
      "config"
      "control"
      "log";
  }

  .config-grid {
    flex-direction: column;
  }

  .control-grid {
    grid-template-columns: 1fr;
  }

  .config-item {
    flex-wrap: wrap;

    .config-label,
    .config-input,
    .config-range {
      width: 100%;
      margin-bottom: 5px;
    }
  }
}

.icon-logs::before {
  content: "📝";
}

.icon-control::before {
  content: "⚙️";
}

.icon-batter::before {
  content: "🔋";
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.control-title {
  display: flex;
  align-items: center;
}

.batch-down-btn {
  padding: 8px 16px;
  font-size: 14px;
}

</style>
