<template>
  <!-- <i :class="isFullScren ? 'icon-tuichuquanping' : 'icon-quanping'" @click="handleScreen"></i> -->
  <img @click="handleScreen" src="/img/cockpit/screen.png" />
</template>
<script>
import { mapGetters } from "vuex";
import { fullscreenToggel, listenfullscreen } from "utils/util";

export default {
  computed: {
    ...mapGetters(["isFullScren"]),
  },
  mounted() {
    listenfullscreen(this.setScreen);
  },
  methods: {
    setScreen() {
      this.$store.commit("SET_FULLSCREN");
    },
    handleScreen() {
      fullscreenToggel();
    },
  },
};
</script>
<style lang="scss" scoped>
img {
  width: 32npx;
  height: 32npx;
  cursor: pointer;
}
</style>
