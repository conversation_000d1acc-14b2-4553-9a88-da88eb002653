<template>
  <div class="mapModel">
    <div class="mapModel-header">
      <div class="mapModel-header-search">
        <el-input
          class="mapModel-header-search-input"
          placeholder="请输入关键字回车键定位"
          v-model="keywords"
          @change="remoteMethod"
        />
        <!-- <el-select
          v-model="keywords"
          multiple
          filterable
          remote
          reserve-keyword
          placeholder="请输入关键字搜索"
          :remote-method="remoteMethod"
          :loading="loading"
          class="mapModel-header-search-input"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.address"
            :value="item.id"
          >
          </el-option>
        </el-select> -->

        <el-button type="primary" v-if="!polygon && !isPolygon" @click="handlePolygonAdd"
          >框选</el-button
        >
        <el-button type="primary" v-if="isPolygon" @click="handlePolygonYes"
          >确定</el-button
        >
        <el-button type="primary" v-if="polygon" @click="handlePolygonClear"
          >清空</el-button
        >
        <el-button type="primary" v-if="polygon" @click="handleSubmit">保存</el-button>
      </div>
    </div>
    <div class="AlarmapContainer" id="AlarmapContainer"></div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { AMapLoaderInitMap } from "@/utils/amap";
let polygonObj = [];
let placeSearch = null;
export default {
  props: {
    info: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      loading: false,
      map: null,
      polygon: null,
      isPolygon: false,
      options: [],
      keywords: "",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },

  methods: {
    remoteMethod(query) {
      placeSearch.search(query, (status, result) => {
        this.options = result.poiList.pois;
        this.loading = false;
      });
      // if (query !== "") {
      //   this.loading = true;
      //   console.log(query);
      //   placeSearch.search(query, (status, result) => {
      //     console.log(result);
      //     this.options = result.poiList.pois;
      //     this.loading = false;
      //   });
      // } else {
      //   this.options = [];
      // }
    },
    handlePolygonYes() {
      this.polygon = polygonObj;
      this.polyEditor.close();
      this.isPolygon = false;
    },
    handlePolygonAdd() {
      this.isPolygon = true;
      this.polyEditor.close();
      this.polyEditor.setTarget();
      this.polyEditor.open();
    },
    handlePolygonEdit() {
      this.polyEditor.setTarget(this.polygon);
      this.polyEditor.open();
    },
    handlePolygonClear() {
      this.map.remove(this.polygon);
      this.polygon = null;
    },
    handleSubmit() {
      const selectRange = JSON.stringify(
        this.polygon.getPath().map((point) => [point.lng, point.lat])
      );
      this.$emit("rowUpdate", { ...this.info, selectRange });
    },
    handleSearch() {},
  },
  mounted() {
    AMapLoaderInitMap({
      afterRequest: (AMap) => {
        this.map = new AMap.Map("AlarmapContainer", {
          //设置地图容器id
          viewMode: "3D", //是否为3D地图模式
          zoom: 8, //初始化地图级别
          resizeEnable: true,
        });
        //搜索
        let autoOptions = {
          city: "全国",
          pageSize: 5, // 单页显示结果条数
          pageIndex: 1, // 页码
          citylimit: true, //是否强制限制在设置的城市内搜索
          map: this.map, // 展现结果的地图实例
          panel: "panel", // 结果列表将在此容器中进行展示。
          autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
        };
        placeSearch = new AMap.PlaceSearch(autoOptions);

        // 数据框选
        const polyEditor = new AMap.PolygonEditor(this.map);
        this.polyEditor = polyEditor;

        if (this.info.selectRange) {
          const polygon = new AMap.Polygon({
            path: JSON.parse(this.info.selectRange),
          });
          this.map.add([polygon]);
          this.map.setFitView();
          this.polygon = polygon;
          polygon.on("dblclick", () => {
            polyEditor.setTarget(polygon);
            polyEditor.open();
          });
        }

        polyEditor.on("add", (data) => {
          let polygon = data.target;
          polygonObj = polygon;
          polyEditor.addAdsorbPolygons(polygon);
          polygon.on("dblclick", () => {
            this.handlePolygonAdd();
          });
        });
      },
    });
  },
};
</script>

<style lang="scss" scoped>
.mapModel {
  width: 100%;
  height: 100%;
}

.mapModel-header {
  width: 100%;
  height: 6%;
  background-color: #fff;
  padding: 0px 10px;

  &-search {
    width: 400px;
    height: 100%;
    display: flex;
    align-items: center;

    &-input {
      width: 300px;
      margin-right: 10px;
    }
  }
}

.AlarmapContainer {
  width: 100%;
  height: 94%;
}
</style>
