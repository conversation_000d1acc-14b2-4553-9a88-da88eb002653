import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/base/deviceinfo/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/api/base/deviceinfo/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/base/deviceinfo/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/api/base/deviceinfo/save',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/base/deviceinfo/submit',
        method: 'post',
        data: row
    })
}

export const getImpotList = (current, size, params) => {
    return request({
        url: '/api/base/deviceinfo/getImportPage',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const saveMark = (row) => {
    return request({
        url: '/api/base/devicemark/save',
        method: 'post',
        data: row
    })
}

export const removeMark = (ids) => {
    return request({
        url: '/api/base/devicemark/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const getDeviceData = (current, size, params) => {
    return request({
        url: '/api/base/deviceinfo/getDeviceData',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getRealDataParam = (current, size, params) => {
    return request({
        url: '/api/base/deviceinfo/getRealDataParam',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getHistoryDataParam = (current, size, params) => {
    return request({
        url: '/api/base/deviceinfo/getHistoryDataParam',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}