<template>
  <div class="wel-table">
    <table style="table-layout: fixed; min-width: 100%">
      <colgroup>
        <col
          v-for="items in column"
          :data-key="items.dataIndex"
          :style="{ width: `${items.width}px`, minWidth: `${items.width}px` }"
        />
      </colgroup>
      <thead class="ant-table-thead">
        <tr>
          <th
            class="wel-table-thead-cell"
            v-for="(items, index) in column"
            colspan="1"
            rowspan="1"
            :colstart="index"
            :key="items.dataIndex"
          >
            {{ items.title }}
          </th>
        </tr>
      </thead>
    </table>
    <div
      class="body-scrollbar"
      style="overflow: hidden; overflow-y: scroll; width: 100%"
      :style="{ 'max-height': '85%' }"
    >
      <table style="table-layout: fixed; min-width: 100%">
        <colgroup>
          <col
            v-for="items in column"
            :data-key="items.dataIndex"
            :style="{ width: `${items.width}px`, minWidth: `${items.width}px` }"
          />
        </colgroup>
        <tbody class="ant-table-tbody">
          <tr
            v-for="(tableItems, tableIndex) in dataSource"
            :data-row-key="tableIndex"
            class="ant-table-row ant-table-row-level-0"
            data-row-key="1"
          >
            <td
              v-for="items in column"
              :key="items.dataIndex"
              class="wel-table-tabletbody-cell"
              :class="{ 'wel-table-tabletbody-cell-action': tableIndex % 2 === 0 }"
            >
              <slot v-if="items.slot" v-bind="tableItems" :name="items.dataIndex"> </slot>
              <template v-else>
                {{ tableItems[items.dataIndex] }}
              </template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  emits: ["update:value"],
  props: {
    column: {
      type: Array,
      default: [],
    },
    dataSource: {
      type: Array,
      default: [],
    },
    scroll: {
      type: Object,
      default: {
        y: 0,
      },
    },
  },
  methods: {
    handleClick(val) {
      this.$emit("update:value", val.value);
    },
  },
};
</script>
<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.body-scrollbar::-webkit-scrollbar-thumb {
  background: #07b667;
}
.wel-table {
  width: 100%;
  font-size: 14npx;
  color: #333333;
  flex: 0.95;
  overflow: hidden;

  table {
    width: 100%;
  }

  &-scroll {
    overflow: auto scroll;
    height: 84%;
    // padding-right: 10px;

    &-bottom {
      height: 20npx;
    }
  }

  &-thead {
    width: 100%;

    &-cell {
      padding: 8npx 10npx;
      text-align: left;
      // text-align: center;
    }
  }

  &-tabletbody {
    //flex: 1;
    overflow-y: auto;
    width: 100%;
    &-cell {
      padding: 8npx 10npx;
      // text-align: center;
      // white-space: nowrap;
      // word-break: keep-all;
      // overflow: hidden;
      // text-overflow: ellipsis;
      &-action {
        background: rgba(152, 161, 177, 0.12);
      }
    }
  }
}
</style>
