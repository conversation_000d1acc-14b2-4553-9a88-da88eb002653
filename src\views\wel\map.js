

const initEchartsMap = (data) => {
    let privinceList = [
        {
            code: 110000,
            area: "北京",
        },
        {
            code: 120000,
            area: "天津",
        },
        {
            code: 130000,
            area: "河北",
        },
        {
            code: 140000,
            area: "山西",
        },
        {
            code: 150000,
            area: "内蒙古",
        },
        {
            code: 210000,
            area: "辽宁",
        },
        {
            code: 220000,
            area: "吉林",
        },
        {
            code: 230000,
            area: "黑龙江",
        },
        {
            code: 310000,
            area: "上海",
        },
        {
            code: 320000,
            area: "江苏",
        },
        {
            code: 330000,
            area: "浙江",
        },
        {
            code: 340000,
            area: "安徽",
        },
        {
            code: 350000,
            area: "福建",
        },
        {
            code: 360000,
            area: "江西",
        },
        {
            code: 370000,
            area: "山东",
        },
        {
            code: 410000,
            area: "河南",
        },
        {
            code: 420000,
            area: "湖北",
        },
        {
            code: 430000,
            area: "湖南",
        },
        {
            code: 440000,
            area: "广东",
        },
        {
            code: 450000,
            area: "广西",
        },
        {
            code: 460000,
            area: "海南",
        },
        {
            code: 500000,
            area: "重庆",
        },
        {
            code: 510000,
            area: "四川",
        },
        {
            code: 520000,
            area: "贵州",
        },
        {
            code: 530000,
            area: "云南",
        },
        {
            code: 540000,
            area: "西藏",
        },
        {
            code: 610000,
            area: "陕西",
        },
        {
            code: 620000,
            area: "甘肃",
        },
        {
            code: 630000,
            area: "青海",
        },
        {
            code: 640000,
            area: "宁夏",
        },
        {
            code: 650000,
            area: "新疆",
        },
        {
            code: 710000,
            area: "台湾",
        },
        {
            code: 810000,
            area: "香港",
        },
        {
            code: 820000,
            area: "澳门",
        },
    ];

    data.forEach(dt => {
        var pvlList = privinceList.filter(pvl => pvl.name === dt.area);
        if (pvlList.length > 0) {
            dt.code = pvlList[0].code;
        }
    })

    return {
        title: {
            text: "",
            left: "center",
        },
        /**
         * 你可以自定义样式
         **/
        tooltip: {
            formatter: function (params) {
                return (
                    params.name + "<br>" + "设备数:" + params.value + "<br>"
                );
            },
        },
        visualMap: {
            min: 0,
            max: 10,
            left: "left",
            top: "bottom",
            text: ["高", "低"],
            calculable: true,
            // seriesIndex: [1],
            inRange: {
                color: ["white", "red"],
            },
            show: true,
        },
        toolbox: {
            show: true,
            feature: {
                saveAsImage: {
                    pixelRatio: 4,
                },
            },
        },
        series: [
            {
                type: "map",
                mapType: 'china',
                selectedMode: "false", //是否允许选中多个区域
                label: {
                    normal: {
                        show: true,
                    },
                    emphasis: {
                        show: true,
                    },
                },
                zoom: 0.7, //当前视角的缩放比例
                roam: true, //是否开启平游或缩放
                data: data,
            },
        ],
    };

}
export {
    initEchartsMap
}
