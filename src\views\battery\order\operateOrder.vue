<template>
  <div class="orderAll-container">
    <basic-container>
      <avue-crud
          style="width: 100%;"
          v-model:search="search"
          :data="tableData"
          :option="option"
          v-model:page="page"
          @current-change="currentChange"
          @search-change="searchChange">
        <template #menu="{ size, row, index }">
          <el-button
              @click.stop="handleView(row)"
              text
              type="primary"
              :size="size">
            确定续费
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {getOrderPages, submitDeviceRenew} from "@/api/pay/order";
import useStore from "vuex/dist/vuex.mjs";
import eventBus from "@/utils/eventBus";

export default {
  name: "operateOrder",
  data() {
    return {
      tableData: [],
      query: {
        orderStatus: 1,
        payStatus: 2,
      },
      search: {},
      //日志功能
      option: {
        searchMenuSpan: 2,
        editBtn: false,
        delBtn: false,
        refreshBtn: false,
        columnBtn: false,
        addBtn: false,
        height: 500,
        menuWidth: 200,
        emptyBtn: false,
        tip: false,
        index: true,
        labelWidth: 140,
        indexLabel: '序列',
        indexWidth: 60,
        align: "center",
        column: [
          {
            label: '订单编号',
            prop: 'orderNo',
            search: true,
            searchSpan: 4,
            width: 160,
            change: (res => {
              this.query.orderNo = res.value;
            }),
          }, {
            label: '订单状态',
            prop: 'orderStatus',
            type: "select",
            search: false,
            searchSpan: 4,
            width: 120,
            dicData: [
              {
                label: "续费中",
                value: '1'
              }
            ]
          }, {
            label: '支付方式',
            prop: 'paymentMethod',
            formatter: function (row, value, column) {
              return "微信"
            },
            width: 150,
          }, {
            label: '支付时间',
            prop: 'paymentTime',
            width: 200,
          }, {
            label: '续费设备数量',
            prop: 'deviceNum',
            formatter: function (row, value, column) {
              return value + "台";
            },
            width: 150,
          }, {
            label: '支付金额',
            prop: 'paymentAmount',
            formatter: function (row, value, column) {
              return value + "元";
            },
            width: 150,
          }, {
            label: '设备编号',
            prop: 'equNo',
            width: 120,
          }, {
            label: '续费年限',
            prop: 'renewTime',
            formatter: function (row, value, column) {
              return value + "年";
            },

          }, {
            label: '支付状态',
            prop: 'payStatus',
            type: "select",
            change: (res => {
              this.query.payStatus = res.value;
              this.getOperateOrderAll(this.page, this.query);
            }),
            dicData: [
              {
                label: "已支付",
                value: '2'
              },
            ]
          }
        ]
      },
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      isDestroyed: false,
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  mounted() {
    this.isDestroyed = false;
    this.query.userId = this.userInfo.user_id;
    this.getOperateOrderAll(this.page, this.query)
  },
  beforeUnmount() {
    this.isDestroyed = true;
    this.tableData = [];
  },
  methods: {
    getOperateOrderAll(page, params = {}) {
      getOrderPages({pageNum: page.currentPage, pageSize: page.pageSize, ...params}).then(res => {
        if (this.isDestroyed) return;
        this.page.total = res.data.data.total
        this.tableData = res.data.data.data

        this.$emit('queryData',  this.page.total);
        eventBus.$emit('dataChanged', this.page.total);
      })
    },
    handleView(row) {
      let form = {
        id: row.id,
        deviceId: row.deviceId,
        renewTime: row.renewTime
      }
      submitDeviceRenew(form).then(res => {
        if (this.isDestroyed) return;
        if (res.data.code === 200) {
          this.getOperateOrderAll(this.page, this.query);
          this.$message.success("操作成功！");
          import('@/utils/eventBus').then(module => {
            if (this.isDestroyed) return;
            module.default.$emit('reload-device-price', true);
          });
        } else {
          this.$message.error(res.data.msg);
        }
      })
    },
    changeOrderNo(res) {
      this.query.orderNo = res.value;
      this.getOperateOrderAll(this.page, this.query);
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.getOperateOrderAll(this.page, this.query);
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.getOperateOrderAll(this.page, this.query);
    },
    searchChange(val, done) {
      this.getOperateOrderAll(this.page, this.query);
      done();
    }

  },

}
</script>

<style lang="scss" scoped>
.orderAll-container {
  width: 100%;
  color: #ffffff;
  background-color: #000;
  height: 82vh;
  border: 1px solid white;
  overflow-y: auto;
  overflow-x: hidden;

  &-right {
    width: 100%;
    height: 100%;

  }
}

</style>

<style>
.el-card__body {
  position: relative;
  height: 100%;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.box-card {
  width: 480px;
}
</style>
