<template>
    <div class="wel-tabs" :style="tabStyle">
        <div class="wel-tabs-options" :class="{ 'wel-tabs-options-action': items.value === value }"
            @click="handleClick(items)" v-for="items in  options " :key="items.value">
            {{ items.label }}
            <div class="animate__animated animate__rubberBand" v-if="items.value === value">
            </div>
        </div>
    </div>
</template>
  
<script>

export default {
    data() {
        return {}
    },
    emits: ['update:value'],
    props: {
        tabStyle: {
            type: Object,
            default: {}
        },
        options: {
            type: Array,
            default: []
        },
        value: {
            type: Number,
        }
    },
    methods: {
        handleClick(val) {
            this.$emit('update:value', val.value)
        }
    },
};
</script>
<style lang="scss" scoped>
.wel-tabs {
    width: 100%;
    height: 34npx;
    font-size: 16px;
    font-weight: 500;
    color: #999999;
    display: flex;
    margin: 15px 0px;
    border-bottom: 1px solid #FAFAFA;

    &-options {
        margin-right: 30px;
        position: relative;
        cursor: pointer;

        &-action {
            color: #2A357A;

            div {
                width: 100%;
                height: 2px;
                background-color: #07B667;
                display: block;
                position: absolute;
                bottom: 0px;
                left: 0px;
                animation-duration: 1s
            }
        }

    }
}
</style>
  