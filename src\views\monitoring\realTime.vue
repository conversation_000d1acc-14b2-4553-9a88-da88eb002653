<template>
  <basic-container>
    <el-form ref="submitForm" :inline="true" :model="query" label-width="80px">
      <el-form-item label="产品名称" prop="productId">
        <el-select v-model="query.productId" placeholder="请选择产品名称">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.productName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectId">
        <el-select v-model="query.projectId" placeholder="请选择项目名称">
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceId">
        <el-select v-model="query.id" placeholder="请选择设备名称">
          <el-option
            v-for="item in deviceList"
            :key="item.id"
            :label="item.deviceName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围" prop="timeFrame">
        <el-select v-model="query.timeFrame" placeholder="请选择时间范围">
          <el-option
            v-for="item in timeFrameOptopm"
            :key="item.dictKey"
            :label="item.dictValue"
            :value="item.dictKey"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="5">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
          >全选</el-checkbox
        >
        <el-checkbox-group v-model="checkedDatas" @change="handleCheckedDatasChange">
          <el-checkbox
            style="width: 100%"
            v-for="item in dataOptions"
            :key="item.dictKey"
            :label="item.dictKey"
            >{{ item.dictValue }}</el-checkbox
          >
        </el-checkbox-group>
      </el-col>
      <el-col :span="19">
        <div
          v-if="isContainer"
          class="wel-container-right-bottom animate__animated animate__bounceIn"
        >
          <div class="wel-container-tabs-postion">
            <TabView :options="totalData" v-model:value="densityValue" />
          </div>
          <div class="wel-container-right-total" id="densityEchats"></div>
        </div>
        <avue-crud
          :option="option"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          v-model="form"
          ref="crud"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @refresh-change="refreshChange"
          @on-load="onLoad"
        >
          <template #menu="{ size, row, index }">
            <el-button
              @click.stop="handleView(row, index)"
              icon="el-icon-view"
              text
              type="primary"
              :size="size"
              >查看</el-button
            >
          </template>
          <template slot="menuLeft">
            <el-button
              type="danger"
              size="small"
              icon="el-icon-delete"
              plain
              v-if="permission.deviceinfo_delete"
              @click="handleDelete"
              >删 除
            </el-button>
          </template>
        </avue-crud>
      </el-col>
    </el-row>
  </basic-container>
</template>

<script>
import * as echarts from "echarts";
import { getList as getDeviceList, getRealDataParam } from "@/api/device/list";
import { getList as getProductList } from "@/api/device/product";
import { getList as getProjectList } from "@/api/project/list";
import { validatenull } from "utils/validate";
import { getDictionary } from "@/api/system/dictbiz";
import { initDensityEchats } from "./ecahts";
let myChart = null;
let myChartTime = null;

export default {
  data() {
    return {
      loading: true,
      checkAll: false,
      isIndeterminate: true,
      dataOptions: [],
      checkedDatas: [],
      tableData: [],
      form: {},
      query: {
        productId: "",
        projectId: "",
        deviceId: "",
        timeFrame: "",
      },
      page: {
        pageSize: 100,
        currentPage: 1,
        total: 0,
      },
      isContainer: true,
      totalData: [
        { label: "年", value: 1 },
        { label: "日", value: 2 },
        { label: "月", value: 3 },
      ],
      densityValue: 1,
      selectionList: [],
      productList: [],
      projectList: [],
      deviceList: [],
      timeFrameOptopm: [],
      data: [],
      option: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        selection: true,
        dialogClickModal: false,
        menu: false,
        refreshBtn: false,
        searchShowBtn: false,
        columnBtn: false,
        column: [
          {
            label: "数据标识",
            prop: "columnKey",
          },
          {
            label: "数据名称",
            prop: "columnValue",
          },
          {
            label: "数据值",
            prop: "columnData",
          },
          {
            label: "时间",
            prop: "time",
          },
        ],
      },
      echatsObjData: {},
    };
  },
  watch: {
    isContainer: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.initDensityEchats(this.data);
          });
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.dept_add, false),
        viewBtn: this.validData(this.permission.dept_view, false),
        delBtn: this.validData(this.permission.dept_delete, false),
        editBtn: this.validData(this.permission.dept_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  async created() {
    this.getProductList(this.page);
    this.getProjectList(this.page);
    await this.getDeviceList(this.page);
    this.dataOptions = await this.getDict("devicePhysicalModel");
    this.timeFrameOptopm = await this.getDict("timeFrame");
    // this.checkedDatas = this.dataOptions.map( p => p.dictKey ); 默认全选
  },
  mounted() {
    // this.initDensityEchats(this.data);
  },
  methods: {
    submitForm() {
      this.refreshChange();
    },
    initDensityEchats() {
      // 基于准备好的dom，初始化echarts实例
      let chartDom = document.getElementById("densityEchats");
      //   if (myChart) {
      //     myChart.clear();
      //   } else {
      //     myChart = echarts.init(chartDom);
      //   }
      if (!myChart) {
        myChart = echarts.init(chartDom);
      }
      if (this.checkedDatas.length <= 0) {
        myChart.setOption(initDensityEchats([]), true);
        return [];
      }
      const myChartData = this.checkedDatas.map((_item, index) => {
        return {
          name: index,
          type: "line",
          // data: row.map(v => v._item),
          data: this.echatsObjData[_item],
          symbol: "circle",
          smooth: true,
          showSymbol: false,
          //   stack: "Total",
        };
      });
      // 绘制图表
      myChart.setOption(initDensityEchats(myChartData), true);
      myChartTime = setTimeout(() => {
        let echatsObjData = this.echatsObjData;
        this.checkedDatas.map((item) => {
          echatsObjData[item] = echatsObjData[item].filter((_, index) => index !== 0);
          echatsObjData[item].push(Math.random() * 10);
        });
        this.echatsObjData = echatsObjData;
        this.$nextTick(() => {
          this.initDensityEchats();
        });
      }, 2000);
    },
    handleCheckAllChange(val) {
      this.checkedDatas = val ? this.dataOptions.map((p) => p.dictKey) : [];
      this.isIndeterminate = false;
      this.refreshChange();
    },
    handleCheckedDatasChange(val) {
      const checkedCount = val.length;
      this.checkAll = checkedCount === this.dataOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.dataOptions.length;
      this.refreshChange();
    },
    getProductList(page) {
      getProductList(page.currentPage, page.pageSize, null).then((res) => {
        const data = res.data.data;
        this.productList = data.records;
      });
    },
    getProjectList(page) {
      getProjectList(page.currentPage, page.pageSize, null).then((res) => {
        const data = res.data.data;
        this.projectList = data.records;
      });
    },
    getDeviceList(page) {
      getDeviceList(page.currentPage, page.pageSize, null).then((res) => {
        const data = res.data.data;
        this.deviceList = data.records;
      });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      window.clearTimeout(myChartTime);
      getRealDataParam(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        let echatsObjData = {};

        if (Object.keys(this.echatsObjData).length <= 0) {
          for (let i = 0; i < this.dataOptions.length; i++) {
            echatsObjData[this.dataOptions[i].dictKey] = [
              Math.random() * 10,
              Math.random() * 10,
              Math.random() * 10,
              Math.random() * 10,
              Math.random() * 10,
              Math.random() * 10,
              Math.random() * 10,
              Math.random() * 10,
              Math.random() * 10,
              Math.random() * 10,
            ];
          }
        } else {
            echatsObjData = this.echatsObjData;
        }

        const rows = [];
        // this.page.total = data.total;
        data.records.forEach((itemData) => {
          this.dataOptions.forEach((_item) => {
            if (this.checkedDatas.indexOf(_item.dictKey) > -1) {
              const arr = {
                columnKey: _item.dictKey.toLowerCase(),
                columnValue: _item.dictValue,
                columnData: itemData[_item.dictKey.toLowerCase()],
                time: itemData.time,
              };
              rows.push(arr);
            }
          });
        });
        this.echatsObjData = echatsObjData;
        this.data = rows;

        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          this.initDensityEchats();
        });
      });
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    async getDict(code) {
      let result = [];
      const query = {
        code: code,
      };
      result = await getDictionary(query).then((res) => {
        const data = res.data.data;
        return data;
      });
      return result;
    },
  },
  beforeUnmount() {
    window.clearTimeout(myChartTime);
    if (myChart) {
      myChart.dispose();
      myChart = null;
    }
  },
};
</script>

<style lang="scss" scoped>
#totalEchats {
  width: 100%;
  flex: 1;
}

#densityEchats {
  width: 100%;
  flex: 1;
}

:global(#avue-view) {
  margin-bottom: 0px;
}

@mixin normalStyle() {
  width: 100%;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.08);
  border-radius: 6npx 6npx 6npx 6npx;
  background-color: #ffffff;
  padding: 0px 0npx;
}

.wel-container {
  position: relative;
  &-tabs-postion {
    position: absolute;
    top: 40npx;
    left: 15npx;
    z-index: 1;
  }

  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #2a357a;
    margin: 15px 0px;
    position: relative;
    padding-left: 12npx;
  }

  &-title::after {
    content: " ";
    width: 5npx;
    height: 21npx;
    display: block;
    background: #07b667;
    border-radius: 3px 3px 3px 3px;
    position: absolute;
    top: 2npx;
    left: 0px;
  }

  .welMap {
    width: 100%;
    height: 100%;
  }

  &-left,
  &-right {
    position: absolute;
    top: 10npx;
    height: 100%;
  }

  &-left {
    left: 10npx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 96%;
    top: 2%;

    &-top {
      height: 24%;
      @include normalStyle();
      display: flex;
      flex-wrap: wrap;

      &-items:nth-child(1),
      &-items:nth-child(2) {
        border-bottom: 1px solid #e5e6eb;
      }

      &-items {
        display: flex;
        width: 50%;
        height: 50%;
        align-items: center;
        justify-content: left;

        img {
          width: 40npx;
          height: 40npx;
          margin-right: 20npx;
        }

        &-block {
          div:nth-child(1) {
            font-size: 14npx;
            color: #333333;
            margin-bottom: 2npx;
          }

          div:nth-child(2) {
            font-size: 24npx;
            color: #333333;
          }
        }
      }
    }

    &-center {
      height: 36%;
      @include normalStyle();
      display: flex;
      flex-direction: column;
    }

    &-bottom {
      height: 36%;
      @include normalStyle();
      display: flex;
      flex-direction: column;
      position: relative;
      :deep(.wel-tabs) {
        margin-bottom: 0px;
      }
    }
  }

  &-right {
    right: 10npx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 96%;
    top: 2%;

    &-top {
      height: 49%;
      @include normalStyle();
      display: flex;
      flex-direction: column;
      position: relative;
      &-img {
      }
    }

    &-bottom {
      height: 300px;
      display: flex;
      flex-direction: column;
      @include normalStyle();
    }
  }
}
</style>
