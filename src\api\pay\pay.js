import request from '@/axios';

export const getWsQRcode = (params) => {
    return request({
        url: '/pay/createUnifiedorder',
        method: 'post',
        data: {
            ...params
        }
    })
}

export const getWsPayOrder = (params) => {
    return request({
        url: '/pay/queryWechatOrder',
        method: 'get',
        params: {
            ...params
        }
    })
}

export const getWsRefund = (params) => {
    return request({
        url: '/pay/refund',
        method: 'get',
        params: {
            ...params
        }
    })
}

export const getEquipmentYearPrice = () => {
    return request({
        url: '/money/getDevicePayMoneyList',
        method: 'get',
    })
}


export const saveDeviceMoney = (params) => {
    return request({
        url: '/money/saveDeviceMoney',
        method: 'post',
        data: params
    })
}

export const closeorder = (params) => {
    return request({
        url: '/pay/closeorder',
        method: 'get',
        params: {
            ...params
        }
    })
}
