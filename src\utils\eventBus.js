import { reactive } from 'vue'

const listeners = reactive({})
// mitt 事件总线
export default {
  $on(event, callback) {
    if (!listeners[event]) {
      listeners[event] = []
    }
    listeners[event].push(callback)
  },
  $off(event, callback) {
    if (!listeners[event]) return
    const index = listeners[event].indexOf(callback)
    if (index > -1) {
      listeners[event].splice(index, 1)
    }
  },
  $emit(event, ...args) {
    if (listeners[event]) {
      listeners[event].forEach(callback => {
        callback(...args)
      })
    }
  }
}
