import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/blade-deviceConfig/deviceConfig/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/blade-deviceConfig/deviceConfig/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const add = (row) => {
    return request({
        url: '/blade-deviceConfig/deviceConfig/submit',
        method: 'post',
        data: row
    })
}

export const remove = (ids) => {
    return request({
        url: '/blade-deviceConfig/deviceConfig/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}


export const publishTopic = () => {
    return request({
        url: '/mqtt/publishTopic',
        method: 'get'
    })
}