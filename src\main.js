import { createApp } from 'vue';
import website from './config/website';
import axios from './axios';
import router from './router/';
import store from './store';
import i18n from './lang/';
import { language, messages } from './lang/';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import Avue from '@smallwei/avue';
import '@smallwei/avue/lib/index.css';
import crudCommon from '@/mixins/crud.js';
import { getScreen } from './utils/util';
import './permission';
import error from './error';
import avueUeditor from 'avue-plugin-ueditor';
import basicBlock from 'components/basic-block/main.vue';
import basicContainer from 'components/basic-container/main.vue';
import thirdRegister from './components/third-register/main.vue';
import NfDesignBase from '@saber/nf-design-base-elp';
import flowDesign from './components/flow-design/main.vue';
import App from './App.vue';
import 'animate.css';
import 'dayjs/locale/zh-cn'
import dayjs from 'dayjs';
import 'styles/common.scss';
// 业务组件
import tenantPackage from './views/system/tenantpackage.vue';
import UpdateChecker from '/src/version/updateChecker.js';
import UpdateModal from '/src/version/UpdateModal.vue';
// main.js 或其他入口文件
import { createMemoryManager } from './utils/memoryManager';

// 创建内存管理器实例
const memoryManager = createMemoryManager({
  threshold: 1500,
  checkInterval: 30000,
  warningThreshold: 100,
  enableLogging: true,

  onCheck: (memoryInfo) => {
    console.log('内存状态:', memoryInfo);
  },

  onWarning: (info) => {
    console.warn('内存警告:', info);
  },

  onThresholdExceeded: (info) => {
    console.error('内存超限:', info);
  }
});

// 如果你的应用有状态管理，可以在这里设置
// memoryManager.setApplicationState(yourAppState);

// 启动内存监控
memoryManager.startMonitoring();
window.$crudCommon = crudCommon;
window.axios = axios;

const app = createApp(App);

// 注册全局组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 注册业务组件
app.component('basicContainer', basicContainer);
app.component('basicBlock', basicBlock);
app.component('thirdRegister', thirdRegister);
app.component('flowDesign', flowDesign);
app.component('tenantPackage', tenantPackage);
app.component('UpdateModal', UpdateModal);

dayjs.locale('zh-cn');
app.config.globalProperties.$dayjs = dayjs;
app.config.globalProperties.website = website;
app.config.globalProperties.getScreen = getScreen;

// 使用插件
app.use(error);
app.use(i18n);
app.use(store);
app.use(router);
app.use(ElementPlus, {
  locale: messages[language],
});
app.use(Avue, {
  axios,
  calcHeight: 10,
  locale: messages[language],
});
app.use(avueUeditor, { axios });
app.use(NfDesignBase);

// 在挂载前检查更新（唯一改动部分开始）
router.isReady().then(() => {
  app.mount('#app');

  // 持久化版本检查（兼容缓存清除）
  const checkUpdate = () => {
    const storedData = sessionStorage.getItem('app_version') ||
        localStorage.getItem('app_version');
    if (!storedData || JSON.parse(storedData).version !== UpdateChecker.currentVersion) {
      setTimeout(() => UpdateChecker.checkUpdate(), 500);
    }
  };

  // 立即检查 + 每12小时轮询一次
  checkUpdate();
  setInterval(checkUpdate, 12 * 60 * 60 * 1000);
});
// 唯一改动部分结束
