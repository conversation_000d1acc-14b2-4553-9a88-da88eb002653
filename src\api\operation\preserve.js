import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/base/devicemaintenance/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/api/base/devicemaintenance/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/base/devicemaintenance/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/api/base/devicemaintenance/submit',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/base/devicemaintenance/submit',
        method: 'post',
        data: row
    })
}

