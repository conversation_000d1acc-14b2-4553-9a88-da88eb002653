import dayjs from 'dayjs'

const currentMonth = dayjs().month(); // 获取当前的月份（0-11）
const currentYear = dayjs().year(); // 获取当前的年份
const currentDay = dayjs().day(); // 获取当前的年份
const getData = ({
    dateLen,
    dateType
}) => {
    let relust = []
    if (dateType === 1) {
        const thirtyDaysAgo = dayjs().subtract(30, 'day');
        for (let i = 0; i < 30; i++) {
            relust.push({
                date: thirtyDaysAgo.add(i, 'day').format('YYYY-MM-DD'),
                extinguishNum: parseInt(Math.random() * 100)
            });
        }
    } else if (dateType === 2) {
        for (let year = currentYear; year >= currentYear - 1; year--) {
            const startMonth = year === currentYear ? currentMonth : 11; // 如果是今年，则从当前月份开始，否则从12月份开始
            const endMonth = year === currentYear - 1 ? 0 : 11; // 如果是去年，则到1月份结束，否则到12月份结束
            for (let month = startMonth; month >= endMonth; month--) {
                relust.push({
                    date: dayjs().year(year).month(month).format('YYYY-MM'),
                    extinguishNum: parseInt(Math.random() * 1000)
                });
            }
        }
        relust = relust.reverse();
    } else {
        for (let i = 2016; i < 2024; i++) {
            relust.push({
                date: `${i}`,
                extinguishNum: parseInt(Math.random() * 10000)
            });
        }
    }
    return relust
}

export default getData