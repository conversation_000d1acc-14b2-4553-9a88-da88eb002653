<template>
  <div class="avue-top">
    <div class="top-bar__left">
      <div
          class="avue-breadcrumb"
          :class="[{ 'avue-breadcrumb--active': isCollapse }]"
          v-if="setting.collapse && !isHorizontal"
      >
        <i class="icon-navicon" @click="setCollapse"></i>
      </div>
    </div>
    <!-- <div class="top-bar__title">
      <top-menu ref="topMenu" v-if="setting.menu"></top-menu>
      <top-search class="top-bar__item" v-if="setting.search"></top-search>
    </div> -->

    <div class="top-bar__right">
      <div class="avue-topBar-right">
        <div class="avue-topBar-right-count fontSizeAdjust">{{ getTime }}</div>
        <div class="fontSizeAdjust">{{ getDD }}</div>
        <div class="fontSizeAdjust">{{ getMMDD }}</div>&nbsp;&nbsp;
        <div style="font-size: 15px" @click="contactDialogVisible = true">联系我们</div>
        <div style="width: 57px;font-size: 15px" @click="openFeedbackDialogVisible" v-if="userInfo.dept_id ==='1123598813738675201'">
          用户反馈  <el-badge :value="badgeCount" v-if="badgeCount!==0"  style="left: -10px;top: -8px"/>
        </div>
      </div>
      <!-- <div class="top-bar__right_text">
        <div class="fontSizeAdjust">余额 : 0.00 (币)</div>
        <a class="disable fontSizeAdjust" @click="paymentModel = true" >充值</a> |
        <a class="disable fontSizeAdjust" @click="renewModel = true" >续费设置</a>
      </div> -->
      <!-- <div class="top-bar__item">
        <top-color></top-color>
      </div> -->
      <!-- <div v-if="setting.lock" class="top-bar__item">
        <top-lock></top-lock>
      </div>
      <div v-if="setting.theme" class="top-bar__item">
        <top-theme></top-theme>
      </div> -->
      <!--<div class="top-bar__item">
        <top-lang></top-lang>
      </div>
      <top-full></top-full>-->
      <el-tooltip effect="dark" :content="$t('navbar.notice')" placement="bottom">
      </el-tooltip>
      <div class="top-user">
        <img class="top-bar__img" :src="userInfo.avatar"/>
        <el-dropdown>
          <span class="el-dropdown-link">
            <div class="fontSizeAdjust fontSizeAdjust2">{{ userInfo.nick_name }}</div>
            <el-icon class="el-icon--right">
              <arrow-down/>
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <!--              <el-dropdown-item>
                              <router-link to="/">
                                {{ $t("navbar.dashboard") }}
                              </router-link>
                            </el-dropdown-item>-->
              <el-dropdown-item @click="userModel = true">
                {{ $t("navbar.userinfo") }}
                <!-- <router-link to="/info/index">{{ $t("navbar.userinfo") }}</router-link> -->
              </el-dropdown-item>
              <!-- logout -->
              <el-dropdown-item @click="passModel = true">修改密码</el-dropdown-item>
              <!--              <el-dropdown-item @click="logout" divided
                              >{{ $t("navbar.logOut") }}
                            </el-dropdown-item>-->
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- <top-setting></top-setting> -->
      </div>
      <div class="logoutBtn" @click="logout">退出</div>
    </div>

    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="paymentModel"
        width="600px"
        title="微信充值"
    >
      <PaymentView/>
    </el-dialog>

    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="renewModel"
        width="600px"
        title="设置下级平台设备续费"
    >
      <RenewView/>
    </el-dialog>

    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="userModel"
        @close="handleClose"
        width="600px"
        title="个人信息">
      <UserInfo ref="userInfoRef" @colse="userModel=false"/>
    </el-dialog>

    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="passModel"
        width="600px"
        title="修改密码"
    >
      <UserPass @colse="passModel=false"/>
    </el-dialog>


    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="contactDialogVisible"
        width="600px"
        title="联系我们">
      <div style=" line-height: 30px;text-align: center;font-size: 25px">
        <img style=" vertical-align: middle;" src="/img/phone.png" alt="电话" width="40" height="40"> 暂未开放
      </div>
      <el-button @click="contactDialogVisible = false" style="float: right">关 闭</el-button>
      <br>
      <br>
    </el-dialog>

    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="feedbackDialogVisible"
        width="1000px"
        title="用户反馈记录">
      <div style="line-height: 30px">
        <el-table :data="feedbackTableData">
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="content" label="反馈问题" align="center" ></el-table-column>
          <el-table-column prop="link" label="图文" align="center" width="230">
            <template #default="{ row }">
              <!-- 新增：空值判断 -->
              <div v-if="!row.link || row.link.trim() === ''" class="empty-text">暂无</div>

              <!-- 处理多个图片链接 -->
              <div class="multi-image-container" v-else-if="isImage(row.link)">
                <el-image
                    v-for="(link, index) in row.link.split(',')"
                    :key="index"
                    :src="link.trim()"
                    :preview-src-list="row.link.split(',').filter(l => isImage(l.trim()))"
                    fit="cover"
                    style="width: 100px; height: 80px; margin: 0 1px 0 0; padding: 0 5px 0 0; border-radius: 4px; overflow: hidden; display: inline-block;"
                    @error="handleMediaError(row, 'image', index)"
                    :preview-teleported="true">
                  <template #error>
                    <div class="media-error" style="display: flex; flex-direction: column; justify-content: center; align-items: center; width: 100%; height: 100%; background: #f5f5f5;">
                      <i class="el-icon-picture-outline"></i>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
              </div>
              <!-- 视频（保持原有逻辑，只处理单个链接） -->
              <a v-else-if="isVideo(row.link)"
                 :href="row.link"
                 target="_blank"
                 class="video-link">
                <div class="video-placeholder" style="width: 100%; height: 100px">
                  <canvas :id="'canvas-' + row.id" style="width:100%;height:100%"></canvas>
                  <img class="fa-play" src="/img/播放.png" alt="播放" title="播放" width="45" height="45" style="position:absolute;">
                </div>
              </a>
              <!-- 普通链接 -->
              <a v-else :href="row.link" target="_blank" class="text-link">{{ truncateLink(row.link) }}</a>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
          <!--          <el-table-column prop="phoneModel" label="手机型号" align="center"></el-table-column>-->
          <el-table-column prop="createTime" label="提交时间" align="center"></el-table-column>

          <el-table-column prop="menu" label="操作" align="center" width="150">
            <template #="{ row }">
              <el-button type="danger"  size="small" v-if="row.isRead === 0" @click="handleRead(row)">
                未处理
              </el-button>
              <el-tag type="success" v-else>已处理</el-tag>
              <el-button v-if="row.isRead === 0" type="primary" size="small" style="margin-left: 5%" @click="openSmsVisible(row)">回复</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="query.pageNum"
            :page-sizes="[5, 10, 15, 20]"
            :page-size="query.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        ></el-pagination>
      </div>
      <template #footer>
        <el-button @click="feedbackDialogVisible = false">关 闭</el-button>
      </template>
    </el-dialog>

    <!--短信回复窗口-->
    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="smsVisible"
        width="400px"
        title="短信回复">
        <el-input
            type="textarea"
            placeholder="请输入内容,不能包含联系方式，链接地址"
            v-model="smsQuery.content"
            maxlength="35"
            rows="4"
            show-word-limit>
        </el-input>
      <br/>
      <br/>
      <el-button @click="handlSmsReply" style="float: right" type="primary">发送</el-button>
      <br>
      <br>
    </el-dialog>
  </div>




</template>

<script>
import { mapGetters } from "vuex";
import topLock from "./top-lock.vue";
import topMenu from "./top-menu.vue";
import topSearch from "./top-search.vue";
import topTheme from "./top-theme.vue";
import topLogs from "./top-logs.vue";
import topLang from "./top-lang.vue";
import topFull from "./top-full.vue";
// import topSetting from "../setting.vue";
import topNotice from "./top-notice.vue";
import PaymentView from "@/components/payment/payment.vue";
import RenewView from "@/components/payment/renew.vue";
import UserInfo from "./userInfo.vue";
import UserPass from "./userpass.vue";

let TimeSetInterval = null;
import { ref } from 'vue';
import {getFeedbackLogPages, readFeedbackLog, saveFeedbackLog, sendSmsReply} from "@/api/device/card";

const showPhone = ref(false);
export default {
  components: {
    topLock,
    topMenu,
    topSearch,
    topTheme,
    topLogs,
    topLang,
    topFull,
    // topSetting,
    topNotice,
    PaymentView,
    RenewView,
    UserInfo,
    UserPass,
  },
  name: "top",
  data() {
    return {
      smsQuery:{
        content:null,
        phone:null,
      },
      smsVisible:false,
      badgeCount:0,
      timeNum: new Date().getTime(),
      paymentModel: false,
      renewModel: false,
      userModel: false,
      passModel: false,
      contactDialogVisible: false,
      feedbackDialogVisible: false,
      feedbackTableData: [],
      total: 0,
      query: {
        pageNum: 1,
        pageSize: 10,
      }
    };
  },

  //页面销毁
  destroyed() {
    window.clearInterval(TimeSetInterval);
  },
  beforeUnmount() {
    window.clearInterval(TimeSetInterval);
  },
  filters: {},
  created() {
    //setTheme('theme-white')
    // this.$store.commit('SET_THEME_NAME', 'theme-white');
  },
  computed: {
    ...mapGetters([
      "setting",
      "userInfo",
      "tagWel",
      "tagList",
      "isCollapse",
      "tag",
      "logsLen",
      "logsFlag",
      "isHorizontal",
      'userInfo'
    ]),
    getDD() {
      return this.$dayjs(this.timeNum).format("dddd");
    },
    getMMDD() {
      return this.$dayjs(this.timeNum).format("MM月DD日");
    },
    getTime() {
      return this.$dayjs(this.timeNum).format("HH:mm:ss");
    },
  },
  mounted() {
    TimeSetInterval = setInterval(() => {
      this.timeNum = new Date().getTime();
    }, 1000);
    this.getReadFeedbackLog();
  },
  methods: {
    //短信回复
     handlSmsReply() {
       if(this.smsQuery.content===null){
         this.$message.warning("回复内容不能为空！")
         return;
       }
       sendSmsReply(this.smsQuery).then(res=>{
         this.smsVisible =false
         this.$message.success("发送成功！")
       })
     },
     //打开短信回复窗口
     openSmsVisible(row) {
       this.smsQuery.content =null
       this.smsVisible = true
       this.smsQuery.phone = row.phone
     },
    handleRead(row){
      row.isRead = 1
      saveFeedbackLog(row).then(res=>{
        this.$message.success(res.data.msg)
        this.getReadFeedbackLog();
        this.loadFeedbackData();
      })
    },
    getReadFeedbackLog(){
      readFeedbackLog().then(res=>{
        this.badgeCount = res.data.data
      })
    },
    handleClose(){
      this.$refs.userInfoRef.resetForm();
    },
    openFeedbackDialogVisible() {
      this.feedbackDialogVisible = true;
      this.loadFeedbackData();
    },
    handlePlayVideo(row) {
      if (!row.showVideo) {
        // 修复 this.$set 问题
        this.feedbackTableData = this.feedbackTableData.map(item => {
          if (item.id === row.id) {
            return { ...item, showVideo: true }
          }
          return item
        })
      }
    },
    // 加载反馈数据
    loadFeedbackData() {
      getFeedbackLogPages(this.query).then(res => {
        if (res.data.code === 200) {
          this.feedbackTableData = res.data.data.data.map(item => ({
            ...item,
            showVideo: false,
            frameLoaded: false,
            id: item.id || Math.random().toString(36).substr(2, 9) // 确保每个项都有唯一ID
          }));
          this.total = res.data.data.total;

          this.$nextTick(() => {
            this.feedbackTableData.forEach(row => {
              if (this.isVideo(row.link)) {
                this.getVideoMiddleFrame(row);
              }
            });
          });
        }

      }).catch(error => {
        console.error('加载反馈数据失败:', error);
        this.$message.error('加载反馈数据失败');
      });
    },
    // 分页大小改变
    handleSizeChange(val) {
      this.query.pageSize = val;
      this.loadFeedbackData();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.query.pageNum = val;
      this.loadFeedbackData();
    },

    // 判断是否为图片
    isImage(url) {
      return url && /\.(jpg|jpeg|png|gif|webp|bmp)$/i.test(url.split('?')[0]);
    },

    // 判断是否为视频
    isVideo(url) {
      return url && /\.(mp4|webm|ogg|mov)$/i.test(url.split('?')[0]);
    },

    // 显示视频
    showVideo(row) {
      this.$set(row, 'showVideo', true);
    },

    // 媒体加载失败处理
    handleMediaError(row, type) {
      console.error(`${type}加载失败:`, row.link);
      this.$set(row, 'mediaError', true);
    },

    // 缩短长链接显示
    truncateLink(url) {
      if (!url) return '';
      if (url.length > 30) {
        return url.substring(0, 15) + '...' + url.substring(url.lastIndexOf('/'));
      }
      return url;
    },
    setCollapse() {
      this.$store.commit("SET_COLLAPSE");
    },
    logout() {
      this.$confirm(this.$t("logoutTip"), this.$t("tip"), {
        confirmButtonText: this.$t("submitText"),
        cancelButtonText: this.$t("cancelText"),
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          localStorage.removeItem("devicesn")
          this.$router.push({ path: "/login" });
        });
      });
    },
    getVideoMiddleFrame(row) {
      const video = document.createElement('video');
      video.src = row.link;
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        const middleTime = video.duration / 2;
        video.currentTime = middleTime;

        video.onseeked = () => {
          const canvas = document.getElementById(`canvas-${row.id}`);
          if (canvas) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            // 直接修改数组项（Vue 3 会自动处理响应式）
            const index = this.feedbackTableData.findIndex(item => item.id === row.id);
            if (index !== -1) {
              this.feedbackTableData[index].frameLoaded = true;
            }
          }
        };
      };

      video.onerror = () => {
        console.error('视频加载失败', row.link);
      };
    }
  },
};
</script>

<style lang="scss" scoped>
.media-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.media-error,
.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 60px;
  background: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.media-error i,
.video-placeholder i {
  font-size: 20px;
  margin-bottom: 5px;
}

.text-link {
  color: #409eff;
  text-decoration: none;
  word-break: break-all;
}

.text-link:hover {
  text-decoration: underline;
}

.el-pagination {
  margin-top: 20px;
  justify-content: center;
}

.disable {
  color: #c4c6cc;
  pointer-events: none;
  user-select: none;
}

.top-user {
  width: 100px;
}

.fontSizeAdjust {
  font-size: 19px;
  font-weight: 700;
}

.fontSizeAdjust2 {
  margin-left: 5%;
  width: 75px;
  /* 设置合适的宽度 */
  overflow: hidden;
  /* 超出部分隐藏 */
  white-space: nowrap;
  /* 不换行 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}

//退出按钮
.logoutBtn {
  width: 80px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  font-weight: 700;
  border: 1px solid white;
  color: white;
  text-align: center;
  border-radius: 4px;
  margin-left: 40px;

  &:hover {
    color: black;
    background-color: white;
  }
}

.avue-topBar {
  &-right {
    display: flex;
    align-items: center;
    font-size: 16npx;
    font-weight: 400;
    color: #ffffff;

    &-count {
      font-size: 24npx;
      font-weight: 500;
      color: #ffffff;
      margin-right: 20px;
    }

    img {
      width: 32npx;
      height: 32npx;
      cursor: pointer;
    }
  }
}

.avue-topBar-right div:nth-child(2) {
  margin-right: 10npx;
}

.avue-topBar-right div:nth-child(3) {
  margin-left: 10npx;
  margin-right: 10npx;
}

.avue-topBar-right div:nth-child(4) {
  margin-right: 13npx;
}

.avue-topBar-right div:nth-child(5) {
  margin-right: 20npx;
}

.el-dropdown-link {
  color: #ffffff;
  display: flex;
}

/* 新增修复图片预览遮挡问题的样式 */
::v-deep .el-image-viewer__wrapper {
  z-index: 99999 !important;
}
.fa-play {
  transition: transform 0.3s ease; /* 添加过渡动画 */
}

.video-link:hover .fa-play{
  transform: scale(1.2);
}

</style>
