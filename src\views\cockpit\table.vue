<template>
  <div class="wel-table">
    <table style="table-layout: fixed; padding-right: 10px">
      <thead class="wel-table-thead">
        <tr>
          <th
            class="wel-table-thead-cell"
            v-for="items in column"
            :style="{
              width: items.width ? items.width + 'px' : 'auto',
              maxWidth: items.width ? items.width + 'px' : 'auto',
              textAlign: items.align,
            }"
            :key="items.dataIndex"
          >
            {{ items.title }}
          </th>
        </tr>
      </thead>
    </table>
    <div class="wel-table-scroll body-scrollbar">
      <table>
        <tbody class="wel-table-tabletbody">
          <!---->
          <tr
            data-row-key="2"
            class="wel-tabl-tabletbody-row"
            v-for="(tableItems, tableIndex) in dataSource"
          >
            <td
              v-for="items in column"
              :key="items.dataIndex"
              :style="{
                width: items.width ? items.width + 'px' : 'auto',
                maxWidth: items.width ? items.width + 'px' : 'auto',
                textAlign: items.align,
              }"
              class="wel-table-tabletbody-cell"
              :class="{ 'wel-table-tabletbody-cell-action': tableIndex % 2 === 0 }"
            >
              <slot v-if="items.slot" :name="items.dataIndex"></slot>
              <template v-else>
                {{ tableItems[items.dataIndex] }}
              </template>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="wel-table-scroll-bottom"></div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  emits: ["update:value"],
  props: {
    column: {
      type: Array,
      default: [],
    },
    dataSource: {
      type: Array,
      default: [],
    },
  },
  methods: {
    handleClick(val) {
      this.$emit("update:value", val.value);
    },
  },
};
</script>
<style lang="scss" scoped>
.body-scrollbar::-webkit-scrollbar-thumb {
  background: #07b667;
}
.wel-table {
  width: 100%;
  font-size: 14npx;
  color: #07b667;
  flex: 0.95;
  overflow: hidden;

  table {
    width: 100%;
  }

  &-scroll {
    overflow: auto scroll;
    height: 84%;
    // padding-right: 10px;

    &-bottom {
      height: 20npx;
    }
  }

  &-thead {
    &-cell {
      padding: 8npx 10npx;
      text-align: left;
      flex: 1;
      text-align: center;
    }
  }

  &-tabletbody {
    flex: 1;
    overflow-y: auto;

    &-cell {
      padding: 8npx 10npx;
      flex: 1;
      text-align: center;
      color: #ffffff;
      white-space: nowrap;
      word-break: keep-all;
      overflow: hidden;
      text-overflow: ellipsis;

      &-action {
        background: #323337;
        color: #bcbdc0;
      }

      &-row {
      }
    }
  }
}
</style>
