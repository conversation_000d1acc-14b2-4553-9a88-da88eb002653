<template>
  <div class="login-container" >
    <userLogin></userLogin>
    <img src="/img/bg.png" class="login-container-bagUrl" />

    <div class="login-container-title" >
      <img src="/img/logo1.jfif" />

    </div>
  </div>
</template>
<script>
import userLogin from "./userlogin.vue";
import { mapGetters } from "vuex";
import { validatenull } from "@/utils/validate";
import { getQueryString, getTopUrl } from "@/utils/util";
import website from "@/config/website";

export default {
  name: "login",
  components: {
    userLogin,
  },
  data() {
    return {
      website: website,
      time: "",
      activeName: "user",
      socialForm: {
        // tenantId: '000000',
        tenantId: "000000",
        source: "",
        code: "",
        state: "",
      },
    };
  },
  watch: {
    $route() {
      this.handleLogin();
    },
  },
  created() {
    this.handleLogin();
    this.getTime();
  },
  mounted() {},
  computed: {
    ...mapGetters(["tagWel"]),
  },
  props: [],
  methods: {
    getTime() {
      setInterval(() => {
        this.time = this.$dayjs().format("YYYY-MM-DD HH:mm:ss");
      }, 1000);
    },
    handleLogin() {
      const topUrl = getTopUrl();
      const redirectUrl = "/oauth/redirect/";
      const ssoCode = "?code=";
      this.socialForm.source = getQueryString("source");
      this.socialForm.code = getQueryString("code");
      this.socialForm.state = getQueryString("state");
      if (validatenull(this.socialForm.source) && topUrl.includes(redirectUrl)) {
        let source = topUrl.split("?")[0];
        source = source.split(redirectUrl)[1];
        this.socialForm.source = source;
      }
      if (
        topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.source) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: "第三方系统登录中,请稍后",
          background: "rgba(0, 0, 0, 0.7)",
        });
        this.$store
          .dispatch("LoginBySocial", this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(redirectUrl)[0];
            this.$router.push(this.tagWel);
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      } else if (
        !topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: "单点系统登录中,请稍后",
          background: "rgba(0, 0, 0, 0.7)",
        });
        this.$store
          .dispatch("LoginBySso", this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(ssoCode)[0];
            this.$router.push(this.tagWel);
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      }
    },
  },
};
</script>

<style lang="scss">
@import "@/styles/login.scss";
</style>
