import request from '@/axios';

/**
 * 文件流返回
 * @param url 接口地址
 * @param params 接口参数
 */
export const exportBlob = (url, params) => {
  return request({
    url: url,
    params: params,
    method: 'get',
    responseType: 'blob',
  });
};

// 获取设备故障数
export const getBaseDeviceinfoSumData = (params) => {
  return request({
    url: '/base/deviceinfo/getDeviceSumData',
    params: params,
    method: 'get'
  });
};


export const getBaseAlarminfoPage = (params) => {
  return request({
    url: '/base/alarminfo/page',
    params: params,
    method: 'get'
  });
};


export const getBaseProjectinfoExtinguishByDate = (params) => {
  return request({
    url: '/base/projectinfo/getExtinguishByDate',
    params: params,
    method: 'get'
  });
};

export const getBaseProjectinfoHighRiskMosquitores = (params) => {
  return request({
    url: '/base/projectinfo/gethighRiskMosquitoes',
    params: params,
    method: 'get'
  });
};


export const getBaseProjectinfoPage = (params) => {
  return request({
    url: '/base/projectinfo/page',
    params: params,
    method: 'get'
  });
};


export const getBladeSystrmDictBizDictBizData = (params) => {
  return request({
    url: '/blade-system/dict-biz/getDictBizData',
    params: params,
    method: 'get'
  });
};


