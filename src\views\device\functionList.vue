<template>
  <div>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
        :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave"
        @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
        @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
        <template #menu="{ size, row, index }">
            <el-button @click.stop="handleView(row.productId, row.id)" icon="el-icon-view" text type="primary" :size="size">查看</el-button>
            <el-button @click.stop="rowDel(row)" icon="el-icon-delete" text type="primary" :size="size">删除</el-button>
        </template>
    </avue-crud>
    <function-dialog ref="functionDialog" @result="refreshChange" />
  </div>
</template>

<script>
import { getList, getDetail, add, update, remove } from "@/api/device/tsl";
import functionDialog from "./functionDialog.vue"
import { mapGetters } from "vuex";
export default {
    components: { functionDialog },
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                searchShow: true,
                span: 24,
                dialogWidth: '40%',
                searchMenuSpan: 8,
                border: true,
                index: true,
                addBtn: false,
                viewBtn: false,
                editBtn: false,
                delBtn: false,
                selection: true,
                dialogClickModal: false,
                column: [
                    {
                        label: "功能类型",
                        prop: "tslType",
                        type: 'select',
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=productFuncType',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        rules: [{
                            required: true,
                            message: "请选择功能类型",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "功能名称",
                        prop: "tslName",
                        rules: [{
                            required: true,
                            message: "请输入功能名称",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "功能标识",
                        prop: "tslMark",
                        rules: [{
                            required: true,
                            message: "请输入功能标识",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "数据类型",
                        prop: "tslDataType",
                        type: 'select',
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=dataType',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        rules: [{
                            required: true,
                            message: "请输入功能标识",
                            trigger: "blur"
                        }]
                    },
                ]
            }
        }
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.dept_add, false),
                viewBtn: this.validData(this.permission.dept_view, false),
                delBtn: this.validData(this.permission.dept_delete, false),
                editBtn: this.validData(this.permission.dept_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        handleView(productId, id) {
            this.$refs.functionDialog.openDialog(productId, id);
        },
        rowSave(row, done, loading) {
            add(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            } else {
                this.form.nodeType = 1;
                this.form.netProtocol = 5;

            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
    }

}
</script>

<style>

</style>