<template>
  <Div class="container">
    <!-- 查看弹窗 -->
    <el-dialog
    class="Device-viewForm"
    :fullscreen="false"
    :show-close="true"
    align-center
    append-to-body
    v-model="viewModel"
    width="1000px"
    title="查看设备信息"
    @close="closeDialog"
    >
        <el-form :model="form" label-width="120px">
        <el-row :gutter="20">
            <el-col :span="12">
            <el-form-item label="设备名称">
                {{ viewData.deviceName }}
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="设备状态">
                {{ viewData.deviceStatusN }}
            </el-form-item>
            </el-col>
        </el-row>
        <Div class="tabsContent">
            <el-tabs
                v-model="activeName"
                type="card"
                class="device-tabs"
            >
                <el-tab-pane label="设备信息" name="first">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="设备编码">
                                {{ viewData.deviceCode }}
                            </el-form-item>
                            </el-col>
                            <el-col :span="12">
                            <el-form-item label="设备名称">
                                {{ viewData.deviceName }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="创建时间">
                                {{ viewData.createTime }}
                            </el-form-item>
                            </el-col>
                            <el-col :span="12">
                            <el-form-item label="sn码">
                                {{ viewData.deviceSn }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="sim卡号">
                                {{ viewData.simCode }}
                            </el-form-item>
                            </el-col>
                            <el-col :span="12">
                            <el-form-item label="工作模式">
                                {{ viewData.workModeName }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="当前状态">
                                {{ viewData.deviceStatusN }}
                            </el-form-item>
                            </el-col>
                            <el-col :span="12">
                            <el-form-item label="最后上线时间">
                                {{ viewData.lastTime }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="经纬度">
                                {{ viewData.longitude }}:{{ viewData.latitude }}
                                <el-link type="primary"><el-icon><LocationFilled /></el-icon></el-link>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-divider />
                    <div class="rowTitle">标签信息<Span class="ml-10"><el-link type="primary" @click="tagModel = true"><el-icon><Edit /></el-icon><span class="ml-5">编辑</span></el-link></Span></div>
                    <el-row :gutter="20">
                        <el-col :span="24">
                        <el-form-item label="设置标签">
                            <div class="flex gap-2">
                            <el-tag
                                v-for="item in viewData.labelLists"
                                :key="item.id"
                                type="info"
                                class="ml-5"
                                effect="light"
                                round
                                closable
                                @close="removeTag(item)"
                            >
                                {{ item.name }}
                            </el-tag>
                            </div>
                        </el-form-item>
                        </el-col>
                    </el-row>
                </el-tab-pane>
                <el-tab-pane label="设备数据" name="second">
                    <device-data ref="deviceData" />
                </el-tab-pane>
            </el-tabs>
        </Div>
        </el-form>
    </el-dialog>

    <!-- tag弹窗 -->
    <el-dialog
    class="Tag-viewForm"
    :fullscreen="false"
    :show-close="true"
    align-center
    append-to-body
    v-model="tagModel"
    width="400px"
    title="设置标签"
    @close="closeTagDialog"

    >
        <el-form label-width="60px">
            <el-form-item
                v-for="(item, index) in addTagValue"
                :key="item"
                :rules="{
                    required: true,
                    message: '必填',
                    trigger: 'blur',
                }"
                >
                <el-select ref="addTagRef" v-model="item.label" clearable>
                    <el-option
                        v-for="item in tagOptions"
                        :key="item.id"
                        :label="item.markName"
                        :value="item.markName"
                    />
                </el-select>
                <!-- <el-button class="ml-5" @click.prevent="removeTag(item)">删除</el-button> -->
            </el-form-item>
            <!-- <el-form-item>
                <span><el-link @click="addTag"><el-icon><CirclePlusFilled /></el-icon>新增标签</el-link></span>
            </el-form-item> -->
            <el-form-item>
                <span class="ml-10"><el-button type="primary" @click="confirmAddTag">确定</el-button></span>
                <span class="ml-5"><el-button type="info" @click="tagModel = false">取消</el-button></span>
            </el-form-item>
        </el-form>
    </el-dialog>
  </Div>
</template>

<script>
import deviceData from "./deviceData.vue";
import { getDetail } from "@/api/device/list";
import { getList as getTagList } from "@/api/identifying/list";
import { getDictionary } from "@/api/system/dictbiz";
export default {
    components: { deviceData },
    data() {
        return {
            viewModel: false,
            tagModel: false,
            viewData: {},
            deviceData: {},
            tagOptions: [],
            addTagValue: [{
                id: '',
                name: '',
                type: 'info'
            }],
            addTagVisible: false,
            tagOptions: [],
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            activeName: 'first',
        }
    },
    methods: {
        async handleView(row) {
            this.viewModel = true;
            const workModeList = await this.getDict('deviceWorkMode');
            this.loadTagList(3);
            if (row !== null) {
                await getDetail(row.id).then(res => {
                    this.viewData = res.data.data;
                    this.viewData.deviceStatusN = row.deviceStatusName;
                    if (row.deviceStatus !== "") {
                        this.viewData.workModeName = workModeList.filter(_item => _item.dictKey == row.deviceStatus)[0].dictValue;
                    }
                    this.viewModel = true;
                })
            }
            await this.$refs.deviceData.loadDeviceData({ devicesn: this.viewData.deviceSn, deviceName: row.deviceName });
        },
        closeDialog() {
            this.viewData = {};
            this.activeName = "first";
            this.viewModel = false;
        },
        closeTagDialog() {

        },
        async getDict(code) {
            let result = [];
            const query = {
                code: code
            }
            result = await getDictionary(query).then(res => {
                const data = res.data.data;
                return data;
            })
            return result;
        },
        loadTagList(type) {
            const query = {
                markType: type,
            }
            getTagList(1, 100, query).then(res => {
                const data = res.data.data;
                this.tagOptions = data.records;
            })
        }
    }
}
</script>

<style lang="scss" scoped>
    .ml-5 {
        margin-left: 5px;
    }
    .ml-10 {
        margin-left: 10px;
    }
    .Device-viewForm {
        .rowTitle {
            font-size: 18px;
            font-weight: bold;
            padding-left: 35px;
            padding-bottom: 20px;
        }
        .tabsContent {
            padding: 10px;
        }
        .el-divider--horizontal {
            margin-top: 0;
        }
    }

</style>