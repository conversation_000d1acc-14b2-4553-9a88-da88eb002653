<template>
    <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
            :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave"
            @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
            @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
            <template #navigation="{ row }">
                <el-button type="primary" text icon="el-icon-position" @click.stop="handleDesign(scope.row.name)">导航
                </el-button>
            </template>
            <template slot="menuLeft">
                <el-button type="danger" size="small" icon="el-icon-delete" plain v-if="permission.alarminfo_delete"
                    @click="handleDelete">删 除
                </el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>
  
<script>
import { getList, getDetail, add, update, remove } from "@/api/alarm/alarmcurrent";
import { mapGetters } from "vuex";

export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                searchShow: true,
                searchMenuSpan: 6,
                border: true,
                index: true,
                viewBtn: true,
                selection: false,
                menu: false,
                dialogClickModal: false,
                addBtn: false,
                excelBtn: true,
                column: [
                    {
                        label: "告警时间",
                        prop: "createTime",
                        rules: [{
                            required: true,
                            message: "请输入创建时间自动",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "告警名称",
                        prop: "alarmName",
                        rules: [{
                            required: true,
                            message: "请输入告警名称",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "项目名称",
                        prop: "projectId",
                        search: true,
                        type: 'select',
                        dicUrl: '/api/base/projectinfo/page',
                        dicFormatter: (res => {
                            return res.data.records;
                        }),
                        props: {
                            value: 'id',
                            label: 'projectName',
                        },
                        rules: [{
                            required: true,
                            message: "请输入项目名称",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "设备标识",
                        prop: "deviceId",
                        type: 'select',
                        dicUrl: '/api/base/deviceinfo/page',
                        dicFormatter: (res => {
                            return res.data.records;
                        }),
                        props: {
                            value: 'id',
                            label: 'deviceSn',
                        },
                        rules: [{
                            required: true,
                            message: "请输入标识id",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "告警等级",
                        prop: "alarmLevel",
                        type: 'select',
                        search: true,
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=alarmRuleLevel',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        dataType: 'number',
                        rules: [{
                            required: true,
                            message: "请输入告警等级",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "处理时间",
                        prop: "handlerTime",
                        rules: [{
                            required: true,
                            message: "请输入处理时间",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "处理结果",
                        prop: "alarmHandle",
                        type: 'select',
                        dicUrl: '/blade-system/dict-biz/getDictBizData?code=excuteWay',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey',
                        },
                        dataType: 'number',
                        rules: [{
                            required: true,
                            message: "请选择告警处理",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "处理人",
                        prop: "handler",
                        rules: [{
                            required: true,
                            message: "请输入处理人",
                            trigger: "blur"
                        }]
                    },
                    // {
                    //     label: "处理时间",
                    //     prop: "handlerTime",
                    //     rules: [{
                    //         required: true,
                    //         message: "请输入处理时间",
                    //         trigger: "blur"
                    //     }]
                    // },
                    // {
                    //     label: "忽略时长",
                    //     prop: "ignoreDuration",
                    //     type: 'select',
                    //     dicUrl: '/blade-system/dict-biz/getDictBizData?code=ignoreDuration',
                    //     props: {
                    //         label: 'dictValue',
                    //         value: 'dictKey',
                    //     },
                    //     dataType: 'number',
                    //     rules: [{
                    //         required: true,
                    //         message: "请输入忽略时长",
                    //         trigger: "blur"
                    //     }]
                    // },
                    {
                        label: "导航",
                        prop: "navigation",
                        slot: true,
                        width: 100,
                        rules: [{
                            required: true,
                            message: "请输入描述",
                            trigger: "blur"
                        }]
                    },
                ]
            },
            data: []
        };
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.dept_add, false),
                viewBtn: this.validData(this.permission.dept_view, false),
                delBtn: this.validData(this.permission.dept_delete, false),
                editBtn: this.validData(this.permission.dept_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        rowSave(row, done, loading) {
            add(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                done();
            }, error => {
                loading();
                console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query, { historyDataFlag: 3 })).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        }
    }
};
</script>
  
<style></style>
  