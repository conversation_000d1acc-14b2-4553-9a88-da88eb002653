<template>
  <basic-container>
    <el-form :inline="true" :model="queryForm" class="alarmap-haeder">
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="queryForm.deviceName" placeholder="请输入设备名称" clearable />
      </el-form-item>
      <el-form-item label="电池编号" prop="projectId">
        <el-input
          v-model="queryForm.deviceName2"
          placeholder="请输入电池编号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getDataList(page)">搜索</el-button>
      </el-form-item>
    </el-form>
    <div class="AlarmapContainer" id="AlarmapContainer"></div>
  </basic-container>
</template>

<script>
import { mapGetters } from "vuex";
import { AMapLoaderInitMap } from "@/utils/amap";
export default {
  data() {
    return {
      map: null,
      queryForm: {},
      dataAlarmap: [
        {
          label: "剩余电量",
          value: 11,
        },
        {
          label: "设备状态",
          value: 11,
        },
        {
          label: "循环次数",
          value: 11,
        },
        {
          label: "电池总电压",
          value: `1V`,
        },
        {
          label: "电流",
          value: `2A`,
        },
        {
          label: "剩余容量百分比",
          value: `3%`,
        },
        {
          label: "累积循环放电次数",
          value: `4次`,
        },
        {
          label: "预计可用时间",
          value: "5h",
        },
        {
          label: "系统软件版本",
          value: "1.0.0",
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },

  methods: {},
  mounted() {
    AMapLoaderInitMap({
      afterRequest: (AMap) => {
        this.map = new AMap.Map("AlarmapContainer", {
          //设置地图容器id
          viewMode: "3D", //是否为3D地图模式
          zoom: 5, //初始化地图级别
          center: [105.602725, 37.076636], //初始化地图中心点位置
        });

        let items = "";
        this.dataAlarmap.map((item) => {
          items += ` <div>${item.label}：<span>${item.value}</span></div>`;
        });

        let content = `
         <div class="MapInfoWindow-content"> 
            <div>BM5361111684</div>
            ${items}
        </div>
         `;

        let infoWindow = new AMap.InfoWindow({
          content: content,
          offset: new AMap.Pixel(20, -10),
        });

        let startIcon = new AMap.Icon({
          size: new AMap.Size(25, 34),
          image: "/img/货车.png",
          size: new AMap.Size(57.125, 25),
          imageSize: new AMap.Size(57.125, 25),
        });

        let marker = new AMap.Marker({
          map: this.map,
          position: [116.478935, 39.997761],
          icon: startIcon,
        });
        const markerClick = (e) => {
          infoWindow.open(this.map, e.target.getPosition());
        };

        marker.on("click", markerClick);
 
      },
    });
  },
};
</script>

<style lang="scss" scoped>
.alarmap-haeder {
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 2;
  background-color: #ffffff;
  padding-left: 15px;
  padding-top: 15px;
}
:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}

:global(#avue-view) {
  margin-bottom: 0px;
}
:global(.MapInfoWindow-content) {
  width: 300px;
  background-color: #ffffff;
}
.basic-container {
  padding: 0px !important;
  margin: 0px !important;
}

:deep(.basic-container__card) {
  height: 100%;
}

.AlarmapContainer {
  width: 100%;
  height: 100%;
}
</style>
