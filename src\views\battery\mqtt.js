export const MQTT_SERVICE = 'wss://emqx.sloctopus.com/mqtt' // mqtt服务地址
export const MQTT_USERNAME = 'admin' // mqtt连接用户名
export const MQTT_PASSWORD = 'w~l1tMIJ2$x' // mqtt连接密码
export const MQTT_CLIENTID = 'emqx_vue_' + Math.random().toString(16).substring(2, 8) // clientId


//测试地址
/*export const MQTT_SERVICE = 'ws://120.197.149.12:8083/mqtt'
export const MQTT_USERNAME = 'admin' // mqtt连接用户名
export const MQTT_PASSWORD = 'yywl@103!' // mqtt连接密码
export const MQTT_CLIENTID = 'emqx_vue_' + Math.random().toString(16).substring(2, 8) */
