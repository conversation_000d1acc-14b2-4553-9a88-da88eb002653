.avue-sidebar {
  width: $sidebar_width;
  height: 100%;
  user-select: none;
  position: relative;
  height: 100%;
  position: relative;
  background-color: #ffffff;
  transition: width .2s;
  box-sizing: border-box;
  box-shadow: 2px 0 6px rgba(0, 21, 41, .35);

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .avue-menu {
    height: calc(100% - #{$top_height});
  }

  .el-menu-item,
  .el-sub-menu__title {
    i {
      margin-right: 5px;
    }

    i,
    span {
      color: #666
    }

    &:hover {
      background: transparent;

      i,
      span {
        color: #07B667;
      }
    }

    &.is-active {
      background-color: #07B667;

      i,
      span {
        color: #fff;
      }
    }

    &.is-active:before {
      background-color: #07B667;
    }
  }

  &--tip {
    width: 90%;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    position: absolute;
    top: 5px;
    left: 5%;
    color: #ccc;
    z-index: 2;
    text-align: center;
    font-size: 14px;
    background-color: rgba(0, 0, 0, .4);
  }



}