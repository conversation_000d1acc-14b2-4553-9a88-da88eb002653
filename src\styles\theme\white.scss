.theme-white {
  .el-menu--popup {
    .el-menu-item {
      background-color: #fff;

      i,
      span {
        color: #666;
      }

      &:hover {

        i,
        span {
          color: #333;
        }
      }

      &.is-active {
        background-color: #07B667;

        &:before {
          content: '';
          top: 0;
          left: 0;
          bottom: 0;
          width: 4px;
          background: #07B667;
          position: absolute;
        }

        i,
        span {
          color: #333333;
        }
      }
    }
  }

  .avue-logo-img {
    width: 70%;
  }

  .avue-logo {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .avue-top,
  .avue-logo,
  .tags-container {
    background-color: #ffffff;
  }

  .avue-sidebar--tip {
    background-color: transparent;
    color: #333;
  }

  .el-dropdown {
    color: #333;
  }

  .avue-logo_title {
    font-weight: 400;
    color: #333;
  }

  .logo_title,
  .avue-breadcrumb {
    color: #333;

    i {
      color: #333;
    }
  }

  .avue-top {
    border: 1px solid rgba(0,0,0,0.08);

    .el-menu-item {

      i,
      span {
        color: #333;
      }

      &:hover {

        i,
        span {
          color: #333;
        }
      }
    }
  }

  .avue-sidebar {
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.15);
    background-color: #fff;

    .el-menu-item,
    .el-sub-menu__title {

      i,
      span {
        color: #666
      }

      &:hover {
        background: transparent;

        i,
        span {
          color: #333;
        }
      }

      &.is-active {
        background-color: #07B667;

        i,
        span {
          color: #333;
        }
      }

      &.is-active:before {
        background-color: #07B667;
      }
    }
  }

  .top-search {
    .el-input__inner {
      color: #333;
    }

    input::-webkit-input-placeholder,
    textarea::-webkit-input-placeholder {
      /* WebKit browsers */
      color: #333;
    }

    input:-moz-placeholder,
    textarea:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #333;
    }

    input::-moz-placeholder,
    textarea::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #333;
    }

    input:-ms-input-placeholder,
    textarea:-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: #333;
    }
  }

  .top-bar__item {
    i {
      color: #333;
    }
  }
}