<template>
  <basic-container>
    <el-row :gutter="12">
      <el-form :inline="true" :model="queryForm" class="queryForm">
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="queryForm.deviceName" placeholder="请输入设备名称" clearable />
        </el-form-item>
        <el-form-item label="设备名称" prop="projectId">
          <el-select
            v-model="queryForm.projectId"
            placeholder="所属项目"
            clearable
          >
            <el-option v-for="(item, index) in projectList" :key="item.id" :label="item.projectName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList(page)">搜索</el-button>
          <el-button type="info" @click="resetForm">清空</el-button>
          <el-button type="success" @click="inspect">一键自查</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-row :gutter="12">
    <el-col :span="8" v-for="(item, index) in dataList" :key="item.id">
      <el-card shadow="hover" :body-class="item.deviceStatus == 7 ? 'online' : 'offline'">
        <el-row :gutter="12">
          <el-col :span="18">
            <div class="r1">
              <span class="name">{{ item.deviceName }}</span>
              <span :class="item.deviceStatus == 7 ? 'xinhao normal' : 'xinhao abnormal'">信号强度：75%</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="item.deviceStatus == 7 || item.deviceStatus == 4" class="r2 online">{{ item.deviceStatusName }}</div>
            <div v-else class="r2 offline">{{ item.deviceStatusName }}</div>
          </el-col>
        </el-row>
        <div class="itemData">
          <el-row :gutter="12">
            <el-col :span="24"><span>所属项目</span><span class="val">{{ item.projectName }}</span></el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12"><span>所属产品</span><span class="val">{{ item.productName }}</span></el-col>
            <el-col :span="12"><span>SIM卡号</span><span class="val">{{ item.simCode }}</span></el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12"><span>灯管1状态</span><span class="val">在线</span></el-col>
            <el-col :span="12"><span>时长</span><span class="val">100小时</span></el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12"><span>灯管2状态</span><span class="val">在线</span></el-col>
            <el-col :span="12"><span>时长</span><span class="val">100小时</span></el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12"><span>高压包状态</span><span class="val">在线</span></el-col>
            <el-col :span="12"><span>时长</span><span class="val">100小时</span></el-col>
          </el-row>
        </div>
        <div class="footerbtn">
          <el-row :gutter="12">
            <el-col :span="8"><el-link type="info" @click="rowView(item)">查看</el-link></el-col>
            <el-col :span="8">
              <el-link type="info" v-if="item.deviceSwitch == 1" @click="rowUpdateStatus(item, 2)">禁用</el-link>
              <el-link type="info" v-else @click="rowUpdateStatus(item, 1)">启用</el-link>
            </el-col>
            <el-col :span="8"><el-link type="info" @click="rowDel(item)">删除</el-link></el-col>
          </el-row>
        </div>
      </el-card>
    </el-col>
  </el-row>
  <device-view ref="deviceView" />
    <div class="avue-crud__pagination">
      <el-pagination background layout="total, prev, pager, next" v-model="page.currentPage" 
      :page-size="page.pageSize" :total="page.total" @current-change="currentChange" @size-change="sizeChange" />
    </div>
  </basic-container>
</template>

<script>
import { getList, update, send } from "@/api/device/card";
import { getList as getProjectList} from "@/api/project/list";
import { getDictionary } from "@/api/system/dictbiz";
import deviceView from "./deviceView.vue"
import { mapGetters } from "vuex";
export default {
  components: { deviceView },
  data() {
    return {
      loading: false,
      dataList: [],
      projectList: [],
      queryForm: {},
      loading: false,
      page: {
          pageSize: 12,
          currentPage: 1,
          total: 0
      },
    }
  },
  created() {
    this.getDataList(this.page);
    this.loadProject();
  },
  methods: {
    async getDataList(page, params = {}) {
      this.loading = true;
      const statusList = await this.getDict('currentState');
      getList(page.currentPage, page.pageSize, Object.assign(params, this.queryForm)).then(res => {
        const data = res.data.data;
        this.dataList = data.records;
        this.page.total = data.total;
        this.dataList.forEach(_item => {
          if (_item.deviceStatus !== "") {
            _item.deviceStatusName = statusList.filter(res => res.dictKey == _item.deviceStatus)[0].dictValue;
          }
        })
        this.loading = false;
      })
    },
    inspect(row) {
      const loading = this.$loading(
        {
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        }
      );
      this.dataList.forEach((_item, index) => {
        const data = {
          deviceId: _item.id,
          sendType: "9",
          inputParameter: "1"
        }
        setTimeout(()=>{
          send(data).then(res => {
            loading.setText(_item.deviceName + " 自查完成...");
          })
          index == 11 ? loading.close() : console.log(index);
        }, 1000*index);
      })
      this.refreshChange();
    },
    rowUpdateStatus(row, v) {
      delete row.lastTime;
      const data = row;
      data.deviceSwitch = v;
      update(data).then(() => {
          this.getDataList(this.page);
          this.$message({
              type: "success",
              message: "操作成功!"
          });
      });
    },
    rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
        })
            .then(() => {
                return remove(row.id);
            })
            .then(() => {
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            });
    },
    rowView(item) {
      this.$refs.deviceView.handleView(item);
    },
    loadProject() {
      getProjectList().then(res=> {
        const data = res.data.data;
        this.projectList = data.records;
      })
    },
    currentChange(currentPage) {
        this.page.currentPage = currentPage;
        this.refreshChange();
    },
    sizeChange(pageSize) {
        this.page.pageSize = pageSize;
    },
    refreshChange() {
        this.getDataList(this.page, this.query);
    },
    resetForm() {
      this.queryForm.deviceName = "";
      this.queryForm.projectId = "";
    },
    async getDict(code) {
      let result = [];
      const query = {
          code: code
      }
      result = await getDictionary(query).then(res => {
          const data = res.data.data;
          return data;
      })
      return result;
    },
  }
}
</script>

<style lang="scss" scoped>
    .ml-5 {
        margin-left: 5px;
    }
    .ml-10 {
        margin-left: 10px;
    }
</style>
<style lang="scss">
    .el-card__body {
        position: relative;
        &.online{
            min-height: 276px;
            background-image: url('/public/img/cardbg1.png');
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 120% 120%;
        }
        &.offline{
            min-height: 276px;
            background-image: url('/public/img/cardbg2.png');
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 120% 120%;
        }
        .r1 {
          background: url('/public/img/141321.png');
          background-repeat: no-repeat;
          background-size: 45px;
          span {
            text-indent: 55px;
            display: block;
          }
          .name {
            font-size: 18px;
          }
          .xinhao {
            font-size: 14px;
            color: #979797;
            &.normal {
              background: url('/public/img/15312.png');
              background-repeat: no-repeat;
              background-position: 160px center;
              background-size: 20px;
            }
            &.abnormal {
              background: url('/public/img/653123.png');
              background-repeat: no-repeat;
              background-position: 160px center;
              background-size: 20px;
            }
          }
        }
        .r2 {
          &.online {
            position: relative;
            background: url('/public/img/15421.png') no-repeat center center;
            background-size: 100%;
            width: 126px;
            height: 36px;
            line-height: 36px;
            text-indent: 50px;
            color: #07B669;
            &::after {
              content: "";
              position: absolute;
              display: block;
              background: url('/public/img/15313.png') no-repeat center center;
              background-size: 100%;
              width: 16px;
              height: 16px;
              right: 20px;
              bottom: 10px;
            }
          }
          &.offline {
            position: relative;
            background: url('/public/img/15132.png') no-repeat center center;
            background-size: 100%;
            width: 126px;
            height: 36px;
            line-height: 36px;
            text-indent: 50px;
            color: #FF3939;
            &::after {
              content: "";
              position: absolute;
              display: block;
              background: url('/public/img/15121.png') no-repeat center center;
              background-size: 100%;
              width: 16px;
              height: 16px;
              right: 20px;
              bottom: 10px;
            }
          }
        }
        .itemData {
          margin-top: 20px;
          padding-left: 10px;
          font-size: 14px;
          line-height: 26px;
          display: table;
          width: 100%;
          span {
            display: table-cell;
            color: #999999;
            width: 90px;
            &.val {
              color: #333333;;
            }
          }
        }
        .footerbtn {
          position: absolute;
          background-color: #F4F5F8;
          width: 100%;
          left: 0;
          bottom: 0;
          padding-top: 5px;
          .el-col {
            text-align: center;
          }
        }
    }
</style>