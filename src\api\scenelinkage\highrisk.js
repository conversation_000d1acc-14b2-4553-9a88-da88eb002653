import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/base/highrisk/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/api/base/highrisk/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/base/highrisk/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/api/base/highrisk/save',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/base/highrisk/update',
        method: 'post',
        data: row
    })
}
