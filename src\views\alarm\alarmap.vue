<template>
    <basic-container>
        <div class="AlarmapContainer" id="AlarmapContainer"></div>
    </basic-container>
</template>
  
<script>
import { mapGetters } from "vuex";
import { AMapLoaderInitMap } from '@/utils/amap'
export default {
    data() {
        return {
            map: null
        }
    },
    computed: {
        ...mapGetters(["permission"]),
    },

    methods: {

    },
    mounted() {
        AMapLoaderInitMap({
            afterRequest: (AMap) => {
                this.map = new AMap.Map("AlarmapContainer", {  //设置地图容器id
                    viewMode: "3D",    //是否为3D地图模式
                    zoom: 5,           //初始化地图级别
                    center: [105.602725, 37.076636], //初始化地图中心点位置
                });
            }
        })

    }
};
</script>
  
<style lang="scss" scoped>
:deep(.el-card__body) {
    height: 100%;
    padding: 0px;
}

:global(#avue-view) {
    margin-bottom: 0px;
}


.basic-container {
    padding: 0px !important;
    margin: 0px !important;
}

:deep(.basic-container__card) {
    height: 100%;
}

.AlarmapContainer {
    width: 100%;
    height: 100%;
}
</style>
  