<template>
  <div class="container">
    <el-row :gutter="12">
        <el-col v-for="item in dataList" :key="item.key" :span="6">
            <el-card shadow="hover">
                <el-row :gutter="12"><el-col :span="16">{{ item.label }}</el-col><el-col :span="8"> <el-link type="info">查看数据</el-link></el-col></el-row>
                <el-row :gutter="12"><el-col :span="12">{{ item.value }}</el-col></el-row>
            </el-card>
        </el-col>
    </el-row>
  </div>
</template>

<script>
import { getDeviceData } from "@/api/device/list";
export default {
    data() {
        return {
            page: {
                pageSize: 999,
                currentPage: 1,
                total: 0
            },
            deviceData: {},
            dataList: [{
                key: 'uv1IData',
                label: '灯管1电流',
                value: 0,
                unit: '安'
            }, {
                key: 'uv1UData',
                label: '灯管1电压',
                value: 0,
                unit: 'V'
            }, {
                key: 'uv2IData',
                label: '灯管2电流',
                value: 0,
                unit: '安'
            }, {
                key: 'uv2UData',
                label: '灯管2电压',
                value: 0,
                unit: 'V'
            }, {
                key: 'hvidata',
                label: '高压包电流',
                value: 0,
                unit: '安'
            }, {
                key: 'hvudata',
                label: '高压包电压',
                value: 0,
                unit: 'V'
            }, {
                key: 'mddata',
                label: '灭蚊消杀数量',
                value: 0,
                unit: '个'
            }, {
                key: 'bvidata',
                label: '太阳能充电功率',
                value: 0,
                unit: 'KW'
            }, {
                key: '',
                label: '设备使用功率',
                value: 0,
                unit: 'KW'
            }, {
                key: '',
                label: '光伏电压',
                value: 0,
                unit: 'V'
            }, {
                key: '',
                label: '光伏电流',
                value: 0,
                unit: '安'
            }, {
                key: '',
                label: '温度',
                value: 0,
                unit: '°C'
            }, {
                key: '',
                label: '湿度',
                value: 0,
                unit: ''
            }, {
                key: '',
                label: '电池充电状态',
                value: 0,
                unit: ''
            }]
        }
    },
    methods: {
        loadDeviceData(query = { deviceName: '' }, page = this.page) {
            getDeviceData(page.currentPage, page.pageSize, Object.assign({}, query)).then(res => {
                const dataArr = res.data.data.records.filter(v => v.devicesn == query.devicesn)[0];
                if (dataArr) {
                    this.dataList.filter(v => v.key == "uv1IData")[0].value = dataArr.uv1IData;
                    this.dataList.filter(v => v.key == "uv1UData")[0].value = dataArr.uv1UData;
                    this.dataList.filter(v => v.key == "uv2IData")[0].value = dataArr.uv2IData;
                    this.dataList.filter(v => v.key == "uv2UData")[0].value = dataArr.uv2UData;
                    this.dataList.filter(v => v.key == "hvidata")[0].value = dataArr.hvidata;
                    this.dataList.filter(v => v.key == "hvudata")[0].value = dataArr.hvudata;
                    this.dataList.filter(v => v.key == "mddata")[0].value = dataArr.mddata;
                    this.dataList.filter(v => v.key == "bvidata")[0].value = dataArr.bvidata;
                }
            })
        }
    }
}
</script>

<style>

</style>