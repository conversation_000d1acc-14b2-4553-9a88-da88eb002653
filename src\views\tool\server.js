const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8099 });

// 存储客户端连接及最后活动时间，区分电脑和小程序
let computerClient = { ws: null, lastActive: 0 };
let miniProgramClient = { ws: null, lastActive: 0 };
const TIMEOUT_MS = 60000; // 超时时间60秒

console.log('WebSocket服务器已启动，监听端口8099');

// 定时检查超时连接（每10秒检查一次）
setInterval(() => {
    const now = Date.now();
    // 检查电脑客户端超时
    if (computerClient.ws && now - computerClient.lastActive > TIMEOUT_MS) {
        console.log('电脑客户端超时，断开连接');
        computerClient.ws.close(4008, '连接超时');
        computerClient.ws = null;
    }
    // 检查小程序客户端超时
    if (miniProgramClient.ws && now - miniProgramClient.lastActive > TIMEOUT_MS) {
        console.log('小程序客户端超时，断开连接');
        miniProgramClient.ws.close(4008, '连接超时');
        miniProgramClient.ws = null;
    }
}, 10000);

wss.on('connection', (ws) => {
    console.log('新客户端连接');
    let currentClient = null;

    // 监听客户端消息
    ws.on('message', (message) => {
        try {
            console.log(message)
            const data = JSON.parse(message.toString());
            console.log('收到消息:', data);

            // 更新活动时间（任何消息都视为活动）
            if (currentClient) {
                currentClient.lastActive = Date.now();
            }

            // 客户端身份认证
            if (data.type === 'auth') {
                if (data.role === 'computer') {
                    computerClient.ws = ws;
                    computerClient.lastActive = Date.now(); // 初始化活动时间
                    currentClient = computerClient;
                    console.log('电脑客户端已连接');
                    ws.send(JSON.stringify({
                        type: 'status',
                        message: '已成功连接到服务器',
                        status: 'connected'
                    }));
                } else if (data.role === 'miniProgram') {
                    miniProgramClient.ws = ws;
                    miniProgramClient.lastActive = Date.now(); // 初始化活动时间
                    currentClient = miniProgramClient;
                    console.log('小程序客户端已连接');
                    ws.send(JSON.stringify({
                        type: 'status',
                        message: '已成功连接到服务器',
                        status: 'connected'
                    }));
                }
                return;
            }

            // 处理心跳消息（客户端定期发送以维持连接）
            if (data.type === 'heartbeat') {
                // 回复心跳确认
                ws.send(JSON.stringify({
                    type: 'heartbeat_ack',
                    timestamp: new Date().getTime()
                }));
                return;
            }

            // 转发电脑发送的操作指令到小程序
            if (data.type === 'operation' && computerClient.ws === ws && miniProgramClient.ws) {
                console.log(`转发操作到小程序: ${JSON.stringify(data)}`);
                miniProgramClient.ws.send(JSON.stringify({
                    type: 'operation',
                    deviceNo: data.deviceNo,
                    command: data.data,
                    timestamp: new Date().toISOString()
                }));
                // 向电脑端确认消息已转发
                ws.send(JSON.stringify({
                    type: 'confirm',
                    message: `操作指令 "${data.data}" 已发送到小程序`,
                    timestamp: new Date().toISOString()
                }));
            }

            // 转发小程序发送的消息到电脑
            if (data.type === 'wx_message' && miniProgramClient.ws === ws && computerClient.ws) {
                console.log(`转发小程序消息到电脑: ${JSON.stringify(data)}`);
                computerClient.ws.send(JSON.stringify({
                    type: 'wx_message',
                    command: data.data,
                }));
                // 向小程序确认消息已转发
                ws.send(JSON.stringify({
                    type: 'confirm',
                    message: '消息已转发到电脑客户端',
                    timestamp: new Date().toISOString()
                }));
            }

        } catch (e) {
            console.error('消息解析失败:', e);
            // 发送错误提示给客户端
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息格式错误',
                timestamp: new Date().toISOString()
            }));
        }
    });

    // 监听连接关闭
    ws.on('close', () => {
        console.log('客户端断开连接');
        // 清除对应的客户端引用
        if (ws === computerClient.ws) {
            computerClient.ws = null;
            console.log('电脑客户端已断开');
        } else if (ws === miniProgramClient.ws) {
            miniProgramClient.ws = null;
            console.log('小程序客户端已断开');
        }
    });

    // 监听连接错误
    ws.on('error', (error) => {
        console.error('WebSocket连接错误:', error);
    });
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason, promise);
});
