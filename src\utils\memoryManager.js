// src/utils/memoryManager.js
export class MemoryManager {
    constructor(options = {}) {
        // 验证并初始化配置
        this._validateAndInitConfig(options);

        // 初始化内部状态
        this._initInternalState();

        // 初始化策略映射
        this._initStrategyMap();
    }

    // ==================== 公共API ====================

    /**
     * 启动内存监控
     */
    startMonitoring() {
        if (!this._isMemoryApiSupported()) {
            this.log('浏览器不支持内存监控API');
            return false;
        }

        // 避免重复启动
        if (this.timer) {
            this.log('内存监控已在运行中');
            return false;
        }

        this.log(`启动内存监控 - 阈值: ${this.options.threshold}MB, 检查间隔: ${this.options.checkInterval}ms`);

        // 立即执行一次检查
        this._checkMemory();

        // 设置定时器
        this.timer = setInterval(() => {
            this._checkMemory();
        }, this.options.checkInterval);

        return true;
    }

    /**
     * 停止内存监控
     */
    stopMonitoring() {
        if (!this.timer) {
            this.log('内存监控未启动');
            return;
        }

        clearInterval(this.timer);
        this.timer = null;
        this.log('已停止内存监控');
    }

    /**
     * 设置应用状态引用
     * @param {Object} state - 应用状态对象
     */
    setApplicationState(state) {
        if (!state || typeof state !== 'object') {
            this.log('无效的应用状态');
            return;
        }

        this.appState = state;
        this.log('已设置应用状态引用');
    }

    /**
     * 手动触发内存释放
     * @returns {Object} 释放结果
     */
    releaseMemory() {
        const result = {
            strategiesExecuted: 0,
            success: true,
            errors: []
        };

        this.log('开始手动释放内存');

        for (const strategy of this.options.strategies) {
            try {
                this._executeStrategy(strategy);
                result.strategiesExecuted++;
            } catch (error) {
                result.success = false;
                result.errors.push({
                    strategy,
                    error: error.message
                });
                this.log(`执行策略 ${strategy} 失败`, error);
            }
        }

        this.log('内存释放完成', result);
        return result;
    }

    /**
     * 获取当前内存信息
     * @returns {Object|null} 内存信息对象
     */
    getMemoryInfo() {
        if (!this._isMemoryApiSupported()) {
            return null;
        }

        const memoryInfo = performance.memory;
        const usedMemory = memoryInfo.usedJSHeapSize / (1024 * 1024);
        const totalMemory = memoryInfo.totalJSHeapSize / (1024 * 1024);
        const percent = Math.round((usedMemory / totalMemory) * 100);

        return {
            usedMB: parseFloat(usedMemory.toFixed(2)),
            totalMB: parseFloat(totalMemory.toFixed(2)),
            percent,
            isOverThreshold: usedMemory >= this.options.threshold,
            isWarning: usedMemory >= this.options.warningThreshold
        };
    }

    // ==================== 内部方法 ====================

    /**
     * 验证并初始化配置
     * @private
     */
    _validateAndInitConfig(options) {
        // 默认配置
        const defaultConfig = {
            threshold: 1500,
            checkInterval: 30000,
            warningThreshold: 1000,
            strategies: ['clearCache', 'clearDOM', 'gc', 'optimizeData'],
            onCheck: () => {},
            onThresholdExceeded: () => {},
            onWarning: () => {},
            enableLogging: true,
            maxReleaseAttempts: 3
        };

        // 验证并合并配置
        this.options = {
            ...defaultConfig,
            ...this._validateOptions(options)
        };
    }

    /**
     * 验证选项有效性
     * @private
     */
    _validateOptions(options) {
        const validOptions = {};

        if (typeof options.threshold === 'number' && options.threshold > 0) {
            validOptions.threshold = options.threshold;
        }

        if (typeof options.checkInterval === 'number' && options.checkInterval > 1000) {
            validOptions.checkInterval = options.checkInterval;
        }

        return validOptions;
    }

    /**
     * 初始化内部状态
     * @private
     */
    _initInternalState() {
        this.timer = null;
        this.lastMemoryUsage = 0;
        this.memoryTrend = [];
        this.isWarning = false;
        this.releaseAttempts = 0;
        this.appState = null;
    }

    /**
     * 初始化策略映射
     * @private
     */
    _initStrategyMap() {
        this._strategyMap = {
            clearCache: this._clearNonCriticalCache.bind(this),
            clearDOM: this._clearInactiveDOM.bind(this),
            gc: this._forceGarbageCollection.bind(this),
            optimizeData: this._optimizeApplicationData.bind(this)
        };
    }

    /**
     * 检查内存使用情况
     * @private
     */
    _checkMemory() {
        try {
            const memoryInfo = this.getMemoryInfo();
            if (!memoryInfo) return;

            this._updateMemoryTrend(memoryInfo.usedMB);
            const growthRate = this._calculateMemoryGrowthRate();
            this.lastMemoryUsage = memoryInfo.usedMB;

            // 触发回调
            this.options.onCheck({
                ...memoryInfo,
                growthRate
            });

            this.log(`内存使用: ${memoryInfo.usedMB.toFixed(2)}MB, 增长率: ${growthRate.toFixed(2)}%`);

            // 警告处理
            this._handleWarningState(memoryInfo);

            // 阈值处理
            this._handleThresholdState(memoryInfo);

        } catch (error) {
            this.log('内存检查出错', error);
        }
    }

    /**
     * 处理警告状态
     * @private
     */
    _handleWarningState(memoryInfo) {
        if (memoryInfo.isWarning && !this.isWarning) {
            this.isWarning = true;
            this.log(`警告: 内存使用接近阈值 (${memoryInfo.usedMB.toFixed(2)}MB)`);
            this.options.onWarning(memoryInfo);
        } else if (!memoryInfo.isWarning && this.isWarning) {
            this.isWarning = false;
            this.log(`内存使用恢复正常 (${memoryInfo.usedMB.toFixed(2)}MB)`);
        }
    }

    /**
     * 处理阈值状态
     * @private
     */
    _handleThresholdState(memoryInfo) {
        if (memoryInfo.isOverThreshold) {
            this.releaseAttempts++;
            this.log(`错误: 内存超限 (${memoryInfo.usedMB.toFixed(2)}MB), 尝试释放内存 (${this.releaseAttempts}/${this.options.maxReleaseAttempts})`);
            this.options.onThresholdExceeded({
                ...memoryInfo,
                attempts: this.releaseAttempts
            });

            this.releaseMemory();

            if (this.releaseAttempts >= this.options.maxReleaseAttempts) {
                this._notifyUser();
                this.releaseAttempts = 0;
            }
        }
    }

    /**
     * 执行特定策略
     * @private
     */
    _executeStrategy(strategy) {
        const strategyFn = this._strategyMap[strategy];
        if (typeof strategyFn === 'function') {
            strategyFn();
        } else {
            this.log(`未知策略: ${strategy}`);
        }
    }

    // ==================== 策略实现 ====================

    /**
     * 清除非关键缓存
     * @private
     */
    _clearNonCriticalCache() {
        try {
            // 清除应用缓存
            if (window.appCache?.update) {
                window.appCache.update();
                this.log('已更新应用缓存');
            }

            // 清除特定缓存项
            const cacheKeys = ['tempData', 'largeDataset', 'unnecessaryCache'];
            cacheKeys.forEach(key => {
                if (sessionStorage.getItem(key)) {
                    sessionStorage.removeItem(key);
                    this.log(`已清除缓存项: ${key}`);
                }
            });

        } catch (error) {
            this.log('清除缓存出错', error);
            throw error;
        }
    }

    /**
     * 清理非活动DOM元素
     * @private
     */
    _clearInactiveDOM() {
        try {
            const inactiveElements = document.querySelectorAll(
                '.inactive-component, .hidden-route, [data-role="temp-content"]'
            );

            inactiveElements.forEach(el => {
                el.parentNode?.removeChild(el);
            });

            if (inactiveElements.length > 0) {
                this.log(`已清理 ${inactiveElements.length} 个非活动DOM元素`);
            }

        } catch (error) {
            this.log('清理DOM出错', error);
            throw error;
        }
    }

    /**
     * 强制垃圾回收
     * @private
     */
    _forceGarbageCollection() {
        try {
            if (window.gc) {
                window.gc();
                this.log('已触发垃圾回收');
            } else {
                this.log('当前环境不支持手动GC');
            }
        } catch (error) {
            this.log('触发GC出错', error);
            throw error;
        }
    }

    /**
     * 优化应用数据
     * @private
     */
    _optimizeApplicationData() {
        try {
            // 清理定时器
            this._cleanupTimers();

            // 释放大型数据
            this._releaseLargeData();

            // 优化应用状态
            if (this.appState) {
                this._optimizeAppState();
            }

        } catch (error) {
            this.log('优化应用数据出错', error);
            throw error;
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 更新内存趋势
     * @private
     */
    _updateMemoryTrend(usedMemory) {
        this.memoryTrend.push(usedMemory);
        if (this.memoryTrend.length > 10) {
            this.memoryTrend.shift();
        }
    }

    /**
     * 计算内存增长率
     * @private
     */
    _calculateMemoryGrowthRate() {
        if (this.memoryTrend.length < 2) return 0;
        const first = this.memoryTrend[0];
        const last = this.memoryTrend[this.memoryTrend.length - 1];
        return ((last - first) / first) * 100;
    }

    /**
     * 检查内存API支持
     * @private
     */
    _isMemoryApiSupported() {
        return !!window.performance?.memory;
    }

    /**
     * 清理定时器
     * @private
     */
    _cleanupTimers() {
        if (window.appTimers?.length > 0) {
            window.appTimers.forEach(timer => {
                clearInterval(timer);
                clearTimeout(timer);
            });
            this.log('已清理应用定时器');
        }
    }

    /**
     * 释放大型数据
     * @private
     */
    _releaseLargeData() {
        const dataKeys = ['largeTempArray', 'largeTempObject', 'tempDataset'];
        dataKeys.forEach(key => {
            if (window[key]) {
                window[key] = null;
                this.log(`已释放大型数据: ${key}`);
            }
        });
    }

    /**
     * 优化应用状态
     * @private
     */
    _optimizeAppState() {
        // 清理历史数据
        if (Array.isArray(this.appState.history) && this.appState.history.length > 100) {
            this.appState.history = this.appState.history.slice(-50);
            this.log('已优化历史数据');
        }

        // 释放临时对象
        if (this.appState.largeTempObject) {
            this.appState.largeTempObject = null;
            this.log('已释放临时大对象');
        }
    }

    /**
     * 通知用户
     * @private
     */
    _notifyUser() {
        this.log('多次尝试释放内存失败，通知用户');
        // 使用 document.open 和 document.close 替换页面内容
        document.open();
        document.write('<html><head></head><body></body></html>');
        document.close();
        window.location.href = window.location.href;
    }

    /**
     * 记录日志（简化版）
     * @private
     */
    log(...args) {
        if (!this.options.enableLogging) return;
        console.log(`[MemoryManager]`, ...args);
    }
}

/**
 * 创建MemoryManager实例
 * @param {Object} options - 配置选项
 * @returns {MemoryManager} MemoryManager实例
 */
export function createMemoryManager(options = {}) {
    return new MemoryManager(options);
}
