<template>
  <basic-container>
    <avue-crud
        style="width: 100%;"
        :option="option"
        v-model:search="search"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
    >
      <!-- 插槽集 -->
      <template slot="menuLeft">
        <el-button
            type="warning"
            size="small"
            plain
            icon="el-icon-download"
            @click="handleExport"
        >导 出
        </el-button>
      </template>
      <template #menu="{ size, row, index }">
        <el-row style="justify-content: center;">
          <el-button
              @click.stop="handleView(row)"
              icon="el-icon-view"
              text
              type="primary"
              :size="size"
          >运行数据
          </el-button
          >
          <el-button
              @click.stop="handleTrajectory(row)"
              icon="el-icon-position"
              text
              type="primary"
              :size="size"
          >续费
          </el-button>
          <!-- 标记 -->
          <div @click="changeColor(row)" style="margin-left: 10px;margin-right: 1px;">
            <el-button :type="row.mark == 0 || row.mark == -1? 'default':'warning'" circle>
              <Star style="width: 1em; height: 1em;"/>
            </el-button>
          </div>
        </el-row>
      </template>
      <template #eleStatus="scope">
        {{ status[scope.row.eleStatus] }}
      </template>
      <template #search-menu="{row,size}">
        <el-button type="primary" @click="batVisble = true">批量处理</el-button>
        <el-button type="primary" @click="dialogVisible = true" icon="el-icon-open">快捷转移</el-button>
        <el-tooltip class="item" effect="dark" content="请先勾选设备框再进行设备转移" placement="top-start" :disabled="IsCheck">
          <el-button type="primary" @click="mvBatchEqu" icon="el-icon-share" :disabled="show">设备转移</el-button>
        </el-tooltip>
        <el-button type="primary" @click="opentransferDialog">设备转移记录</el-button>
        <el-button type="primary" @click="signEquip">标记设备</el-button>
        <div style="color: #ffffff;margin-left: 2%" v-if="userInfo.dept_id==='1123598813738675201'">{{
            customerTree
          }}
        </div>
      </template>
      <template #devicesn-search="{ disabled, size }">
        <avue-select
            @change="changeDevice"
            v-model="query.devicesn"
            placeholder="请选择 设备编号"
            type="tree"
            style="width: 90%"
            :filterable="true"
            :dic="devEquList"
            :span="5"
        ></avue-select>
      </template>
      <template #devicesn="scope">
        <div style="color:#ffffff;cursor:pointer" @click="BindingUserIsOpen(scope.row)">{{ scope.row.devicesn }}</div>
      </template>
      <template #isEnable-search="{ disabled, size }">
        <avue-select
            @change="changeDevice1"
            v-model="query.isEnable"
            placeholder="请选择 是否失效"
            type="tree"
            style="width: 90%"
            :filterable="true"
            :dic = "isEnableData"
            :span="5"
        ></avue-select>
      </template>
      <template #softVer-search="{ disabled, size }">
        <avue-select
            @change="changeDevice2"
            v-model="query.softVer"
            placeholder="请选择 软件版本"
            type="tree"
            style="width: 90%"
            :filterable="true"
            :dic = "softVerList"
            :span="5"
        ></avue-select>
      </template>
    </avue-crud>
    <!-- 弹窗集 -->
    <el-dialog title="转移记录" v-model="dialogTableVisible">
      <el-table
          :data="transferInfoData"
          stripe
          style="width: 100%">
        <el-table-column
            type="index"
            width="50">
        </el-table-column>
        <el-table-column
            width="115"
            prop="deviceEqu"
            label="转移设备编号">
        </el-table-column>
        <el-table-column
            prop="transferTime"
            label="转移时间">
        </el-table-column>
        <el-table-column
            prop="customerName"
            label="转移客户">
        </el-table-column>
        <el-table-column
            prop="operatorName"
            label="操作人">
        </el-table-column>
      </el-table>
      <div class="paginationOne">
        <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[5, 10, 15, 30]"
            :page-size="transferPage.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="transferPage.total">
        </el-pagination>
      </div>
    </el-dialog>
    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="treeVisble"
        width="600px"
        title="选择转移设备至供应商下">
      <avue-tree
          ref="userThree"
          :option="treeOption"
          :data="treeData"
          v-model="treeForm"
          @node-click="treeNodeClick">
      </avue-tree>
    </el-dialog>
    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="batVisble"
        width="600px"
        title="批量添加查询设备">
      <el-input
          v-model="search1.batSn"
          style="width: 100%"
          :rows="8"
          type="textarea"
          placeholder="请输入设备编号以换行进行批量查询"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="batchQuery()">查 询</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        @close="transferClose"
        title="设备转移"
        v-model="dialogVisible"
        width="565px">
      <el-transfer
          id="device-transfer"
          filterable
          v-model="transferValue"
          :data="transferData"
          style="color: #ffffff"
          :titles="['设备编号', '待转移设备']"
          :button-texts="['撤回', '转移']"
      ></el-transfer>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="sureTransfer">选择客户</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
        title="当前设备绑定用户"
        v-model="BindingUserOpen"
        width="700">
      <div class="BindingUserEquit">
        <!-- 设备编号容器使用 Flex 布局实现左右对齐 -->
        <div class="equipmentContent">
          <div class="title-wrapper"> <!-- 新增父容器用于布局 -->
            <span class="equipmentContentTitle">
          设备编号：{{ userBindDevicesn || '暂无用户使用' }}
        </span>
            <span style="width: 100px;font-size: 14px">层级：</span>
            <span class="customer-tree">{{ bindCustomerTree }}</span> <!-- 右侧内容 -->
          </div>
        </div>

        <el-table
            border
            :data="bindUser"
            style="width: 100%">
          <el-table-column
              align="center"
              label="用户名"
              prop="name">
          </el-table-column>
          <el-table-column
              align="center"
              label="车牌号"
              prop="carNo">
          </el-table-column>
          <el-table-column
              align="center"
              label="手机号"
              prop="phone">
          </el-table-column>
          <el-table-column prop="menu" label="操作" align="center">
            <template #="{ row }">
              <el-button type="primary" @click="UnbindDevice(row)" size="small">
                解绑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </basic-container>


</template>

<script>
import {getAssociationUserInfo, getBatterList, liftDeviceBind} from "@/api/battery/equipment";
import {getProvinceList} from '@/api/base/region.js';
import {validatenull} from '@/utils/validate';
import {getBladeSystemDeptThree, upBatterByDeptIds, upBatterInfo} from "@/api/battery/equipment.js";
import {
  devicelevelTree,
  getlastDevPage as getList,
  getTransferRecordPageAll,
  saveTransferRecord
} from "@/api/device/card";

import {mapGetters} from "vuex";
import { format } from "echarts";

export default {
  name: "wel",
  props: {
    params: {
      type: Object,
      default: () => ({key: null})
    },
    pathQuery: {
      type: Object,
      default: {}
    },
    userDeptNo: {
      type: String,
      default: {}
    }
  },

  data() {
    return {
      bindCustomerTree: null,
      userBindDevicesn: null,
      selectedUserName: null,
      selectedCarModel: null,
      selectedCarNo: null,
      selectedPhone: null,
      customerTree: null,
      transferInfoData: [],
      transferPage: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      BindingUserOpen: false,
      dialogTableVisible: false,
      search1: {
        batSn: null
      },
      search: {},
      transferValue: [],
      transferData: [],
      dialogVisible: false,
      IsCheck: false,
      form: {},
      query: {},
      stopOnLoad: false,
      // pathQuery: {
      //   province: this.pathQuery.province,
      //   batSn: this.pathQuery.batSn
      // },
      loading: true,
      batVisble: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      isEnableData:[
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 2,
        }
      ],
      softVerList:[],
      devEquList: [],
      selectionList: [],
      selectionListIds: [],
      option: {
        searchIndex: 5,
        searchIcon: true,
        menu: true,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 8,
        border: false,
        index: true,
        searchBtn: true,
        refreshBtn: true,
        columnBtn: true,
        viewBtn: false,
        emptyBtn: true,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        labelWidth: 140,
        indexLabel: "序号",
        indexWidth: 60,
        height: 500,
        expandFixed: false,
        align: "center",
        menuWidth: 240,
        column: [
          {
            label: "设备编号",
            prop: "devicesn",
            width: 140,
            overHidden: true,
            // change:((res) => {
            //   if (res.value === "") {
            //     this.query.devicesn = "";
            //     return
            //   }
            //   this.query.devicesn = res.value;
            //   this.page.currentPage = 1;
            //   this.debouncedSearch(this.page,this.query);
            // }),
            search: true,
            searchSpan: 5
          },
          {
            label: "设备状态",
            prop: "status",
            type: "select",
            searchSpan: 5,
            search: true,
            hide: false,
            filterable: true,
            change: (res) => {
              if (res.value === "") {
                this.query.status = "";
                return
              }
              if (this.stopOnLoad) {
                return;
              }
              this.page.currentPage = 1;
              this.query.status = res.value;
              this.onLoad(this.page, this.query);
            },
            dicData: [
              {label: "开机", value: "101"},
              {label: "放电", value: "2"},
              {label: "充电", value: "3"},
              {label: "待机", value: "4"},
              {label: "电池故障", value: "100"},
              {label: "关机", value: "8"},
              {label: "电池低电", value: "12 "},
              {label: "充电完成", value: "14"},
              {label: "离线", value: "-1"},
              {label: "在线", value: "-2"},
            ],
            formatter: function (row, column, cellValue) {
              const dicItem = this.dicData.find(item => item.value.includes(cellValue.toString()));
              return dicItem ? dicItem.label : cellValue;
            }
          },
          {
            label: "是否失效",
            prop: "isEnable",
            searchSpan: 5,
            search: true,
            type: "select",
            hide: true,
            dicData: [
              {
                label: "是",
                value: 1,
              },
              {
                label: "否",
                value: 2,
              },
            ],
          },
          {
            label: "软件版本",
            prop: "softVer",
            type: "select",
            searchSpan: 5,
            search: true,
            display: false,
            hide: true,
            filterable: true,
            minWidth: 80,
            props: {
              label: "label",
              value: "value",
            },
          },
          {
            label: "地区",
            prop: "province",
            type: "select",
            searchSpan: 4,
            search: true,
            display: false,
            overHidden: true,
            filterable: true,
            change: (res) => {
              if (res.value === "") {
                this.query.province = "";
                return
              }
              if (this.stopOnLoad) {
                return;
              }
              this.query.province = res.value;
              this.page.currentPage = 1;
              this.onLoad(this.page, this.query);
            },
            props: {
              label: "label",
              value: "value",
            },
            dicData: [],
          },
          {
            label: "剩余电量",
            prop: "resiEle",
            minWidth: 80,
          },
          {
            label: "循环次数",
            prop: "sumCycEle",
            minWidth: 80,
          },
          {
            label: "到期时间",
            prop: "timeEnable",
            minWidth: 80,
            width: 130,
            overHidden: true,
            formatter(row, value, column) {
              return value.split(" ")[0];
            }
          },
        ],
      },
      data: [],
      status: {
        0: "上电",
        1: "预充",
        2: "放电",
        3: "充电",
        4: "待机",
        5: "短路保护",
        6: "AFE异常",
        7: "预充次数超限",
        8: "关机",
        9: "NTC异常",
        10: "电池断线",
        11: "放电过流",
        12: "电池低电",
        13: "充电过流",
        14: "充电完成",
      },
      // 部门Id
      deptId: "",
      // 树形
      treeVisble: false,
      treeOption: {
        // defaultExpandAll: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        firstMeetId: null,
        defaultExpandedKeys: [],
        props: {
          label: "title",
          value: "id",
        },
      },
      timeCount: '',
      treeData: [],
      treeForm: {},
      treeId: "",
      treeIdList: [],
      equids: [],
      PROVINCE_SHORT_TO_FULL_MAP: {
        "新疆": "新疆维吾尔自治区",
        "西藏": "西藏自治区",
        "广西": "广西壮族自治区",
        "宁夏": "宁夏回族自治区",
        "内蒙古": "内蒙古自治区",
        "香港": "香港特别行政区",
        "澳门": "澳门特别行政区",
        "北京": "北京市",
        "天津": "天津市",
        "上海": "上海市",
        "重庆": "重庆市",
      },
      show: true,
      paramsStatus: null,
      bindUser: [],
      isFirstLoad: false,
    };
  },
  // created() {
  //   // 创建防抖函数，延迟2秒
  //   this.debouncedSearch = debounce(this.onLoad, 2000);
  // },
  beforeUnmount() {
    this.DataDestroyOrKeep("Destroy");
    // 组件销毁前取消未执行的防抖任务
    // this.debouncedSearch.cancel();
    this.findObject(this.option.column, 'devicesn').dicData = '';
    this.findObject(this.option.column, 'softVer').dicData = "";
    this.transferData = ''
    clearInterval(this.timeCount);
    // 清理防抖定时器
    if (this.loadDebounceTimer) {
      clearTimeout(this.loadDebounceTimer);
      this.loadDebounceTimer = null;
    }
    // 清理数据引用
    this.data = [];
    this.devEquList = [];
    this.transferData = [];
    this.selectionList = [];
    this.bindUser = [];
    this.treeData = [];
    this.transferInfoData = [];
    this.customerTree = null;
    this.pathQuery.province = '';
    // console.log("销毁掉了？")
  },
  mounted() {
    this.treeId = ""
    this.init()
    if (this.DataDestroyOrKeep("isKeep")) {
      this.DataDestroyOrKeep("Keep");
    } else {
      //this.onLoad(this.page);
      localStorage.removeItem('node');
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    search() {
      if (this.DataDestroyOrKeep('isKeep')) {
        var StorageQueryData = this.DataDestroyOrKeep('keepSelect').query;
        var selectData = {
          province: this.PROVINCE_SHORT_TO_FULL_MAP[StorageQueryData.province] ||
              (
                  StorageQueryData.province === undefined ? StorageQueryData.province :
                      StorageQueryData.province.includes("省") ? StorageQueryData.province : StorageQueryData.province + "省"
              ),
          isEnable: StorageQueryData.isEnable,
          softVer: StorageQueryData.softVer,
          status: StorageQueryData.status,
          devicesn: StorageQueryData.devicesn
        }
      }
      const selectData2 = {
        province: this.PROVINCE_SHORT_TO_FULL_MAP[this.pathQuery.province] ||
            (this.pathQuery.province === undefined ? this.pathQuery.province : this.pathQuery.province + "省"),
        batSn: this.pathQuery.batSn,
        status: this.pathQuery.status
      };
      return this.DataDestroyOrKeep('isKeep') ? selectData : selectData2
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    params: {
      handler(newVal, oldVal) {

        // 1. 确保参数变化时才执行
        if (newVal.key == undefined) {
          return;
        }
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) {
          return;
        }
        this.query.deptId = newVal.key;
        if (newVal.id != oldVal.id) {
          this.page.currentPage = 1;
        }
        this.query.devicesn = '';
        // 2. 准备查询参数
        const queryParams = {
          ...this.query, // 保留原有查询参数
          deptId: newVal.key ?? this.userDeptNo // 使用nullish合并运算符
        };

        // 3. 首次加载特殊处理
        if (this.isFirstLoad) {
          this.cleanRouteQueryParams(['province', 'batSn']);
          this.isFirstLoad = false;
        }

        if (oldVal.key !== null) {
          queryParams.status = null
          queryParams.province = null
          queryParams.isEnable = 2
          queryParams.softVer = null
          queryParams.batchSn = null

          this.query.batchSn = null
          this.pathQuery.status = null
          this.pathQuery.province = null

        }

        // 4. 发起数据加载
        this.loadDataWithDebounce(queryParams);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 初始化
    init() {
      // 获取设备
      this.getBatterListData();
      getProvinceList().then(res => {
        var data = res.data.data;
        var provList = [];
        data.forEach(dt => {
          provList.push(
              {
                label: dt.provinceName,
                value: dt.name
              }
          )
        })
        this.findObject(this.option.column, 'province').dicData = provList;
        if (!validatenull(this.pathQuery.province)) {
          this.search.province = this.pathQuery.province
          // this.onLoad(this.page)
        }
      })

    },
    /**
     * 清理路由查询参数
     * @param {Array} params 需要清理的参数名数组
     */
    cleanRouteQueryParams(params) {
      const newQuery = {...this.$route.query};
      params.forEach(param => delete newQuery[param]);

      this.$router.replace({
        path: this.$route.path,
        query: newQuery
      });
    },

    /**
     * 防抖加载数据
     * @param {Object} params 查询参数
     */
    loadDataWithDebounce(params) {
      if (this.loadDebounceTimer) {
        clearTimeout(this.loadDebounceTimer);
      }

      this.loadDebounceTimer = setTimeout(() => {
        this.onLoad(this.page, params);
        this.loadDebounceTimer = null;
      }, 300);
    },
    //解除设备绑定
    UnbindDevice(data) {
      let userId = data.id;
      liftDeviceBind({deviceNo: this.userBindDevicesn, userId: userId}).then(res => {
        if (res.data.data === true) {
          this.$message.success('操作成功！');
          this.BindingUserOpen = false
        } else {
          this.$message.error(res.data.msg);
        }
      })
    },
    changeColor(row) {
      row.mark == 0 ? row.mark = 1 : row.mark = 0
      upBatterInfo({mark: row.mark, id: row.id}).then((res) => {
        if (res.status == 200) {
          this.$message.success('更新成功');
        }
      })
    },
    signEquip() {
      this.query.mark = 1;
      this.onLoad(this.page, this.query);
    },
    getBatterListData(value) {
      getBatterList().then((res) => {
        let data = res.data.data;
        // 来料条码扫描
        let devEquList = []
        let softVerList = []
        data.forEach(item => {
          devEquList.push({label: item.equNo, value: item.equNo, devicesn: item.devicesn});
          this.transferData.push({label: item.equNo, key: item.id,})
          softVerList.push({label: item.softVer, value: item.softVer});
        });
        //去重
        const map = new Map();
        const uniqueDataList = softVerList.filter((obj) => {
          const key = obj.label;
          // 新增条件判断：label 的长度必须为 4
          if (key.length !== 4) {
            return false;
          }
          if (map.has(key)) {
            return false;
          } else {
            map.set(key, true);
            return true;
          }
        });
        this.devEquList = devEquList;
        this.softVerList = uniqueDataList;
        //this.findObject(this.option.column, 'devicesn').dicData = devEquList;
        //this.findObject(this.option.column, 'softVer').dicData = uniqueDataList;
        data = [];
        // 来料条码扫描
        devEquList = []
        softVerList = []
      });
    },
    //获取转移记录
    getTransferRecords() {
      getTransferRecordPageAll({
        pageNum: this.transferPage.pageNum,
        pageSize: this.transferPage.pageSize,
        operatorId: this.userInfo.user_id
      }).then(res => {
        this.transferPage.total = res.data.data.total
        this.transferInfoData = res.data.data.data
      })

    },
    DataDestroyOrKeep(type) {
      //销毁前保存
      if (type == "Destroy") {
        localStorage.setItem('StorageData', JSON.stringify({page: this.page, query: this.query}));
      }
      //重载时保持原有的状态
      if (type == "Keep") {
        const data = JSON.parse(localStorage.getItem('StorageData'))
        this.page = data.page;
        this.query = data.query;
        localStorage.removeItem('StorageData');
        this.onLoad(this.page, this.query);
        this.stopOnLoad = true;
      }
      //返回判断值
      if (type == "isKeep") {
        const isTrue = localStorage.getItem('StorageData') !== null;
        return isTrue;
      }
      //复原列表输入选择的数据
      if (type == 'keepSelect') {
        const data = JSON.parse(localStorage.getItem('StorageData'))
        const selectData = {
          page: data.page,
          query: data.query,
        }
        return selectData;
      }
    },
    opentransferDialog() {
      this.transferValue = []
      this.dialogTableVisible = true
      this.getTransferRecords();
    },
    handleSizeChange(val) {
      this.transferPage.pageSize = val
      this.getTransferRecords();
    },
    handleCurrentChange(val) {
      this.transferPage.pageNum = val
      this.getTransferRecords();
    },
    // chang input
    changeDevice(res) {
      if (res.value === "") {
        this.query.devicesn = "";
        this.customerTree = null
        return
      }
      if (res.value !== undefined && res.value !== null && res.value !== "") {
        devicelevelTree(res.value).then(res => {
          this.customerTree = "设备所在客户层级：" + res.data.data.join(" -> ")

        }) //设备所在客户层级
      }
      this.page.currentPage = 1;
      this.pathQuery.batSn = '';
      this.query.isEnable = null;//1
      this.query.softVer = null;//2
      let a = setTimeout(() => {
         this.$router.replace({path: '/equipment'});//0
         clearTimeout(a);
      },1000)
      this.query.devicesn = res.value;//0
      this.onLoad(this.page, this.query);//0
    },
    sureTransfer() {
      if (this.transferValue.length === 0) {
        this.$message.warning("至少转移一条数据");
        return;
      }
      this.mvBatchEqu()
    },
    changeDevice1 (res){
      if (res.value === "") {
        this.query.isEnable = "";
        return
      }
      if (this.stopOnLoad) {
        return;
      }
      this.query.isEnable = res.value;
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
    },
    changeDevice2 (res){
      if (res.value === "") {
        this.query.softVer = "";
        return
      }
      if (this.stopOnLoad) {
        return;
      }
      this.page.currentPage = 1;
      this.query.softVer = res.value;
      this.onLoad(this.page, this.query);
    },
    // 批量查询
    batchQuery() {
      if (validatenull(this.search1.batSn)) {
        return this.$message.warning('请输入条码');
      }
      var batchSn = this.search1.batSn.replace(/[\r\n]+/g, ',')
      this.query.batchSn = batchSn
      this.batVisble = false;
      this.search1.batSn = ""
      this.onLoad(this.page, this.query);
    },
    mvBatchEqu() {
      this.getTreeData()
      this.treeVisble = true;
    },

    //设备列被点击事件
    BindingUserIsOpen(row) {
      this.bindUser = []
      this.userBindDevicesn =null
      this.BindingUserOpen = true;
      getAssociationUserInfo(row.devicesn).then(res => {
        if (Object.keys(res.data.data).length !== 0) {
          this.bindUser = res.data.data
          this.userBindDevicesn = row.devicesn

        } else {
          this.bindUser = []
          this.bindUser.devicesn = row.devicesn
          this.$message.warning("当前设备暂时没有绑定用户信息！")
        }
      })

      devicelevelTree(row.devicesn).then(res => {
        this.bindCustomerTree = res.data.data.join("->")
      })
    },
    //设备转移关闭事件
    transferClose() {
      this.transferValue = []
    },
    getTreeData() {
      // this.treeData = this.regionList.data.data;
      getBladeSystemDeptThree().then((res) => {
        this.treeData = res.data.data;
        this.treeOption.defaultExpandedKeys.push(res.data.data[0].id);
        /* this.$nextTick(() => {
           this.treeOption.defaultExpandedKeys.push(this.treeData[0].id);
           this.$userThree.tree.setCurrentKey(this.treeData[0].key);
         })*/
      });
    },
    treeNodeClick(e) {

      this.$confirm("请确认转移设备到该供应商下面?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let form = {
          deviceEqu: this.ids === "" ? this.transferValue.join(",") : this.ids,
          customerName: e.title,
          operatorId: this.userInfo.user_id,
        }
        const batterIds = this.selectionList.map(ele => ele.id).join(',');
        upBatterByDeptIds(e.id, this.transferValue.length === 0 ? batterIds : this.transferValue.join(",")).then(res => {
          if (res.data.success === true) {
            this.treeVisble = false
            this.dialogVisible = false
            this.transferValue = []
            this.selectionList = []
            saveTransferRecord(form).then(res => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            })
          } else {
            this.$message.warning("操作不成功，请再试一下！")
          }
        })
      }).then(() => {
        this.onLoad(this.page);

      });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.pathQuery.batSn = '';
      this.$router.replace({path: '/equipment'});
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.paramsStatus = params
      if (this.query.devicesn !== undefined && this.query.devicesn !== null && this.query.devicesn !== "") {
        params.devicesn = this.query.devicesn
        devicelevelTree(this.query.devicesn).then(res => {
          this.customerTree = "设备所在客户层级：" + res.data.data.join(" -> ")

        }) //设备所在客户层级
      }
      this.query = params;
      this.page.currentPage = 1;
      this.pathQuery.province = null
      this.pathQuery.status = null


      this.onLoad(this.page, params);
      done();
    },
    //表格复选框选中
    selectionChange(list) {
      this.IsCheck = list.length > 0;
      this.show = list.length <= 0;
      this.selectionList = []
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page, this.query);
    },
    sizeChange(pageSize) {
      this.query = this.paramsStatus
      this.page.pageSize = pageSize;
      this.onLoad(this.page, this.query)
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    handleTrajectory(row) {
      /* this.$router.push({
         path: `/battery/location`,
         query: {devicesn: row.devicesn},
       });*/

      this.$parent.OpenEquipPay(row);
    },
    handleView(row) {
      localStorage.setItem('devicesn', row.devicesn);
      this.$router.push({path: `/battery/ibms`, query: {devicesn: row.devicesn}});
    },
    onLoad(page, params = {}) {

      if (this.stopOnLoad) return;
      const cleartimeout = setTimeout(() => {
        this.stopOnLoad = false;
        clearTimeout(cleartimeout);
      }, 1200)

      if (this.customerTree != null) {
        this.customerTree = null;
      }
      // 如果 paramsStatus 不为空，使用 paramsStatus 替换 params
      if (this.paramsStatus !== null) {
        params = this.paramsStatus;
      }
      this.loading = true;
      // 封装参数赋值逻辑的函数
      const assignParamIfValid = (paramName, value) => {
        if (!validatenull(value)) {
          params[paramName] = value;
        }
      };
      // 处理部门 ID 和部门名称
      assignParamIfValid('deptId', params.deptId || this.params.key);
      assignParamIfValid('deptName', params.deptName || this.params.deptName);

      // 处理省份名称
      // 这里省份处理不能同意统一按照prods数据处理，用户如果选择其他省份呢？
      if (!validatenull(this.pathQuery.province)) {
        const fullProvinceName = this.PROVINCE_SHORT_TO_FULL_MAP[this.pathQuery.province];
        params.province = fullProvinceName && (fullProvinceName.includes("市") || fullProvinceName.includes("自治区") || fullProvinceName.includes("特别行政区"))
            ? fullProvinceName
            : `${this.pathQuery.province?.trim() || ''}省`; // 防止空字符串或仅空格的情况
      }
      // 处理设备编号和状态
      assignParamIfValid('batchSn', this.pathQuery.batSn);
      assignParamIfValid('status', this.pathQuery.status);

      getList({current: page.currentPage, size: page.pageSize, ...params})
          .then((res) => {
            // const data = res.data.data;
            this.page.total = res.data.data.total;

            this.data = res.data.data.records;
            this.data.forEach(element => {
              if (element.status === 2) {
                element.province = "--";
                element.resiEle = "--";
              }
              if (element.softVer.length !== 4 || element.sumCycEle < 0) {
                element.softVer = "--";
                element.sumCycEle = "--";
              }
              if (element.timeEnable == "") {
                element.timeEnable = "--"
              }
              element.status = element.status === 1 ? element.eleStatus : -1;
              const targetStatus1 = [5, 6, 7, 9, 10, 11, 13, 15];
              const targetStatus2 = [0, 1];
              if (element.status !== "-1") {
                if (targetStatus1.includes(parseInt(element.status, 10))) {
                  element.status = 100;
                } else if (targetStatus2.includes(parseInt(element.status, 10))) {
                  element.status = 101;
                }
              }
            });

            this.loading = false;
          })
          .catch((error) => {
            console.error('请求数据失败:', error);
            this.loading = false;
          });
    },
    getEleStatusText(statusCode) {
      return this.status[statusCode] || '';
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-transfer__buttons) {
  padding: 0px 47px;
  width: 169px;
}

:deep(.el-transfer__button:nth-child(2)) {
  margin: 20px 0px 0px 0px;
}

/*:deep(.avue-crud .el-table__cell){
  background-color:transparent !important;
}*/
:deep(.el-table tr):hover {
  color: #d7d7d7;
}

.outTime {
  color: #ffffff;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.BindingUserEquit {
  height: 400px;
  width: 100%;
  border: 1px dashed #ccc;
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;

  .equipmentContent {
    margin: 11px;
    font-size: 16px;
    font-weight: 650;
    width: 95%;

  }

  .equipmentContentTitle {
    display: inline-block;
    width: 100%;
    line-height: 20px;
    overflow: hidden; /* 隐藏溢出的内容 */
    text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
    white-space: nowrap;
  }

  // &:hover{
  //   border: 1px solid #ccc;
  // }
}

.paginationOne {
  margin: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.Nubmer-viewForm {
  .dialogContent {
    padding: 20px;

    .title {
      font-size: 16px;
      padding-bottom: 5px;
    }

    .item {
      padding: 0px 0 0px 0;
      background-color: #F7F8FA;
      margin-bottom: 5px;

      .name {
        display: block;
        width: 100%;
        padding: 10px 10px;
      }
    }

    .btns {
      padding-top: 20px;
    }
  }

}

:deep(.el-tree-node__expand-icon) {
  color: rgb(114, 114, 114);
}

:deep(.el-tree-node__label) {
  color: rgb(114, 114, 114);
}

//avue菜单Slot插槽样式
@media only screen and (min-width: 992px) {
  :deep(.el-col-md-8) {
    display: flex;
    justify-content: left;
    max-width: 100%;
    flex: 0 0 100%;

  }
}

#device-transfer ::v-deep .el-input__inner {
  color: #000;
  font-size: 13px;
}

/* 设备编号容器样式 */
.equipmentContent {
  margin-bottom: 16px; /* 与下方表格保持间距 */
}

/* 新增的 Flex 父容器 */
.title-wrapper {
  display: flex; /* 启用 Flex 布局 */
  justify-content: space-between; /* 左右内容分别靠两端对齐 */
  align-items: center; /* 垂直居中对齐（可选，让文字对齐更美观） */
  width: 100%; /* 占满整个宽度，确保右侧内容能顶到最右边 */
}

/* 右侧内容的样式（可选，根据需求调整） */
.customer-tree {
  font-size: 13px;
  color: #797878; /* 与左侧文字颜色统一（参考 Element UI 默认样式） */
  /* 如需增加与左侧的距离，可添加 margin-left */
  /* margin-left: 10px; */
}
</style>
<style>
.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
  color: #ffffff;
}

.el-dialog__body {
  padding: 5px 15px;
}
</style>
