import * as echarts from 'echarts';
const labelStyle = {
  color: "#333333",
  fontSize: '14npx',
}
const initEchartsTotal = (relust) => {

  return {
    color: ["#59e7eb", "#38e27f", "#2f92ed", "#d0cc6f", "#ffea00"],
    tooltip: {},
    grid: {
      top: "5%",
      left: "1%",
      right: "1%",
      bottom: "5%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: true,
        axisLine: {
          //坐标轴轴线相关设置。数学上的x轴
          show: true,
          lineStyle: {
            color: "#E5E5E5",
          },
        },
        axisLabel: {
          //坐标轴刻度标签的相关设置
          textStyle: {
            ...labelStyle,
            margin: 20
          },
        },
        axisTick: {
          show: true,
        },
        data:  relust.map(item=>item.name),
      },
      {
        type: "category",
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        data: [],
      },
    ],
    yAxis: [
      {
        type: "value",
        nameTextStyle: {
          color: "#fff",
        },
        name: "货车/台",
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#E5E5E5",
          },
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#E5E5E5",
          },
        },
        axisLabel: {
          margin: 10,
          textStyle: {
            ...labelStyle,
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      
      {
        name: "设备省份数据汇总", //这个是Bar图
        type: "bar",
        barWidth: "28%",
        barGap: "220%",
        itemStyle: {
          normal: {
            barBorderRadius: [30, 30, 0, 0],
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#07B667", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#6BD8A9", // 100% 处的颜色
                },
              ],
            },
          },
        },
        data: relust.map(item=>item.num),
      }
    ],
  };
}
const initDensityEchats = (data, xAxis, densityValue) => {
  const colorList = ["#0EB96C", "#FFA71E"];
  return {
    backgroundColor: "#fff",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        label: {
          show: true,
          backgroundColor: "#fff",
          color: "#556677",
          borderColor: "rgba(0,0,0,0)",
          shadowColor: "rgba(0,0,0,0)",
          shadowOffsetY: 0,
        },
        lineStyle: {
          width: 0,
        },
      },
      backgroundColor: "#fff",
      textStyle: {
        color: "#5c6c7c",
      },
      padding: [10, 10],
      extraCssText: "box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)",
    },
    grid: {
      top: "5%",
      left: densityValue === 1 ? "7.5%" : "10%",
      right: "0%",
      bottom: "10%",
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: true,
        axisLine: {
          //坐标轴轴线相关设置。数学上的x轴
          show: true,
          lineStyle: {
            color: "#E5E5E5",
          },
        },
        axisLabel: {
          //坐标轴刻度标签的相关设置
          textStyle: {
            ...labelStyle,
            margin: 20
          },
        },
        axisTick: {
          show: true,
        },
        data: xAxis,
      },
    ],
    yAxis: [
      {
        type: "value",
        nameTextStyle: {
          color: "#fff",
        },
        name: "上报数据/条",
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#E5E5E5",
          },
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#E5E5E5",
          },
        },
        axisLabel: {
          margin: 10,
          textStyle: {
            ...labelStyle,
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: "上报数",
        type: "line",
        data: data,
        symbolSize: 1,
        symbol: "circle",
        smooth: true,
        yAxisIndex: 0,
        showSymbol: false,
        lineStyle: {
          width: 5,
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            {
              offset: 0,
              color: "#0EB96C",
            },
            {
              offset: 1,
              color: "#0EB96C",
            },
          ]),
          shadowColor: "rgba(158,135,255, 0.3)",
          shadowBlur: 10,
          shadowOffsetY: 20,
        },
        itemStyle: {
          normal: {
            color: colorList[0],
            borderColor: colorList[0],
          },
        },
      },
      // {
      //     name: "类目二",
      //     type: "line",
      //     data: [5, 12, 11, 14, 25, 16, 10, 10, 10, 30, 12, 15],
      //     symbolSize: 1,
      //     symbol: "circle",
      //     smooth: true,
      //     yAxisIndex: 0,
      //     showSymbol: false,
      //     lineStyle: {
      //         width: 5,
      //         color: new echarts.graphic.LinearGradient(1, 1, 0, 0, [
      //             {
      //                 offset: 0,
      //                 color: "#FFA71E",
      //             },
      //             {
      //                 offset: 1,
      //                 color: "#FFA71E",
      //             },
      //         ]),
      //         shadowColor: "rgba(115,221,255, 0.3)",
      //         shadowBlur: 10,
      //         shadowOffsetY: 20,
      //     },
      //     itemStyle: {
      //         normal: {
      //             color: colorList[1],
      //             borderColor: colorList[1],
      //         },
      //     },
      // },
    ],
  };
}

export {
  initEchartsTotal,
  initDensityEchats
}