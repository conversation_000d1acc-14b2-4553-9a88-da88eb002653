import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/base/devicekeep/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/base/devicekeep/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/base/devicekeep/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/base/devicekeep/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/base/devicekeep/submit',
    method: 'post',
    data: row
  })
}

