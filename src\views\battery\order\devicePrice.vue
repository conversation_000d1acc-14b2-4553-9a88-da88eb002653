<template>
  <div class="orderAll-container">
    <basic-container>
      <avue-crud
          style="width: 100%;"
          :data="tableData"
          :option="option"
          @row-update="rowUpdate"
          v-model:page="page">
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {getEquipmentYearPrice,saveDeviceMoney} from "@/api/pay/pay";

export default {
  name: "operateOrder",
  data() {
    return {
      tableData: [],
      //日志功能
      option: {
        delBtn:false,
        dialogWidth: '30%', // 设置弹窗宽度为 50%
        refreshBtn: false,
        columnBtn: false,
        addBtn: false,
        height:500,
        menuWidth: 200,
        //关闭菜单
        menu:false,
        index: true,
        indexLabel: '序列',
        indexWidth: 100,
        align: "center",
        column: [
          {
            label: '单价',
            prop: 'duration',
            editDisplay: false,

          }, {
            label: '金额',
            prop: 'money',
            span: 20,

          }, {
            label: '创建时间',
            prop: 'createTime',
            editDisplay: false,
            width:260,
          }, {
            label: '修改时间',
            prop: 'updateTime',
            editDisplay: false,
            width:260,
          }, {
            label: '操作人',
            prop: 'userId',
            editDisplay: false,
            formatter: function (row, value, column) {
              return "SLOC"
            },
           width:200,
          },
        ]
      },
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      form: {},
      isDestroyed: false,
    }
  },
  mounted() {
    this.isDestroyed = false;
    this.init();
  },
  beforeUnmount() {
    this.isDestroyed = true;
    this.tableData = [];
  },
  methods: {
    init() {
      this.onLoad();
    },
    //修改金额
    rowUpdate(form, index, done, loading){
      saveDeviceMoney(form).then(res=>{
        if (this.isDestroyed) return;
        done();
        this.onLoad()
      })
    },
    onLoad(){
      getEquipmentYearPrice().then(res => {
        if (this.isDestroyed) return;
        this.tableData = res.data.data
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.orderAll-container {
  width: 100%;
  color: #ffffff;
  background-color: #000;
  height: 82vh;
  border: 1px solid white;
  overflow-y: auto;
  overflow-x: hidden;

  &-right {
    width: 100%;
    height: 100%;

  }
}
</style>


