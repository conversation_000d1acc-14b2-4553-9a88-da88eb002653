<template>
  <div class="equipment-container">
    <div class="equipment-container-left battery-border">
      <div class="equipment-container-left-title" style="margin: 10px 0px">
        <div>客户列表</div>
        <!--        <el-button type="primary" @click="handleThreeAdd" icon="el-icon-plus">新增用户</el-button>-->
      </div>
      <avue-tree
          ref="userThree"
          :option="treeOption"
          :data="treeData"
          v-model="treeForm"
          :contextmenu="false"
          @del="threeDel"
          @update="threeEdit"
          @save="handleCreateUser"
          @node-click="treeNodeClick"
          :before-open="beforeOpen">
        <template #menu="{ node }">
          <div class="avue-tree__item" @click="threeModelEdit(node)">编 辑</div>
          <div class="avue-tree__item" @click="threeModelDel(node)">删 除</div>
          <div class="avue-tree__item" @click="threeModelesetPwd(node)" v-if="userInfo.dept_id==='1123598813738675201'">
            重置密码
          </div>
          <div class="avue-tree__item" @click="permAllocation(node)" v-if="userInfo.dept_id==='1123598813738675201'">
            权限分配
          </div>
        </template>
      </avue-tree>
    </div>
    <div class="equipment-container-right battery-border">
      <div class="equipment-container-right-users">
        客户名称：<span style="color: #b6b6b6"> {{ currentDeptInfo.deptName ? currentDeptInfo.deptName : '无' }}</span>
        联系方式：<span style="color: #b6b6b6"> {{ currentUserInfo.phone ? currentUserInfo.phone : '无' }}</span>
        联系人：<span style="color: #b6b6b6">{{ currentUserInfo.realName ? currentUserInfo.realName : '无' }}</span>
        位置：<span style="color: #b6b6b6">{{ currentUserInfo.detailAdr ? currentUserInfo.detailAdr : '无' }}</span>
        <span>功能：<a @click="OpenEquipPay">设备续费</a></span>
      </div>
      <carListView ref="carListView" :params="treeForm" :pathQuery="$route.query" :userDeptNo="userDeptId"/>
    </div>
    <el-dialog
        class="Nubmer-viewForm"
        :fullscreen="false"
        :show-close="true"
        align-center
        append-to-body
        v-model="paymentModel"
        width="600px"
        title="微信充值"
        @close="closeEvent"
    >
      <el-steps :active="active" finish-status="success" align-center>
        <el-step title="选择续费设备"></el-step>
        <el-step title="选择续费时长"></el-step>
        <el-step title="微信扫码支付"></el-step>
      </el-steps>
      <el-tabs v-model="activeName">
        <el-tab-pane>
          <div class="block">
            <div class="flex mb-3" style="margin-bottom:16px;">
              <span>剩余天数：</span>
              <el-input
                  id="inputColor"
                  v-model="equipInput"
                  clearable
                  style="width: 240px; margin:0 10px 0px 10px;"
                  placeholder="检索剩余天数设备"
              />
              <el-button type="primary" @click="residueDate">搜索</el-button>
            </div>
            <el-transfer
                filterable
                v-model="transferValue"
                :data="transferData"
                style="color: #ffffff"
                @change="equipmentTransfer"
                :titles="['临期30天设备', '待续费设备']"
                :button-texts="['撤回', '转移']"
            >
              <template #default="scope">
                <div :style="{color:scope.option.color}">
                  {{ scope.option.label + "    " + scope.option.text }}
                </div>
              </template>
            </el-transfer>
          </div>
        </el-tab-pane>
        <el-tab-pane>
          <div class="block">
            <div class="timeSelect">
              <div class="PayMoney" style="font-size: 15px">支付金额：<span style="color: red">{{ payMoneyReality }}元</span>
              </div>
              <div class="timeSelectList">
                <div class="timeSelectListNoActive"
                     v-for='index in 12' :key='index'
                     @click="timeSelect(index)">{{ index }}年
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane>
          <div class="block">
            <qrcode :value="qrCodeUrl" width="300" :options="qrCodeOptions"></qrcode>
          </div>
        </el-tab-pane>
        <el-tab-pane>
          <div class="paySucceed">
            <div>支付成功！</div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div style="display:flex;justify-content: right;">
        <el-button :disabled="!ReBackBtn" style="margin-top: 12px;" @click="reBack">上一步</el-button>
        <el-button :disabled="!NextStepBtn" style="margin-top: 12px;" @click="next">下一步</el-button>
      </div>
    </el-dialog>

    <el-dialog
        class="Nubmer-viewForm"
        v-model="TreeDialogVisible"
        width="600px"
        title="权限分配">
      <el-divider content-position="left">参数设置页面</el-divider>
      <el-checkbox-group v-model="checkList">
        <el-checkbox label="controlBtn" value="controlBtn" :disabled="userInfo.user_name.toUpperCase() === userName">
          参数设置页面权限
        </el-checkbox>
        <el-checkbox label="logPanel" value="logPanel" :disabled="userInfo.user_name.toUpperCase() === userName">
          参数设置操作记录
        </el-checkbox>
      </el-checkbox-group>

      <el-divider content-position="left">核心参数</el-divider>
      <el-checkbox
          :indeterminate="isIndeterminate1"
          v-model="checkAll1"
          @change="handleCheckAllChange('core')">
        全选
      </el-checkbox>
      <el-checkbox-group
          v-model="checkList1"
          @change="handleCheckedParamsChange('core')"
          class="core-params-group">

        <br>
        <el-checkbox
            v-for="param in coreParamsOptions"
            :key="param"
            :label="param"
        >
          {{ getParamLabel(param, 'core') }}
        </el-checkbox>
      </el-checkbox-group>

      <el-divider content-position="left">常用参数</el-divider>
      <el-checkbox
          :indeterminate="isIndeterminate2"
          v-model="checkAll2"
          @change="handleCheckAllChange('common')">
        全选
      </el-checkbox>
      <el-checkbox-group
          v-model="checkList2"
          @change="handleCheckedParamsChange('common')"
          class="common-params-group">
        <br>
        <el-checkbox
            v-for="param in commonParamsOptions"
            :key="param"
            :label="param"
        >
          {{ getParamLabel(param, 'common') }}
        </el-checkbox>
      </el-checkbox-group>
      <el-divider content-position="left">操作指令</el-divider>
      <el-checkbox
          :indeterminate="isIndeterminate3"
          v-model="checkAll3"
          @change="handleCheckAllChange('instructions')">
        全选
      </el-checkbox>

      <el-checkbox-group v-model="instructionsList" @change="handleCheckedParamsChange('instructions')">
        <el-checkbox
            v-for="instruction in instructionsOptions"
            :key="instruction"
            :label="instruction"
        >
          {{ instructionsLabels[instruction] || instruction }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="savePremSign">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
        v-model="isHintClose"
        width="500px"
        title="订单提示"
    >
      <div>订单已取消</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="closeHint">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import {
  deleteBladeSystemUserRemoveCustom,
  equipmentAdd,
  getBladeSystemDeptByDetai,
  getBladeSystemDeptThree,
  getUserByname,
  getUserInfoByDeptIdOrTitle,
  resetUserPwd,
  updateUserPremByDeptId,
} from "@/api/battery/equipment.js";
import carListView from "./carList.vue";
import PaymentView from "@/components/payment/payment.vue";
import RenewView from "@/components/payment/renew.vue";
import {validatenull} from 'utils/validate';
import md5 from "js-md5";
import {mapGetters} from "vuex";
import VueQrcode from "vue-qrcode";
import {getAdventDeviceByDeptId as getList} from "@/api/battery/equipment";
import {getWsQRcode, getWsPayOrder, getWsRefund, closeorder, getEquipmentYearPrice} from "@/api/pay/pay.js";

export default {
  components: {
    carListView,
    PaymentView,
    RenewView,
    qrcode: VueQrcode
  },
  data() {
    return {
      checkAll3: false,
      isIndeterminate3: false,
      instructionsOptions: [
        'shutdown',
        'strongStart',
        'autoHeatOn',
        'autoHeatOff',
        'reportData',
        'reportCCID',
        'quickReport'
      ],
      instructionsLabels: {
        'shutdown': '设备关机',
        'strongStart': '启动强启',
        'autoHeatOn': '开启自动加热',
        'autoHeatOff': '关闭自动加热',
        'reportData': '上报设置数据',
        'reportCCID': '上报CCID',
        'quickReport': '快速上报'
      },
      instructionsList: [],
      checkAll1: false,
      isIndeterminate1: false,
      checkList1: [],
      checkAll2: false,
      isIndeterminate2: false,
      checkList2: [],
      commonParamsOptions: [
        'Batterycapacity',
        'switch',
      ],
      commonParamLabels: {
        'Batterycapacity': '电池容量',
        'switch': '输出开关',
      },
      coreParamsOptions: [
        'Hargingprotectionvoltage',
        'Chargingrecoveryvoltage',
        'Dischargeprotectionvoltage',
        'Dischargerecoveryvoltage',
        'Dischargeshutdownvoltage',
        'MOStemperatureprotection',
        'MOStemperaturerecovery',
        'Batterytemperatureprotection',
        'Batterytemperaturerecovery',
        'Lowtemperatureprotectionforcharging',
        'Lowtemperaturechargingrecovery',
        'Lowtemperatureprotectionfordischarge',
        'Lowtemperaturedischargerecovery',
        'Heatingactivationtemperature',
        'Heatingstoptemperature',
        'Reportingintervaltime'
      ],
      paramLabels: {
        'Hargingprotectionvoltage': '充电保护电压',
        'Chargingrecoveryvoltage': '充电恢复电压',
        'Dischargeprotectionvoltage': '放电保护电压',
        'Dischargerecoveryvoltage': '放电恢复电压',
        'Dischargeshutdownvoltage': '放电关断电压',
        'MOStemperatureprotection': 'MOS温度保护',
        'MOStemperaturerecovery': 'MOS温度恢复',
        'Batterytemperatureprotection': '电池温度保护',
        'Batterytemperaturerecovery': '电池温度恢复',
        'Lowtemperatureprotectionforcharging': '充电低温保护',
        'Lowtemperaturechargingrecovery': '充电低温恢复',
        'Lowtemperatureprotectionfordischarge': '放电低温保护',
        'Lowtemperaturedischargerecovery': '放电低温恢复',
        'Heatingactivationtemperature': '加热开启温度',
        'Heatingstoptemperature': '加热停止温度',
        'Reportingintervaltime': '上报间隔时间'
      },
      userDeptId: null,
      deviceCount: {},
      currentUserInfo: {},
      currentDeptInfo: {},
      imgDataUrl: '',
      payMoney: 0,
      payMoneyReality: 0,
      active: 0,
      activeName: '0',
      ReBackBtn: false,
      IsCloseBtn: true,
      NextStepBtn: false,
      timeYear: 0,
      YearPrice: 0,
      dateSelect: null,
      equipInput: '',
      qrCodeUrl: '',
      qrCodeOptions: {
        width: 240,
        height: 240,
        color: {dark: '#000000ff', light: '#ffffffff'}
      },
      payinfo: {
        totalFee: "1",
        body: '设备续费',
        deviceNum: 1,
        paymentAmount: 1,
        deviceId: '1897570479349633026',
        remark: '无',
        userId: '1',
        renewTime: '1'
      },
      transferValue: [],
      transferData: [],
      payOptions: [],
      outTradeNo: '',
      premResult: null,
      checkList: [],
      TreeDialogVisible: false,
      renewModel: false,
      createUserModel: false,
      checkboxGroup1: ["全部"],
      cities: ["全部", "到期设备", "离线设备"],
      treeData: [],
      treeForm: {},
      paymentModel: false,
      isHintClose: false,
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        defaultExpandedKeys: [],
        props: {
          label: "title",
          value: "id",
        },
        formOption: {
          labelWidth: 140,
          column: [
            {
              label: "上级运营商",
              prop: "lastDeptName",
              readonly: true,
              row: true,
            },
            {
              label: "运营商名称",
              prop: "deptName",
              rules: [
                {
                  required: true,
                  message: "请输入运营商名称",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "登陆账号",
              prop: "account",
              span: 12,
              rules: [
                {
                  required: true,
                  message: "请输入登陆账号",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "登录密码",
              type: "password",
              prop: "password",
              rules: [
                {
                  required: true,
                  message: "请输入登录密码",
                  trigger: "blur"
                },
              ],
            },
            {
              label: "确认密码",
              type: "password",
              prop: "newPassword1",
              rules: [
                {
                  required: true,
                  message: "请输入确认密码",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "联系人名称",
              prop: "realName",
              rules: [
                {
                  required: true,
                  message: "请输入联系人",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "联系电话",
              prop: "phone",
            },
            {
              label: "身份证号码",
              prop: "idCard",
            },
            {
              label: "详细地址",
              prop: "detailAdr",
            },
            {
              label: "小程序备注",
              prop: "remark",
              type: "textarea",
              span: 24,
            },
          ],
        },
      },
      choiseId: null,
      choiseTitle: null,
      treeId: null,
      treeTitle: null,
      deptId: null,
      firstMeetId: null,
      shortcuts: [],
      userName: 'ADMIN', // 假设默认值，可根据实际情况修改
    };
  },
  computed: {
    ...mapGetters(['userInfo', 'permission'])
  },
  created() {
    if (!validatenull(this.$route.query.deptName)) {
      this.treeForm.deptName = this.$route.query.deptName
    }
    this.getSelectTime();
    this.getTreeData();
  },
  mounted() {
    this.init()
    let arr = this.userInfo.premSign.split(",")
    const result = arr.indexOf('permBtn') !== -1;
    this.premResult = result
  },
  methods: {
    init() {
      // this.findObject(this.treeOption.formOption.column, 'regionId').dicData = regionList.data;
    },
    setCurrentKey(node = {}, type) {
      //销毁前保存
      if (type == "Destroy") {
        localStorage.setItem('node', JSON.stringify(node));
      }
      //重载时保持原有的状态
      if (type == "Keep") {
        let node1 = JSON.parse(localStorage.getItem("node"))
        this.$refs.userThree.setCurrentNode(node1);
        this.getMinuteUserInfo(node1.value, node1.title)
      }
      //返回判断值
      if (type == "isKeep") {
        const isTrue = localStorage.getItem('node') !== null;
        return isTrue;
      }
    },
    getParamLabel(param, type) {
      const labels = type === 'core' ? this.paramLabels : this.commonParamLabels;
      return labels[param] || param;
    },
    handleCheckAllChange(type) {
      const options =
          type === 'core' ? this.coreParamsOptions :
              type === 'common' ? this.commonParamsOptions :
                  type === 'instructions' ? this.instructionsOptions : [];

      const checkList =
          type === 'core' ? this.checkList1 :
              type === 'common' ? this.checkList2 :
                  type === 'instructions' ? this.instructionsList : [];

      if (type === 'core') {
        this.checkList1 = checkList.length === options.length ? [] : [...options];
        this.isIndeterminate1 = false;
      } else if (type === 'common') {
        this.checkList2 = checkList.length === options.length ? [] : [...options];
        this.isIndeterminate2 = false;
      } else if (type === 'instructions') {
        this.instructionsList = checkList.length === options.length ? [] : [...options];
        this.isIndeterminate3 = false;
      }
    },

    handleCheckedParamsChange(type) {
      const options =
          type === 'core' ? this.coreParamsOptions :
              type === 'common' ? this.commonParamsOptions :
                  type === 'instructions' ? this.instructionsOptions : [];

      const checkList =
          type === 'core' ? this.checkList1 :
              type === 'common' ? this.checkList2 :
                  type === 'instructions' ? this.instructionsList : [];

      const checkedCount = checkList.length;
      const totalOptions = options.length;

      if (type === 'core') {
        this.checkAll1 = checkedCount === totalOptions;
        this.isIndeterminate1 = checkedCount > 0 && checkedCount < totalOptions;
      } else if (type === 'common') {
        this.checkAll2 = checkedCount === totalOptions;
        this.isIndeterminate2 = checkedCount > 0 && checkedCount < totalOptions;
      } else if (type === 'instructions') {
        this.checkAll3 = checkedCount === totalOptions;
        this.isIndeterminate3 = checkedCount > 0 && checkedCount < totalOptions;
      }
    },
    savePremSign() {
      const allSelectedPermissions = [
        ...this.checkList,
        ...this.checkList1,
        ...this.checkList2,
        ...this.instructionsList
      ];

      updateUserPremByDeptId({
        deptId: this.deptId,
        premSign: allSelectedPermissions.join(",")
      }).then(res => {
        this.$message.success("权限分配成功,重新登录后生效");
        this.TreeDialogVisible = false;
        this.checkList = [];
        this.checkList1 = [];
        this.checkList2 = [];
        this.instructionsList = [];
        this.checkAll1 = false;
        this.isIndeterminate1 = false;
        this.checkAll2 = false;
        this.isIndeterminate2 = false;
        this.checkAll3 = false;
        this.isIndeterminate3 = false;
      }).catch(error => {
        this.$message.error("权限分配失败");
      });
    },
    change(value) {
    },
    closeHint() {
      this.isHintClose = false;
    },
    next() {
      this.active++
      if (this.active === 2) {
        this.NextStepBtn = false;
        this.ReBackBtn = false;
        let arrTostring = ""
        this.transferValue.forEach((element, index) => {
          if (index == 0) {
            arrTostring = element;
          } else {
            arrTostring = arrTostring + "," + element;
          }
        })
        this.payinfo = {
          totalFee: this.payMoney,
          body: '设备续费',
          deviceNum: this.transferValue.length,
          paymentAmount: this.payMoney,
          deviceId: arrTostring,
          userId: this.userInfo.user_id,
          renewTime: this.timeYear
        }
        getWsQRcode(this.payinfo).then((res) => {
          this.qrCodeUrl = res.data.QrCode;
          this.outTradeNo = res.data.out_trade_no
        }).then(() => {
          const getPayStatus = setInterval(() => {
            getWsPayOrder({outTradeNo: this.outTradeNo}).then((res) => {
              if (this.paymentModel === false) {
                clearInterval(getPayStatus);
                closeorder({outTradeNo: this.outTradeNo}).then(res => {
                });
                this.isHintClose = true;
              }
              if (res.data.msg === "支付成功") {
                clearInterval(getPayStatus);
                this.active = 3;
                this.activeName = this.active.toString();
              }
            });
          }, 3000);
        })
      }
      if (this.active == 1) {
        this.ReBackBtn = true;
        this.NextStepBtn = false;
      }
      this.activeName = this.active.toString();
    },
    reBack() {
      this.active--;
      if (this.active == 0 || this.active == 2) {
        this.ReBackBtn = false;
      }
      if (this.active <= 2) {
        this.NextStepBtn = true;
        const a = document.querySelector(".timeSelectList")
        for (var i = 0; i < a.children.length; i++) {
          a.children[i].className = "timeSelectListNoActive";
        }
        this.payMoney = 0;
        this.payMoneyReality = 0;
      }
      this.activeName = this.active.toString();
    },
    closeEvent() {
      this.active = 0
      this.activeName = this.active.toString();
      this.ReBackBtn = false;
      this.IsCloseBtn = true;
      this.NextStepBtn = false;
      this.transferValue = [];
      const a = document.querySelector(".timeSelectList")
      for (var i = 0; i < a.children.length; i++) {
        a.children[i].className = "timeSelectListNoActive";
      }
    },
    OpenEquipPay(data) {
      this.paymentModel = true;
      if (data?.id) {
        this.equipmentTransfer(data.id)
        const hasDuplicateId = this.transferData.some(item => item.key === data.id);
        if (!hasDuplicateId) {
          this.transferData.push({label: data.equNo, key: data.id, text: ""})
        }
        this.transferValue.push(data.id)
      }
      getEquipmentYearPrice().then((res) => {
        this.YearPrice = res.data.data[0].money;
      })
    },
    timeSelect(index) {
      this.timeYear = index.toString();
      this.payMoney = this.YearPrice * this.transferValue.length * index;
      this.payMoneyReality = this.YearPrice * this.transferValue.length * index;
      const a = document.querySelector(".timeSelectList")
      if (a.children[index - 1].getAttribute("class") === "timeSelectListActive") {
        a.children[index - 1].className = "timeSelectListNoActive";
        this.payMoney = 0;
        this.payMoneyReality = 0;
        this.NextStepBtn = false;
        return;
      }
      for (var i = 0; i < a.children.length; i++) {
        a.children[i].className = "timeSelectListNoActive";
      }
      a.children[index - 1].className = "timeSelectListActive"
      this.NextStepBtn = true;
    },
    equipmentTransfer(value) {
      this.payMoney = 0;
      this.payMoneyReality = 0;
      if (value.length == 0) {
        this.NextStepBtn = false;
      } else {
        this.NextStepBtn = true;
      }
    },
    permAllocation(node) {
      if (node.data.title === 'SLOC') {
        this.$message.warning("SLOC账号默认为最高权限,暂时无法调整！");
        return;
      }
      getUserByname({deptId: node.data.id}).then(res => {
        if (res.data.data.premSign) {
          const allPermissions = res.data.data.premSign.split(',');

          // 分离不同类型的权限
          this.checkList = allPermissions.filter(perm =>
              !this.coreParamsOptions.includes(perm) &&
              !this.commonParamsOptions.includes(perm) &&
              !this.instructionsOptions.includes(perm)
          );

          this.checkList1 = allPermissions.filter(perm =>
              this.coreParamsOptions.includes(perm)
          );

          this.checkList2 = allPermissions.filter(perm =>
              this.commonParamsOptions.includes(perm)
          );

          // 单独处理操作指令
          this.instructionsList = allPermissions.filter(perm =>
              this.instructionsOptions.includes(perm)
          );

          // 初始化全选状态
          this.handleCheckedParamsChange('core');
          this.handleCheckedParamsChange('common');
          this.handleCheckedParamsChange('instructions');
        } else {
          this.checkList = [];
          this.checkList1 = [];
          this.checkList2 = [];
          this.instructionsList = [];
          this.checkAll1 = false;
          this.isIndeterminate1 = false;
          this.checkAll2 = false;
          this.isIndeterminate2 = false;
          this.checkAll3 = false;
          this.isIndeterminate3 = false;
        }
      });
      this.deptId = node.data.id;
      this.TreeDialogVisible = true;
    },
    beforeOpen(done, type) {
      if (['view', 'edit'].includes(type)) {
        // 查看和编辑逻辑
      } else {
        //新增逻辑
        this.treeForm.parentId = this.treeId
        this.treeForm.lastDeptName = this.treeTitle
      }
      done();
    },
    threeDel(parent, data, done, loading) {
      deleteBladeSystemUserRemoveCustom({id: parent.data.id}).then((res) => {
        this.$message.success("删除成功");
        this.getTreeData();
        done();
      });
    },
    handleThreeAdd() {
      if (validatenull(this.treeId)) {
        return this.$message.warning("请选择添加的上级机构");
      }
      this.findObject(this.treeOption.formOption.column, "lastDeptName").display = true;
      this.$refs.userThree.parentAdd();
      const password = this.findObject(this.treeOption.formOption.column, "password");
      const newPassword1 = this.findObject(
          this.treeOption.formOption.column,
          "newPassword1"
      );
      password.rules = [
        {
          required: true,
          message: "请输入登录密码",
          trigger: "blur",
        },
      ];
      newPassword1.rules = [
        {
          required: true,
          message: "请输入登录密码",
          trigger: "blur",
        },
      ];
    },
    threeEdit(parent, data, done, loading) {
      let params = {...data};
      if (params.password) {
        if (params.password !== params.newPassword1) {
          this.$message.warning("两次密码输入不一致");
          loading();
          return;
        }
        params.password = md5(params.password);
      } else {
        delete params.password;
      }
      if (!validatenull(params.bgImgList)) {
        params.bgImg = params.bgImgList.join(',')
      }
      equipmentAdd({...params})
          .then((res) => {
            this.$message.success("修改成功!");
            this.getTreeData();
            done();
          })
          .catch((err) => {
            loading();
          });
    },
    threeModelEdit(data) {
      if (data.data.title === 'SLOC') {
        this.$message.warning("无法编辑sloc");
        return;
      }
      let result = data.data;
      getBladeSystemDeptByDetai({id: result.id}).then((res) => {
        let colunmPassword = this.findObject(
            this.treeOption.formOption.column,
            "password"
        );
        let colunmNewPassword1 = this.findObject(
            this.treeOption.formOption.column,
            "newPassword1"
        );
        colunmPassword.rules = [];
        colunmNewPassword1.rules = [];
        var data = res.data.data;
        let treeForm = data;
        this.treeForm = treeForm;
        this.$refs.userThree.rowEdit(treeForm);
      });
    },
    threeModelDel(data) {
      deleteBladeSystemUserRemoveCustom({id: data.data.id}).then((res) => {
        this.$message.success("删除成功");
        this.getTreeData();
        done();
      });
    },
    threeModelesetPwd(data) {
      let name = data.data.title;
      this.$confirm('此操作将重置用户为 "' + name + '" 的密码,重置后默认密码为:123456, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resetUserPwd({id: data.data.id, password: md5('123456')}).then(res => {
          this.$message({
            type: 'success',
            message: res.data.msg
          });
        })
      }).catch(() => {

      });
    },
    getTreeData() {
      getBladeSystemDeptThree().then((res) => {
        this.treeData = res.data.data;
        this.treeOption.defaultExpandedKeys.push(res.data.data[0].id);
        this.selectFirstNode()
        // 获取第一个节点的 ID
        const firstNodeId = this.treeData[0].id;

        // 使用模板字符串动态构建选择器
        const selector = `[data-key="${firstNodeId}"]`;

        // 等待 DOM 渲染完成后查找节点
        this.$nextTick(() => {
          const firstNodeEl = document.querySelector(selector);

          if (firstNodeEl) {
            // 触发真实点击，确保样式和状态更新
            if (!this.setCurrentKey({}, "isKeep")) {
              firstNodeEl.click();
            } else {
              this.setCurrentKey({}, "Keep");
            }
          } else {
            console.warn(`未找到 data-key="${firstNodeId}" 的节点`);
          }
        });
      });
    },
    selectFirstNode() {

    },
    treeNodeClick(node) {
      this.setCurrentKey(node, "Destroy");
      // localStorage.removeItem('StorageData');
      let nodeData = localStorage.getItem("nodeData");
      console.log(nodeData)
      if (nodeData === null) {
        localStorage.setItem("nodeData", node.id);
      } else {
        if (node.id !== nodeData) {
          this.$router.replace({path: '/equipment'});
        }
      }
      this.treeForm = node;
      this.userDeptId = node.id
      this.treeId = node.id
      this.treeTitle = node.title
      this.getBetterListData(node.id, 30);
      this.getMinuteUserInfo(node.value, node.title)
    },

    getMinuteUserInfo(value, title) {
      getUserInfoByDeptIdOrTitle(value, title).then(res => {
        this.treeForm.title = res.data.data.dept.deptName
        this.currentDeptInfo = res.data.data.dept
        this.currentUserInfo = res.data.data.user
        this.deviceCount.sumTotal = res.data.data.count
        this.deviceCount.expire = res.data.data.expire
      })
    },
    getBetterListData(Id, Day) {
      getList({deptId: Id, Days: Day}).then((res) => {
        var data = res.data.data;
        this.transferData = [];
        let arrayA = [];
        let arrayB = [];
        data.forEach(item => {
          let Date1 = new Date(item.timeEnable);
          let Date2 = new Date(Date());
          const timeDifference = this.getDaysDifference(Date1, Date2);
          if (timeDifference <= 7 && timeDifference > 0) {
            arrayA.push({label: item.equNo, key: item.id, text: timeDifference + "天", color: "red"})
          } else if (timeDifference >= 7 && timeDifference <= 30) {
            arrayA.push({label: item.equNo, key: item.id, text: timeDifference + "天", color: "orange"})
          } else if (timeDifference < 0) {
            arrayB.push({label: item.equNo, key: item.id, text: "过期", color: "rgb(163, 162, 162)"})
          } else {
            this.transferData.push({label: item.equNo, key: item.id, text: timeDifference + "天"})
          }
        });
        this.transferData.unshift(...arrayB);
        this.transferData.unshift(...arrayA);
      });
    },
    getDaysDifference(date1, date2) {
      const normalizedDate1 = new Date(date1.getFullYear(), date1.getMonth(), date1.getDate());
      const normalizedDate2 = new Date(date2.getFullYear(), date2.getMonth(), date2.getDate());
      const millisecondsPerDay = 86400000;
      const diffInMilliseconds = normalizedDate1 - normalizedDate2;
      const daysDifference = diffInMilliseconds / millisecondsPerDay;
      return daysDifference;
    },
    residueDate() {
      let day = this.equipInput;
      if (this.equipInput === "") {
        day = 30;
      }
      this.getBetterListData(this.treeId, day);
    },
    getSelectTime() {
      let array = []
      for (let i = 1; i <= 10; i++) {
        let obj = {
          text: i + '年',
          value: () => {
            const end = new Date()
            const start = new Date()
            start.setFullYear(start.getFullYear() + i)
            return [end, start]
          }
        }
        array.push(obj);
      }
      this.shortcuts = array;
    },
    handleCreateUser(parent, data, done, loading) {
      data.id = ""
      let params = data;
      if (params.password !== params.newPassword1) {
        this.$message.warning("两次密码输入不一致");
        loading();
        return;
      }
      if (!validatenull(params.bgImgList)) {
        params.bgImg = params.bgImgList.join(',')
      }
      equipmentAdd({...params, password: md5(params.password)})
          .then((res) => {
            this.$message.success("创建成功!");
            this.getTreeData();
            done();
          })
          .catch((err) => {
            loading();
          });
    },
    findObject(arr, prop) {
      return arr.find(item => item.prop === prop);
    }
  },
};
</script>
<style lang="scss" scoped>
.el-transfer-panel {
  width: 208px;
}

.loading {
  width: 100%;
  height: 40px;
  color: rgb(163, 162, 162);
}

.backgroundPng {
  width: 194px;
  height: 194px;
  background-image: url(/public/img/weixin_zhifu.png);
  background-position: center;
  background-size: 100% 100%;
}

.timeSelectList {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
  margin-top: 30px;
}

.timeSelectListNoActive {
  width: 100px;
  margin: 10px;
  height: 70px;
  line-height: 70px;
  border: 1px solid black;
  text-align: center;
  border-radius: 5px;
  font-size: 15px;

  &:hover {
    background: black;
    color: white;
  }
}

.timeSelectListActive {
  width: 100px;
  margin: 10px;
  height: 70px;
  line-height: 70px;
  border: 1px solid black;
  text-align: center;
  border-radius: 5px;
  background: black;
  color: white;
}

:deep(.el-transfer__buttons) {
  padding: 0px 28px;
  width: 130px;
}

:deep(.el-transfer__button:nth-child(2)) {
  margin: 20px 0px 0px 0px;
}

:deep(.el-date-editor .el-range-input, .el-date-editor .el-range-separator) {
  color: black;
}

.block {
  text-align: center;
  height: 385px;
}

.paySucceed {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 37px;
  font-weight: 600;
  height: 347px;
}

.block .demonstration {
  display: block;
  font-size: 14px;
  margin-bottom: 20px;
  margin-right: 10px;
}

.timeSelect {
  width: 100%;
  height: 100%;
  padding-bottom: 15%;
}

.equipment-container {
  width: 100%;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 5px;

  &-left {
    width: 20%;
    height: 100%;
    padding: 5px;

    &-title {
      display: flex;
      justify-content: space-between;
    }
  }

  &-right {
    width: 79.5%;
    height: 100%;
    overflow-y: auto;

    &-users {
      color: #ffffff;
      font-size: 16px;
      padding: 15px 15px 0px 20px;

      span {
        margin-right: 50px;
      }

      a {
        color: #409eff;
        cursor: pointer;
      }
    }
  }
}

.Nubmer-viewForm {
  .dialogContent {
    padding: 20px;

    .title {
      font-size: 16px;
      padding-bottom: 5px;
    }

    .item {
      padding: 0px 0 0px 0;
      background-color: #F7F8FA;
      margin-bottom: 5px;

      .name {
        display: block;
        width: 100%;
        padding: 10px 10px;
      }
    }

    .btns {
      padding-top: 20px;
    }
  }
}

:deep(.avue-tree__content) {
  padding: 5px 0;
  height: calc(100% - 132px);
}

.equipment-container-right-users {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.equipment-container-right-users span {
  margin-right: 10px;
}

.el-divider--horizontal {
  margin: 15px 0;
}

:deep(.el-input__inner) {
  color: #ffffff
}

.el-input__inner {
  color: #ffffff;
}

</style>
<style>
#inputColor {
  color: #000;
}
</style>
