import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-report/report/rest/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const remove = ids => {
  return request({
    url: '/blade-report/report/rest/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};


export const getBladeWeatherList = (params) => {
  return request({
    url: '/blade-weather/weather/getList',
    method: 'get',
    params: params
  });

}
export const getBaseProjectInfoOtherData = (params) => {
  return request({
    url: '/base/projectinfo/getProjectOtherData',
    method: 'get',
    params: params
  });

}



